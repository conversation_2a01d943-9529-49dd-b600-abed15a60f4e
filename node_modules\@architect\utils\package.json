{"name": "@architect/utils", "version": "4.0.6", "description": "Common utility functions", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/architect/utils.git"}, "scripts": {"test:nolint": "npm run test:unit:updater && npm run test:unit", "test": "npm run lint && npm run test:unit:updater && npm run test:unit", "test:unit": "cross-env tape 'test/**/*-test.js' | tap-arc", "test:unit:updater": "cross-env tape test/updater/test.js | tap-arc", "lint": "npx eslint . --fix", "rc": "npm version prerelease --preid RC"}, "engines": {"node": ">=16"}, "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "dependencies": {"@aws-lite/client": "^0.21.1", "chalk": "4.1.2", "glob": "~10.3.12", "path-sort": "~0.1.0", "restore-cursor": "3.1.0", "run-series": "~1.1.9", "run-waterfall": "~1.1.7", "sha": "~3.0.0"}, "devDependencies": {"@architect/eslint-config": "~3.0.0", "@architect/inventory": "~4.0.4", "cross-env": "~7.0.3", "eslint": "~9.1.1", "proxyquire": "~2.1.3", "sinon": "~17.0.1", "tap-arc": "1.1.0", "tape": "~5.7.5"}, "eslintConfig": {"extends": "@architect/eslint-config"}}