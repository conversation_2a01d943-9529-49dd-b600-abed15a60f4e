var y=e=>{throw TypeError(e)};var h=(e,s,t)=>s.has(e)||y("Cannot "+t);var r=(e,s,t)=>(h(e,s,"read from private field"),t?t.call(e):s.get(e)),n=(e,s,t)=>s.has(e)?y("Cannot add the same private member more than once"):s instanceof WeakSet?s.add(e):s.set(e,t),u=(e,s,t,a)=>(h(e,s,"write to private field"),a?a.call(e,t):s.set(e,t),t),f=(e,s,t)=>(h(e,s,"access private method"),t);import K from"@mux/mux-data-google-ima";import{MuxVideoBaseElement as S,Attributes as _}from"@mux/mux-video/base";import{CastableMediaMixin as M}from"castable-video/castable-mixin.js";import{MediaTracksMixin as R}from"media-tracks";var i=class{addEventListener(){}removeEventListener(){}dispatchEvent(s){return!0}};if(typeof DocumentFragment=="undefined"){class e extends i{}globalThis.DocumentFragment=e}var m=class extends i{},g=class extends i{},v={get(e){},define(e,s,t){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(m)}},l,x=class{constructor(s,t={}){n(this,l);u(this,l,t==null?void 0:t.detail)}get detail(){return r(this,l)}initCustomEvent(){}};l=new WeakMap;function k(e,s){return new m}var D={document:{createElement:k},DocumentFragment,customElements:v,CustomEvent:x,EventTarget:i,HTMLElement:m,HTMLVideoElement:g},E=typeof window=="undefined"||typeof globalThis.customElements=="undefined",b=E?D:globalThis,F=E?D.document:globalThis.document;import{AdsVideoMixin as B,Attributes as w,Events as A}from"@mux/mux-video/ads/mixin";export*from"@mux/mux-video/base";var W={..._,...w},d,o,C,T,p,c=class extends M(R(B(S))){constructor(){super(...arguments);n(this,o);n(this,d,!1);n(this,p)}handleEvent(t){super.handleEvent(t),t.type===A.AD_BREAK_START?f(this,o,C).call(this):t.type===A.AD_BREAK_END&&f(this,o,T).call(this)}get muxDataSDK(){return K}get muxDataSDKOptions(){return{imaAdsLoader:this.adsLoader}}set muxDataKeepSession(t){u(this,d,!!t)}get muxDataKeepSession(){return r(this,d)}get autoplay(){let t=this.getAttribute("autoplay");return t===null?!1:t===""?!0:t}set autoplay(t){let a=this.autoplay;t!==a&&(t?this.setAttribute("autoplay",typeof t=="string"?t:""):this.removeAttribute("autoplay"))}get muxCastCustomData(){return{mux:{playbackId:this.playbackId,minResolution:this.minResolution,maxResolution:this.maxResolution,renditionOrder:this.renditionOrder,customDomain:this.customDomain,tokens:{drm:this.drmToken},envKey:this.envKey,metadata:this.metadata,disableCookies:this.disableCookies,disableTracking:this.disableTracking,beaconCollectionDomain:this.beaconCollectionDomain,startTime:this.startTime,preferCmcd:this.preferCmcd}}}get castCustomData(){var t;return(t=r(this,p))!=null?t:this.muxCastCustomData}set castCustomData(t){u(this,p,t)}};d=new WeakMap,o=new WeakSet,C=function(){var t,a;(t=this.ad)!=null&&t.isLinear()&&(a=this.ad)!=null&&a.isCustomPlaybackUsed()&&(this.muxDataKeepSession=!0,this.unload(),this.muxDataKeepSession=!1)},T=function(){var t,a;(t=this.ad)!=null&&t.isLinear()&&(a=this.ad)!=null&&a.isCustomPlaybackUsed()&&(this.muxDataKeepSession=!0,this.load(),this.muxDataKeepSession=!1)},p=new WeakMap;b.customElements.get("mux-video")||b.customElements.define("mux-video",c);var X=c;export{W as Attributes,X as default};
//# sourceMappingURL=index.mjs.map
