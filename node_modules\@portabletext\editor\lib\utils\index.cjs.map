{"version": 3, "file": "index.cjs", "sources": ["../../src/utils/util.is-equal-selections.ts", "../../src/utils/util.reverse-selection.ts", "../../src/utils/util.split-text-block.ts"], "sourcesContent": ["import type {EditorSelection} from '../types/editor'\nimport {isEqualSelectionPoints} from './util.is-equal-selection-points'\n\n/**\n * @public\n */\nexport function isEqualSelections(a: EditorSelection, b: EditorSelection) {\n  if (!a && !b) {\n    return true\n  }\n\n  if (!a || !b) {\n    return false\n  }\n\n  return (\n    isEqualSelectionPoints(a.anchor, b.anchor) &&\n    isEqualSelectionPoints(a.focus, b.focus)\n  )\n}\n", "import type {EditorSelection} from '../types/editor'\n\n/**\n * @public\n */\nexport function reverseSelection<\n  TEditorSelection extends NonNullable<EditorSelection> | null,\n>(selection: TEditorSelection): TEditorSelection {\n  if (!selection) {\n    return selection\n  }\n\n  if (selection.backward) {\n    return {\n      anchor: selection.focus,\n      focus: selection.anchor,\n      backward: false,\n    } as TEditorSelection\n  }\n\n  return {\n    anchor: selection.focus,\n    focus: selection.anchor,\n    backward: true,\n  } as TEditorSelection\n}\n", "import type {PortableTextTextBlock} from '@sanity/types'\nimport type {EditorSelectionPoint} from '..'\nimport type {EditorContext} from '../editor/editor-snapshot'\nimport {isSpan} from './util.is-span'\nimport {sliceTextBlock} from './util.slice-text-block'\n\n/**\n * @beta\n */\nexport function splitTextBlock({\n  context,\n  block,\n  point,\n}: {\n  context: Pick<EditorContext, 'schema'>\n  block: PortableTextTextBlock\n  point: EditorSelectionPoint\n}): {before: PortableTextTextBlock; after: PortableTextTextBlock} | undefined {\n  const firstChild = block.children.at(0)\n  const lastChild = block.children.at(block.children.length - 1)\n\n  if (!firstChild || !lastChild) {\n    return undefined\n  }\n\n  const before = sliceTextBlock({\n    context: {\n      schema: context.schema,\n      selection: {\n        anchor: {\n          path: [{_key: block._key}, 'children', {_key: firstChild._key}],\n          offset: 0,\n        },\n        focus: point,\n      },\n    },\n    block,\n  })\n  const after = sliceTextBlock({\n    context: {\n      schema: context.schema,\n      selection: {\n        anchor: point,\n        focus: {\n          path: [{_key: block._key}, 'children', {_key: lastChild._key}],\n          offset: isSpan(context, lastChild) ? lastChild.text.length : 0,\n        },\n      },\n    },\n    block,\n  })\n\n  return {before, after}\n}\n"], "names": ["isEqualSelections", "a", "b", "isEqualSelectionPoints", "anchor", "focus", "reverseSelection", "selection", "backward", "splitTextBlock", "context", "block", "point", "<PERSON><PERSON><PERSON><PERSON>", "children", "at", "<PERSON><PERSON><PERSON><PERSON>", "length", "before", "sliceTextBlock", "schema", "path", "_key", "offset", "after", "isSpan", "text"], "mappings": ";;;AAMO,SAASA,kBAAkBC,GAAoBC,GAAoB;AACxE,SAAI,CAACD,KAAK,CAACC,IACF,KAGL,CAACD,KAAK,CAACC,IACF,KAIPC,0BAAAA,uBAAuBF,EAAEG,QAAQF,EAAEE,MAAM,KACzCD,0BAAAA,uBAAuBF,EAAEI,OAAOH,EAAEG,KAAK;AAE3C;ACdO,SAASC,iBAEdC,WAA+C;AAC/C,SAAKA,cAIDA,UAAUC,WACL;AAAA,IACLJ,QAAQG,UAAUF;AAAAA,IAClBA,OAAOE,UAAUH;AAAAA,IACjBI,UAAU;AAAA,EAAA,IAIP;AAAA,IACLJ,QAAQG,UAAUF;AAAAA,IAClBA,OAAOE,UAAUH;AAAAA,IACjBI,UAAU;AAAA,EAAA;AAEd;AChBO,SAASC,eAAe;AAAA,EAC7BC;AAAAA,EACAC;AAAAA,EACAC;AAKF,GAA8E;AAC5E,QAAMC,aAAaF,MAAMG,SAASC,GAAG,CAAC,GAChCC,YAAYL,MAAMG,SAASC,GAAGJ,MAAMG,SAASG,SAAS,CAAC;AAE7D,MAAI,CAACJ,cAAc,CAACG;AAClB;AAGF,QAAME,SAASC,oBAAAA,eAAe;AAAA,IAC5BT,SAAS;AAAA,MACPU,QAAQV,QAAQU;AAAAA,MAChBb,WAAW;AAAA,QACTH,QAAQ;AAAA,UACNiB,MAAM,CAAC;AAAA,YAACC,MAAMX,MAAMW;AAAAA,UAAAA,GAAO,YAAY;AAAA,YAACA,MAAMT,WAAWS;AAAAA,UAAAA,CAAK;AAAA,UAC9DC,QAAQ;AAAA,QAAA;AAAA,QAEVlB,OAAOO;AAAAA,MAAAA;AAAAA,IACT;AAAA,IAEFD;AAAAA,EAAAA,CACD,GACKa,QAAQL,mCAAe;AAAA,IAC3BT,SAAS;AAAA,MACPU,QAAQV,QAAQU;AAAAA,MAChBb,WAAW;AAAA,QACTH,QAAQQ;AAAAA,QACRP,OAAO;AAAA,UACLgB,MAAM,CAAC;AAAA,YAACC,MAAMX,MAAMW;AAAAA,UAAAA,GAAO,YAAY;AAAA,YAACA,MAAMN,UAAUM;AAAAA,UAAAA,CAAK;AAAA,UAC7DC,QAAQE,iBAAAA,SAAOf,SAASM,SAAS,IAAIA,UAAUU,KAAKT,SAAS;AAAA,QAAA;AAAA,MAC/D;AAAA,IACF;AAAA,IAEFN;AAAAA,EAAAA,CACD;AAED,SAAO;AAAA,IAACO;AAAAA,IAAQM;AAAAA,EAAAA;AAClB;;;;;;;;;;;;;;;;;;;;;;;;"}