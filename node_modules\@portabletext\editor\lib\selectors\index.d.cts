import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, EditorSelection, EditorSelectionPoint, EditorSelector } from "../_chunks-dts/behavior.types.action.cjs";
import { PortableTextBlock, PortableTextListBlock, PortableTextObject, PortableTextSpan, PortableTextTextBlock } from "@sanity/types";
/**
 * @public
 */
declare const getActiveAnnotations: EditorSelector<Array<PortableTextObject>>;
/**
 * @public
 */
declare const getActiveListItem: EditorSelector<PortableTextListBlock['listItem'] | undefined>;
/**
 * @public
 */
declare const getActiveStyle: EditorSelector<PortableTextTextBlock['style']>;
/**
 * @public
 */
declare const getAnchorBlock: EditorSelector<{
  node: PortableTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getAnchorChild: EditorSelector<{
  node: PortableTextObject | PortableTextSpan;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getAnchorSpan: EditorSelector<{
  node: PortableTextSpan;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getAnchorTextBlock: EditorSelector<{
  node: PortableTextTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getBlockOffsets: EditorSelector<{
  start: BlockOffset;
  end: BlockOffset;
} | undefined>;
/**
 * @public
 * Returns the selection of the of the word the caret is placed in.
 * Note: Only returns a word selection if the current selection is collapsed
 */
declare const getCaretWordSelection: EditorSelector<EditorSelection>;
/**
 * @public
 */
declare const getFirstBlock: EditorSelector<{
  node: PortableTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getFocusBlock: EditorSelector<{
  node: PortableTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getFocusBlockObject: EditorSelector<{
  node: PortableTextObject;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getFocusChild: EditorSelector<{
  node: PortableTextObject | PortableTextSpan;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getFocusInlineObject: EditorSelector<{
  node: PortableTextObject;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getFocusListBlock: EditorSelector<{
  node: PortableTextListBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getFocusSpan: EditorSelector<{
  node: PortableTextSpan;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getFocusTextBlock: EditorSelector<{
  node: PortableTextTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getLastBlock: EditorSelector<{
  node: PortableTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @beta
 * @deprecated Use the precomputed `data-list-index` on text blocks instead.
 * Given the `path` of a block, this selector will return the "list index" of
 * the block.
 */
declare function getListIndex({
  path
}: {
  path: BlockPath;
}): EditorSelector<number | undefined>;
/**
 * @public
 */
declare const getNextBlock: EditorSelector<{
  node: PortableTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getNextInlineObject: EditorSelector<{
  node: PortableTextObject;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getPreviousBlock: EditorSelector<{
  node: PortableTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getPreviousInlineObject: EditorSelector<{
  node: PortableTextObject;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getSelectedBlocks: EditorSelector<Array<{
  node: PortableTextBlock;
  path: BlockPath;
}>>;
/**
 * @public
 * @deprecated Renamed to `getSelectedValue`.
 */
declare const getSelectedSlice: EditorSelector<Array<PortableTextBlock>>;
/**
 * @public
 */
declare const getSelectedSpans: EditorSelector<Array<{
  node: PortableTextSpan;
  path: ChildPath;
}>>;
/**
 * @public
 */
declare const getSelectedTextBlocks: EditorSelector<Array<{
  node: PortableTextTextBlock;
  path: BlockPath;
}>>;
/**
 * @public
 */
declare const getSelectedValue: EditorSelector<Array<PortableTextBlock>>;
/**
 * @public
 */
declare const getSelection: EditorSelector<EditorSelection>;
/**
 * @public
 */
declare const getSelectionEndBlock: EditorSelector<{
  node: PortableTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getSelectionEndChild: EditorSelector<{
  node: PortableTextSpan | PortableTextObject;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getSelectionEndPoint: EditorSelector<EditorSelectionPoint | undefined>;
/**
 * @public
 */
declare const getSelectionStartBlock: EditorSelector<{
  node: PortableTextBlock;
  path: BlockPath;
} | undefined>;
/**
 * @public
 */
declare const getSelectionStartChild: EditorSelector<{
  node: PortableTextSpan | PortableTextObject;
  path: ChildPath;
} | undefined>;
/**
 * @public
 */
declare const getSelectionStartPoint: EditorSelector<EditorSelectionPoint | undefined>;
/**
 * @public
 */
declare const getSelectionText: EditorSelector<string>;
/**
 * @public
 */
declare const getBlockTextBefore: EditorSelector<string>;
/**
 * @public
 */
declare const getTrimmedSelection: EditorSelector<EditorSelection>;
/**
 * @public
 */
declare const getValue: EditorSelector<Array<PortableTextBlock>>;
/**
 * @public
 */
declare function isActiveAnnotation(annotation: string): EditorSelector<boolean>;
/**
 * @public
 */
declare function isActiveDecorator(decorator: string): EditorSelector<boolean>;
/**
 * @public
 */
declare function isActiveListItem(listItem: string): EditorSelector<boolean>;
/**
 * @public
 */
declare function isActiveStyle(style: string): EditorSelector<boolean>;
/**
 * @public
 */
declare function isAtTheEndOfBlock(block: {
  node: PortableTextBlock;
  path: BlockPath;
}): EditorSelector<boolean>;
/**
 * @public
 */
declare function isAtTheStartOfBlock(block: {
  node: PortableTextBlock;
  path: BlockPath;
}): EditorSelector<boolean>;
/**
 * @public
 */
declare function isOverlappingSelection(selection: EditorSelection): EditorSelector<boolean>;
/**
 * @public
 */
declare function isPointAfterSelection(point: EditorSelectionPoint): EditorSelector<boolean>;
/**
 * @public
 */
declare function isPointBeforeSelection(point: EditorSelectionPoint): EditorSelector<boolean>;
/**
 * @public
 */
declare const isSelectingEntireBlocks: EditorSelector<boolean>;
/**
 * @public
 */
declare const isSelectionCollapsed: EditorSelector<boolean>;
/**
 * @public
 */
declare const isSelectionExpanded: EditorSelector<boolean>;
export { getActiveAnnotations, getActiveListItem, getActiveStyle, getAnchorBlock, getAnchorChild, getAnchorSpan, getAnchorTextBlock, getBlockOffsets, getBlockTextBefore, getCaretWordSelection, getFirstBlock, getFocusBlock, getFocusBlockObject, getFocusChild, getFocusInlineObject, getFocusListBlock, getFocusSpan, getFocusTextBlock, getLastBlock, getListIndex, getNextBlock, getNextInlineObject, getPreviousBlock, getPreviousInlineObject, getSelectedBlocks, getSelectedSlice, getSelectedSpans, getSelectedTextBlocks, getSelectedValue, getSelection, getSelectionEndBlock, getSelectionEndChild, getSelectionEndPoint, getSelectionStartBlock, getSelectionStartChild, getSelectionStartPoint, getSelectionText, getTrimmedSelection, getValue, isActiveAnnotation, isActiveDecorator, isActiveListItem, isActiveStyle, isAtTheEndOfBlock, isAtTheStartOfBlock, isOverlappingSelection, isPointAfterSelection, isPointBeforeSelection, isSelectingEntireBlocks, isSelectionCollapsed, isSelectionExpanded };