{"version": 3, "file": "util.is-selection-collapsed.cjs", "sources": ["../../src/utils/util.get-block-end-point.ts", "../../src/utils/util.is-empty-text-block.ts", "../../src/utils/util.is-equal-selection-points.ts", "../../src/utils/util.is-selection-collapsed.ts"], "sourcesContent": ["import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorContext} from '../editor/editor-snapshot'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport type {EditorSelectionPoint} from '../types/editor'\nimport type {BlockPath} from '../types/paths'\n\n/**\n * @public\n */\nexport function getBlockEndPoint({\n  context,\n  block,\n}: {\n  context: Pick<EditorContext, 'schema'>\n  block: {\n    node: PortableTextBlock\n    path: BlockPath\n  }\n}): EditorSelectionPoint {\n  if (isTextBlock(context, block.node)) {\n    const lastChild = block.node.children[block.node.children.length - 1]\n\n    if (lastChild) {\n      return {\n        path: [...block.path, 'children', {_key: lastChild._key}],\n        offset: isSpan(context, lastChild) ? lastChild.text.length : 0,\n      }\n    }\n  }\n\n  return {\n    path: block.path,\n    offset: 0,\n  }\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorContext} from '../editor/editor-snapshot'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {getTextBlockText} from './util.get-text-block-text'\n\n/**\n * @public\n */\nexport function isEmptyTextBlock(\n  context: Pick<EditorContext, 'schema'>,\n  block: PortableTextBlock,\n) {\n  if (!isTextBlock(context, block)) {\n    return false\n  }\n\n  const onlyText = block.children.every((child) => isSpan(context, child))\n  const blockText = getTextBlockText(block)\n\n  return onlyText && blockText === ''\n}\n", "import type {EditorSelectionPoint} from '../types/editor'\n\n/**\n * @public\n */\nexport function isEqualSelectionPoints(\n  a: EditorSelectionPoint,\n  b: EditorSelectionPoint,\n) {\n  return (\n    a.offset === b.offset && JSON.stringify(a.path) === JSON.stringify(b.path)\n  )\n}\n", "import type {EditorSelection} from '../types/editor'\n\n/**\n * @public\n */\nexport function isSelectionCollapsed(selection: EditorSelection) {\n  if (!selection) {\n    return false\n  }\n\n  return (\n    JSON.stringify(selection.anchor.path) ===\n      JSON.stringify(selection.focus.path) &&\n    selection.anchor.offset === selection.focus.offset\n  )\n}\n"], "names": ["getBlockEndPoint", "context", "block", "isTextBlock", "node", "<PERSON><PERSON><PERSON><PERSON>", "children", "length", "path", "_key", "offset", "isSpan", "text", "isEmptyTextBlock", "onlyText", "every", "child", "blockText", "getTextBlockText", "isEqualSelectionPoints", "a", "b", "JSON", "stringify", "isSelectionCollapsed", "selection", "anchor", "focus"], "mappings": ";;AASO,SAASA,iBAAiB;AAAA,EAC/BC;AAAAA,EACAC;AAOF,GAAyB;AACvB,MAAIC,6BAAYF,SAASC,MAAME,IAAI,GAAG;AACpC,UAAMC,YAAYH,MAAME,KAAKE,SAASJ,MAAME,KAAKE,SAASC,SAAS,CAAC;AAEpE,QAAIF;AACF,aAAO;AAAA,QACLG,MAAM,CAAC,GAAGN,MAAMM,MAAM,YAAY;AAAA,UAACC,MAAMJ,UAAUI;AAAAA,QAAAA,CAAK;AAAA,QACxDC,QAAQC,iBAAAA,OAAOV,SAASI,SAAS,IAAIA,UAAUO,KAAKL,SAAS;AAAA,MAAA;AAAA,EAGnE;AAEA,SAAO;AAAA,IACLC,MAAMN,MAAMM;AAAAA,IACZE,QAAQ;AAAA,EAAA;AAEZ;AC1BO,SAASG,iBACdZ,SACAC,OACA;AACA,MAAI,CAACC,iBAAAA,YAAYF,SAASC,KAAK;AAC7B,WAAO;AAGT,QAAMY,WAAWZ,MAAMI,SAASS,MAAOC,CAAAA,UAAUL,wBAAOV,SAASe,KAAK,CAAC,GACjEC,YAAYC,iBAAAA,iBAAiBhB,KAAK;AAExC,SAAOY,YAAYG,cAAc;AACnC;ACfO,SAASE,uBACdC,GACAC,GACA;AACA,SACED,EAAEV,WAAWW,EAAEX,UAAUY,KAAKC,UAAUH,EAAEZ,IAAI,MAAMc,KAAKC,UAAUF,EAAEb,IAAI;AAE7E;ACPO,SAASgB,qBAAqBC,WAA4B;AAC/D,SAAKA,YAKHH,KAAKC,UAAUE,UAAUC,OAAOlB,IAAI,MAClCc,KAAKC,UAAUE,UAAUE,MAAMnB,IAAI,KACrCiB,UAAUC,OAAOhB,WAAWe,UAAUE,MAAMjB,SANrC;AAQX;;;;;"}