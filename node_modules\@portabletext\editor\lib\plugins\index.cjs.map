{"version": 3, "file": "index.cjs", "sources": ["../../src/plugins/plugin.behavior.tsx", "../../src/internal-utils/get-text-to-emphasize.ts", "../../src/behaviors/behavior.decorator-pair.ts", "../../src/plugins/plugin.decorator-shortcut.ts", "../../src/plugins/plugin.editor-ref.tsx", "../../src/plugins/plugin.event-listener.tsx", "../../src/behaviors/behavior.markdown.ts", "../../src/plugins/plugin.markdown.tsx", "../../src/plugins/plugin.one-line.tsx"], "sourcesContent": ["import {useEffect} from 'react'\nimport type {Behavior} from '../behaviors'\nimport {useEditor} from '../editor/use-editor'\n\n/**\n * @beta\n */\nexport function BehaviorPlugin(props: {behaviors: Array<Behavior>}) {\n  const editor = useEditor()\n\n  useEffect(() => {\n    const unregisterBehaviors = props.behaviors.map((behavior) =>\n      editor.registerBehavior({behavior}),\n    )\n\n    return () => {\n      unregisterBehaviors.forEach((unregister) => unregister())\n    }\n  }, [editor, props.behaviors])\n\n  return null\n}\n", "export function createPairRegex(char: string, amount: number) {\n  // Negative lookbehind: Ensures that the matched sequence is not preceded by the same character\n  const prePrefix = `(?<!\\\\${char})`\n\n  // Repeats the character `amount` times\n  const prefix = `\\\\${char}`.repeat(Math.max(amount, 1))\n\n  // Negative lookahead: Ensures that the opening pair (**, *, etc.) is not followed by a space\n  const postPrefix = `(?!\\\\s)`\n\n  // Captures the content inside the pair\n  const content = `([^${char}\\\\n]+?)`\n\n  // Negative lookbehind: Ensures that the content is not followed by a space\n  const preSuffix = `(?<!\\\\s)`\n\n  // Repeats the character `amount` times\n  const suffix = `\\\\${char}`.repeat(Math.max(amount, 1))\n\n  // Negative lookahead: Ensures that the matched sequence is not followed by the same character\n  const postSuffix = `(?!\\\\${char})`\n\n  return `${prePrefix}${prefix}${postPrefix}${content}${preSuffix}${suffix}${postSuffix}`\n}\n\nconst italicRegex = new RegExp(\n  `(${createPairRegex('*', 1)}|${createPairRegex('_', 1)})$`,\n)\n\nconst boldRegex = new RegExp(\n  `(${createPairRegex('*', 2)}|${createPairRegex('_', 2)})$`,\n)\n\nexport function getTextToItalic(text: string) {\n  return text.match(italicRegex)?.at(0)\n}\n\nexport function getTextToBold(text: string) {\n  return text.match(boldRegex)?.at(0)\n}\n", "import type {EditorSchema} from '../editor/editor-schema'\nimport {createPairRegex} from '../internal-utils/get-text-to-emphasize'\nimport * as selectors from '../selectors'\nimport type {BlockOffset} from '../types/block-offset'\nimport * as utils from '../utils'\nimport {effect, execute} from './behavior.types.action'\nimport {defineBehavior} from './behavior.types.behavior'\n\nexport function createDecoratorPairBehavior(config: {\n  decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n  pair: {char: string; amount: number}\n  onDecorate: (offset: BlockOffset) => void\n}) {\n  if (config.pair.amount < 1) {\n    console.warn(\n      `The amount of characters in the pair should be greater than 0`,\n    )\n  }\n\n  const pairRegex = createPairRegex(config.pair.char, config.pair.amount)\n  const regEx = new RegExp(`(${pairRegex})$`)\n\n  return defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      if (config.pair.amount < 1) {\n        return false\n      }\n\n      const decorator = config.decorator({schema: snapshot.context.schema})\n\n      if (decorator === undefined) {\n        return false\n      }\n\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const selectionStartPoint = selectors.getSelectionStartPoint(snapshot)\n      const selectionStartOffset = selectionStartPoint\n        ? utils.spanSelectionPointToBlockOffset({\n            context: {\n              schema: snapshot.context.schema,\n              value: snapshot.context.value,\n            },\n            selectionPoint: selectionStartPoint,\n          })\n        : undefined\n\n      if (!focusTextBlock || !selectionStartOffset) {\n        return false\n      }\n\n      const textBefore = selectors.getBlockTextBefore(snapshot)\n      const newText = `${textBefore}${event.text}`\n      const textToDecorate = newText.match(regEx)?.at(0)\n\n      if (textToDecorate === undefined) {\n        return false\n      }\n\n      const prefixOffsets = {\n        anchor: {\n          path: focusTextBlock.path,\n          // Example: \"foo **bar**\".length - \"**bar**\".length = 4\n          offset: newText.length - textToDecorate.length,\n        },\n        focus: {\n          path: focusTextBlock.path,\n          // Example: \"foo **bar**\".length - \"**bar**\".length + \"*\".length * 2 = 6\n          offset:\n            newText.length -\n            textToDecorate.length +\n            config.pair.char.length * config.pair.amount,\n        },\n      }\n\n      const suffixOffsets = {\n        anchor: {\n          path: focusTextBlock.path,\n          // Example: \"foo **bar*|\" (10) + \"*\".length - 2 = 9\n          offset:\n            selectionStartOffset.offset +\n            event.text.length -\n            config.pair.char.length * config.pair.amount,\n        },\n        focus: {\n          path: focusTextBlock.path,\n          // Example: \"foo **bar*|\" (10) + \"*\".length = 11\n          offset: selectionStartOffset.offset + event.text.length,\n        },\n      }\n\n      // If the prefix is more than one character, then we need to check if\n      // there is an inline object inside it\n      if (prefixOffsets.focus.offset - prefixOffsets.anchor.offset > 1) {\n        const prefixSelection = utils.blockOffsetsToSelection({\n          context: snapshot.context,\n          offsets: prefixOffsets,\n        })\n        const inlineObjectBeforePrefixFocus = selectors.getPreviousInlineObject(\n          {\n            ...snapshot,\n            context: {\n              ...snapshot.context,\n              selection: prefixSelection\n                ? {\n                    anchor: prefixSelection.focus,\n                    focus: prefixSelection.focus,\n                  }\n                : null,\n            },\n          },\n        )\n        const inlineObjectBeforePrefixFocusOffset =\n          inlineObjectBeforePrefixFocus\n            ? utils.childSelectionPointToBlockOffset({\n                context: {\n                  schema: snapshot.context.schema,\n                  value: snapshot.context.value,\n                },\n                selectionPoint: {\n                  path: inlineObjectBeforePrefixFocus.path,\n                  offset: 0,\n                },\n              })\n            : undefined\n\n        if (\n          inlineObjectBeforePrefixFocusOffset &&\n          inlineObjectBeforePrefixFocusOffset.offset >\n            prefixOffsets.anchor.offset &&\n          inlineObjectBeforePrefixFocusOffset.offset <\n            prefixOffsets.focus.offset\n        ) {\n          return false\n        }\n      }\n\n      // If the suffix is more than one character, then we need to check if\n      // there is an inline object inside it\n      if (suffixOffsets.focus.offset - suffixOffsets.anchor.offset > 1) {\n        const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n        const previousInlineObjectOffset = previousInlineObject\n          ? utils.childSelectionPointToBlockOffset({\n              context: {\n                schema: snapshot.context.schema,\n                value: snapshot.context.value,\n              },\n              selectionPoint: {\n                path: previousInlineObject.path,\n                offset: 0,\n              },\n            })\n          : undefined\n\n        if (\n          previousInlineObjectOffset &&\n          previousInlineObjectOffset.offset > suffixOffsets.anchor.offset &&\n          previousInlineObjectOffset.offset < suffixOffsets.focus.offset\n        ) {\n          return false\n        }\n      }\n\n      return {\n        prefixOffsets,\n        suffixOffsets,\n        decorator,\n      }\n    },\n    actions: [\n      // Insert the text as usual in its own undo step\n      ({event}) => [execute(event)],\n      (_, {prefixOffsets, suffixOffsets, decorator}) => [\n        // Decorate the text between the prefix and suffix\n        execute({\n          type: 'decorator.add',\n          decorator,\n          at: {\n            anchor: prefixOffsets.focus,\n            focus: suffixOffsets.anchor,\n          },\n        }),\n        // Delete the suffix\n        execute({\n          type: 'delete.text',\n          at: suffixOffsets,\n        }),\n        // Delete the prefix\n        execute({\n          type: 'delete.text',\n          at: prefixOffsets,\n        }),\n        // Toggle the decorator off so the next inserted text isn't emphasized\n        execute({\n          type: 'decorator.remove',\n          decorator,\n        }),\n        effect(() => {\n          config.onDecorate({\n            ...suffixOffsets.anchor,\n            offset:\n              suffixOffsets.anchor.offset -\n              (prefixOffsets.focus.offset - prefixOffsets.anchor.offset),\n          })\n        }),\n      ],\n    ],\n  })\n}\n", "import {useActorRef} from '@xstate/react'\nimport {isEqual} from 'lodash'\nimport {\n  assign,\n  fromCallback,\n  setup,\n  type AnyEventObject,\n  type CallbackLogicFunction,\n} from 'xstate'\nimport {createDecoratorPairBehavior} from '../behaviors/behavior.decorator-pair'\nimport {effect, execute, forward} from '../behaviors/behavior.types.action'\nimport {defineBehavior} from '../behaviors/behavior.types.behavior'\nimport type {Editor} from '../editor'\nimport type {EditorSchema} from '../editor/editor-schema'\nimport {useEditor} from '../editor/use-editor'\nimport type {BlockOffset} from '../types/block-offset'\nimport * as utils from '../utils'\n\n/**\n * @beta\n * @deprecated Install the plugin from `@portabletext/plugin-character-pair-decorator`\n */\nexport function DecoratorShortcutPlugin(config: {\n  decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n  pair: {char: string; amount: number}\n}) {\n  const editor = useEditor()\n\n  useActorRef(decoratorPairMachine, {\n    input: {\n      editor,\n      decorator: config.decorator,\n      pair: config.pair,\n    },\n  })\n\n  return null\n}\n\ntype MarkdownEmphasisEvent =\n  | {\n      type: 'emphasis.add'\n      blockOffset: BlockOffset\n    }\n  | {\n      type: 'selection'\n      blockOffsets?: {\n        anchor: BlockOffset\n        focus: BlockOffset\n      }\n    }\n  | {\n      type: 'delete.backward'\n    }\n\nconst emphasisListener: CallbackLogicFunction<\n  AnyEventObject,\n  MarkdownEmphasisEvent,\n  {\n    decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n    editor: Editor\n    pair: {char: string; amount: number}\n  }\n> = ({sendBack, input}) => {\n  const unregister = input.editor.registerBehavior({\n    behavior: createDecoratorPairBehavior({\n      decorator: input.decorator,\n      pair: input.pair,\n      onDecorate: (offset) => {\n        sendBack({type: 'emphasis.add', blockOffset: offset})\n      },\n    }),\n  })\n\n  return unregister\n}\n\nconst selectionListenerCallback: CallbackLogicFunction<\n  AnyEventObject,\n  MarkdownEmphasisEvent,\n  {editor: Editor}\n> = ({sendBack, input}) => {\n  const unregister = input.editor.registerBehavior({\n    behavior: defineBehavior({\n      on: 'select',\n      guard: ({snapshot, event}) => {\n        if (!event.at) {\n          return {blockOffsets: undefined}\n        }\n\n        const anchor = utils.spanSelectionPointToBlockOffset({\n          context: snapshot.context,\n          selectionPoint: event.at.anchor,\n        })\n        const focus = utils.spanSelectionPointToBlockOffset({\n          context: snapshot.context,\n          selectionPoint: event.at.focus,\n        })\n\n        if (!anchor || !focus) {\n          return {blockOffsets: undefined}\n        }\n\n        return {\n          blockOffsets: {\n            anchor,\n            focus,\n          },\n        }\n      },\n      actions: [\n        ({event}, {blockOffsets}) => [\n          {\n            type: 'effect',\n            effect: () => {\n              sendBack({type: 'selection', blockOffsets})\n            },\n          },\n          forward(event),\n        ],\n      ],\n    }),\n  })\n\n  return unregister\n}\n\nconst deleteBackwardListenerCallback: CallbackLogicFunction<\n  AnyEventObject,\n  MarkdownEmphasisEvent,\n  {editor: Editor}\n> = ({sendBack, input}) => {\n  const unregister = input.editor.registerBehavior({\n    behavior: defineBehavior({\n      on: 'delete.backward',\n      actions: [\n        () => [\n          execute({\n            type: 'history.undo',\n          }),\n          effect(() => {\n            sendBack({type: 'delete.backward'})\n          }),\n        ],\n      ],\n    }),\n  })\n\n  return unregister\n}\n\nconst decoratorPairMachine = setup({\n  types: {\n    context: {} as {\n      decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n      editor: Editor\n      offsetAfterEmphasis?: BlockOffset\n      pair: {char: string; amount: number}\n    },\n    input: {} as {\n      decorator: ({schema}: {schema: EditorSchema}) => string | undefined\n      editor: Editor\n      pair: {char: string; amount: number}\n    },\n    events: {} as MarkdownEmphasisEvent,\n  },\n  actors: {\n    'emphasis listener': fromCallback(emphasisListener),\n    'delete.backward listener': fromCallback(deleteBackwardListenerCallback),\n    'selection listener': fromCallback(selectionListenerCallback),\n  },\n}).createMachine({\n  id: 'decorator pair',\n  context: ({input}) => ({\n    decorator: input.decorator,\n    editor: input.editor,\n    pair: input.pair,\n  }),\n  initial: 'idle',\n  states: {\n    'idle': {\n      invoke: [\n        {\n          src: 'emphasis listener',\n          input: ({context}) => ({\n            decorator: context.decorator,\n            editor: context.editor,\n            pair: context.pair,\n          }),\n        },\n      ],\n      on: {\n        'emphasis.add': {\n          target: 'emphasis added',\n          actions: assign({\n            offsetAfterEmphasis: ({event}) => event.blockOffset,\n          }),\n        },\n      },\n    },\n    'emphasis added': {\n      exit: [\n        assign({\n          offsetAfterEmphasis: undefined,\n        }),\n      ],\n      invoke: [\n        {\n          src: 'selection listener',\n          input: ({context}) => ({editor: context.editor}),\n        },\n        {\n          src: 'delete.backward listener',\n          input: ({context}) => ({editor: context.editor}),\n        },\n      ],\n      on: {\n        'selection': {\n          target: 'idle',\n          guard: ({context, event}) => {\n            const selectionChanged = !isEqual(\n              {\n                anchor: context.offsetAfterEmphasis,\n                focus: context.offsetAfterEmphasis,\n              },\n              event.blockOffsets,\n            )\n\n            return selectionChanged\n          },\n        },\n        'delete.backward': {\n          target: 'idle',\n        },\n      },\n    },\n  },\n})\n", "import React from 'react'\nimport type {Editor} from '../editor'\nimport {useEditor} from '../editor/use-editor'\n\n/**\n * @beta\n */\nexport const EditorRefPlugin = React.forwardRef<Editor | null>((_, ref) => {\n  const editor = useEditor()\n\n  const portableTextEditorRef = React.useRef(editor)\n\n  React.useImperativeHandle(ref, () => portableTextEditorRef.current, [])\n\n  return null\n})\nEditorRefPlugin.displayName = 'EditorRefPlugin'\n", "import {useEffect} from 'react'\nimport {useEffectEvent} from 'use-effect-event'\nimport type {EditorEmittedEvent} from '../editor/relay-machine'\nimport {useEditor} from '../editor/use-editor'\n\n/**\n * @public\n * Listen for events emitted by the editor. Must be used inside `EditorProvider`. Events available include:\n *  - 'blurred'\n *  - 'done loading'\n *  - 'editable'\n *  - 'error'\n *  - 'focused'\n *  - 'invalid value'\n *  - 'loading'\n *  - 'mutation'\n *  - 'patch'\n *  - 'read only'\n *  - 'ready'\n *  - 'selection'\n *  - 'value changed'\n *\n * @example\n * Listen and log events.\n * ```tsx\n * import {EditorProvider} from '@portabletext/editor'\n * import {EventListenerPlugin} from '@portabletext/editor/plugins'\n *\n * function MyComponent() {\n *  return (\n *  <EditorProvider>\n *   <EventListenerPlugin\n *    on={(event) => {\n *     console.log(event)\n *    }\n *   } />\n *   { ... }\n * </EditorProvider>\n *  )\n * }\n * ```\n * @example\n * Handle events when there is a mutation.\n * ```tsx\n * <EventListenerPlugin\n *  on={(event) => {\n *    if (event.type === 'mutation') {\n *      console.log('Value changed:', event.snapshot)\n *    }\n *  }}\n * />\n * ```\n * @group Components\n */\nexport function EventListenerPlugin(props: {\n  on: (event: EditorEmittedEvent) => void\n}) {\n  const editor = useEditor()\n  const on = useEffectEvent(props.on)\n\n  useEffect(() => {\n    const subscription = editor.on('*', on)\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }, [editor])\n\n  return null\n}\n", "import type {EditorSchema} from '../editor/editor-schema'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport * as selectors from '../selectors'\nimport {spanSelectionPointToBlockOffset} from '../utils/util.block-offset'\nimport {getTextBlockText} from '../utils/util.get-text-block-text'\nimport {execute} from './behavior.types.action'\nimport {defineBehavior} from './behavior.types.behavior'\n\nexport type MarkdownBehaviorsConfig = {\n  horizontalRuleObject?: (context: {\n    schema: EditorSchema\n  }) => {name: string; value?: {[prop: string]: unknown}} | undefined\n  defaultStyle?: (context: {schema: EditorSchema}) => string | undefined\n  headingStyle?: (context: {\n    schema: EditorSchema\n    level: number\n  }) => string | undefined\n  blockquoteStyle?: (context: {schema: EditorSchema}) => string | undefined\n  unorderedListStyle?: (context: {schema: EditorSchema}) => string | undefined\n  orderedListStyle?: (context: {schema: EditorSchema}) => string | undefined\n}\n\nexport function createMarkdownBehaviors(config: MarkdownBehaviorsConfig) {\n  const automaticBlockquoteOnSpace = defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      const isSpace = event.text === ' '\n\n      if (!isSpace) {\n        return false\n      }\n\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const focusSpan = selectors.getFocusSpan(snapshot)\n\n      if (!selectionCollapsed || !focusTextBlock || !focusSpan) {\n        return false\n      }\n\n      const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n      const blockOffset = spanSelectionPointToBlockOffset({\n        context: snapshot.context,\n        selectionPoint: {\n          path: [\n            {_key: focusTextBlock.node._key},\n            'children',\n            {_key: focusSpan.node._key},\n          ],\n          offset: snapshot.context.selection?.focus.offset ?? 0,\n        },\n      })\n\n      if (previousInlineObject || !blockOffset) {\n        return false\n      }\n\n      const blockText = getTextBlockText(focusTextBlock.node)\n      const caretAtTheEndOfQuote = blockOffset.offset === 1\n      const looksLikeMarkdownQuote = /^>/.test(blockText)\n      const blockquoteStyle = config.blockquoteStyle?.(snapshot.context)\n\n      if (\n        caretAtTheEndOfQuote &&\n        looksLikeMarkdownQuote &&\n        blockquoteStyle !== undefined\n      ) {\n        return {focusTextBlock, style: blockquoteStyle}\n      }\n\n      return false\n    },\n    actions: [\n      () => [\n        execute({\n          type: 'insert.text',\n          text: ' ',\n        }),\n      ],\n      (_, {focusTextBlock, style}) => [\n        execute({\n          type: 'block.unset',\n          props: ['listItem', 'level'],\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'block.set',\n          props: {style},\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'delete.text',\n          at: {\n            anchor: {\n              path: focusTextBlock.path,\n              offset: 0,\n            },\n            focus: {\n              path: focusTextBlock.path,\n              offset: 2,\n            },\n          },\n        }),\n      ],\n    ],\n  })\n  const automaticHr = defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      const hrCharacter =\n        event.text === '-'\n          ? '-'\n          : event.text === '*'\n            ? '*'\n            : event.text === '_'\n              ? '_'\n              : undefined\n\n      if (hrCharacter === undefined) {\n        return false\n      }\n\n      const hrObject = config.horizontalRuleObject?.(snapshot.context)\n      const focusBlock = selectors.getFocusTextBlock(snapshot)\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n\n      if (!hrObject || !focusBlock || !selectionCollapsed) {\n        return false\n      }\n\n      const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n      const textBefore = selectors.getBlockTextBefore(snapshot)\n      const hrBlockOffsets = {\n        anchor: {\n          path: focusBlock.path,\n          offset: 0,\n        },\n        focus: {\n          path: focusBlock.path,\n          offset: 3,\n        },\n      }\n\n      if (\n        !previousInlineObject &&\n        textBefore === `${hrCharacter}${hrCharacter}`\n      ) {\n        return {hrObject, focusBlock, hrCharacter, hrBlockOffsets}\n      }\n\n      return false\n    },\n    actions: [\n      (_, {hrCharacter}) => [\n        execute({\n          type: 'insert.text',\n          text: hrCharacter,\n        }),\n      ],\n      (_, {hrObject, hrBlockOffsets}) => [\n        execute({\n          type: 'insert.block',\n          placement: 'before',\n          block: {\n            _type: hrObject.name,\n            ...(hrObject.value ?? {}),\n          },\n        }),\n        execute({\n          type: 'delete.text',\n          at: hrBlockOffsets,\n        }),\n      ],\n    ],\n  })\n  const automaticHrOnPaste = defineBehavior({\n    on: 'clipboard.paste',\n    guard: ({snapshot, event}) => {\n      const text = event.originEvent.dataTransfer.getData('text/plain')\n      const hrRegExp = /^(---)$|(___)$|(\\*\\*\\*)$/\n      const hrCharacters = text.match(hrRegExp)?.[0]\n      const hrObject = config.horizontalRuleObject?.(snapshot.context)\n      const focusBlock = selectors.getFocusBlock(snapshot)\n\n      if (!hrCharacters || !hrObject || !focusBlock) {\n        return false\n      }\n\n      return {hrCharacters, hrObject, focusBlock}\n    },\n    actions: [\n      (_, {hrCharacters}) => [\n        execute({\n          type: 'insert.text',\n          text: hrCharacters,\n        }),\n      ],\n      ({snapshot}, {hrObject, focusBlock}) =>\n        isTextBlock(snapshot.context, focusBlock.node)\n          ? [\n              execute({\n                type: 'insert.block',\n                block: {\n                  _type: snapshot.context.schema.block.name,\n                  children: focusBlock.node.children,\n                },\n                placement: 'after',\n              }),\n              execute({\n                type: 'insert.block',\n                block: {\n                  _type: hrObject.name,\n                  ...(hrObject.value ?? {}),\n                },\n                placement: 'after',\n              }),\n              execute({\n                type: 'delete.block',\n                at: focusBlock.path,\n              }),\n            ]\n          : [\n              execute({\n                type: 'insert.block',\n                block: {\n                  _type: hrObject.name,\n                  ...(hrObject.value ?? {}),\n                },\n                placement: 'after',\n              }),\n            ],\n    ],\n  })\n  const automaticHeadingOnSpace = defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      const isSpace = event.text === ' '\n\n      if (!isSpace) {\n        return false\n      }\n\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const focusSpan = selectors.getFocusSpan(snapshot)\n\n      if (!selectionCollapsed || !focusTextBlock || !focusSpan) {\n        return false\n      }\n\n      const blockOffset = spanSelectionPointToBlockOffset({\n        context: snapshot.context,\n        selectionPoint: {\n          path: [\n            {_key: focusTextBlock.node._key},\n            'children',\n            {_key: focusSpan.node._key},\n          ],\n          offset: snapshot.context.selection?.focus.offset ?? 0,\n        },\n      })\n\n      if (!blockOffset) {\n        return false\n      }\n\n      const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n      const blockText = getTextBlockText(focusTextBlock.node)\n      const markdownHeadingSearch = /^#+/.exec(blockText)\n      const level = markdownHeadingSearch\n        ? markdownHeadingSearch[0].length\n        : undefined\n      const caretAtTheEndOfHeading = blockOffset.offset === level\n\n      if (previousInlineObject || !caretAtTheEndOfHeading) {\n        return false\n      }\n\n      const style =\n        level !== undefined\n          ? config.headingStyle?.({schema: snapshot.context.schema, level})\n          : undefined\n\n      if (level !== undefined && style !== undefined) {\n        return {\n          focusTextBlock,\n          style: style,\n          level,\n        }\n      }\n\n      return false\n    },\n    actions: [\n      ({event}) => [execute(event)],\n      (_, {focusTextBlock, style, level}) => [\n        execute({\n          type: 'block.unset',\n          props: ['listItem', 'level'],\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'block.set',\n          props: {style},\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'delete.text',\n          at: {\n            anchor: {\n              path: focusTextBlock.path,\n              offset: 0,\n            },\n            focus: {\n              path: focusTextBlock.path,\n              offset: level + 1,\n            },\n          },\n        }),\n      ],\n    ],\n  })\n  const clearStyleOnBackspace = defineBehavior({\n    on: 'delete.backward',\n    guard: ({snapshot}) => {\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const focusSpan = selectors.getFocusSpan(snapshot)\n\n      if (!selectionCollapsed || !focusTextBlock || !focusSpan) {\n        return false\n      }\n\n      const atTheBeginningOfBLock =\n        focusTextBlock.node.children[0]._key === focusSpan.node._key &&\n        snapshot.context.selection?.focus.offset === 0\n\n      const defaultStyle = config.defaultStyle?.(snapshot.context)\n\n      if (\n        atTheBeginningOfBLock &&\n        defaultStyle &&\n        focusTextBlock.node.style !== defaultStyle\n      ) {\n        return {defaultStyle, focusTextBlock}\n      }\n\n      return false\n    },\n    actions: [\n      (_, {defaultStyle, focusTextBlock}) => [\n        execute({\n          type: 'block.set',\n          props: {style: defaultStyle},\n          at: focusTextBlock.path,\n        }),\n      ],\n    ],\n  })\n  const automaticListOnSpace = defineBehavior({\n    on: 'insert.text',\n    guard: ({snapshot, event}) => {\n      const isSpace = event.text === ' '\n\n      if (!isSpace) {\n        return false\n      }\n\n      const selectionCollapsed = selectors.isSelectionCollapsed(snapshot)\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n      const focusSpan = selectors.getFocusSpan(snapshot)\n\n      if (!selectionCollapsed || !focusTextBlock || !focusSpan) {\n        return false\n      }\n\n      const previousInlineObject = selectors.getPreviousInlineObject(snapshot)\n      const blockOffset = spanSelectionPointToBlockOffset({\n        context: snapshot.context,\n        selectionPoint: {\n          path: [\n            {_key: focusTextBlock.node._key},\n            'children',\n            {_key: focusSpan.node._key},\n          ],\n          offset: snapshot.context.selection?.focus.offset ?? 0,\n        },\n      })\n\n      if (previousInlineObject || !blockOffset) {\n        return false\n      }\n\n      const blockText = getTextBlockText(focusTextBlock.node)\n      const defaultStyle = config.defaultStyle?.(snapshot.context)\n      const looksLikeUnorderedList = /^(-|\\*)/.test(blockText)\n      const unorderedListStyle = config.unorderedListStyle?.(snapshot.context)\n      const caretAtTheEndOfUnorderedList = blockOffset.offset === 1\n\n      if (\n        defaultStyle &&\n        caretAtTheEndOfUnorderedList &&\n        looksLikeUnorderedList &&\n        unorderedListStyle !== undefined\n      ) {\n        return {\n          focusTextBlock,\n          listItem: unorderedListStyle,\n          listItemLength: 1,\n          style: defaultStyle,\n        }\n      }\n\n      const looksLikeOrderedList = /^1\\./.test(blockText)\n      const orderedListStyle = config.orderedListStyle?.(snapshot.context)\n      const caretAtTheEndOfOrderedList = blockOffset.offset === 2\n\n      if (\n        defaultStyle &&\n        caretAtTheEndOfOrderedList &&\n        looksLikeOrderedList &&\n        orderedListStyle !== undefined\n      ) {\n        return {\n          focusTextBlock,\n          listItem: orderedListStyle,\n          listItemLength: 2,\n          style: defaultStyle,\n        }\n      }\n\n      return false\n    },\n    actions: [\n      ({event}) => [execute(event)],\n      (_, {focusTextBlock, style, listItem, listItemLength}) => [\n        execute({\n          type: 'block.set',\n          props: {\n            listItem,\n            level: 1,\n            style,\n          },\n          at: focusTextBlock.path,\n        }),\n        execute({\n          type: 'delete.text',\n          at: {\n            anchor: {\n              path: focusTextBlock.path,\n              offset: 0,\n            },\n            focus: {\n              path: focusTextBlock.path,\n              offset: listItemLength + 1,\n            },\n          },\n        }),\n      ],\n    ],\n  })\n\n  const markdownBehaviors = [\n    automaticBlockquoteOnSpace,\n    automaticHeadingOnSpace,\n    automaticHr,\n    automaticHrOnPaste,\n    clearStyleOnBackspace,\n    automaticListOnSpace,\n  ]\n\n  return markdownBehaviors\n}\n", "import {useEffect} from 'react'\nimport {\n  createMarkdownBehaviors,\n  type MarkdownBehaviorsConfig,\n} from '../behaviors/behavior.markdown'\nimport type {EditorSchema} from '../editor/editor-schema'\nimport {useEditor} from '../editor/use-editor'\nimport {DecoratorShortcutPlugin} from './plugin.decorator-shortcut'\n\n/**\n * @beta\n */\nexport type MarkdownPluginConfig = MarkdownBehaviorsConfig & {\n  boldDecorator?: ({schema}: {schema: EditorSchema}) => string | undefined\n  codeDecorator?: ({schema}: {schema: EditorSchema}) => string | undefined\n  italicDecorator?: ({schema}: {schema: EditorSchema}) => string | undefined\n  strikeThroughDecorator?: ({\n    schema,\n  }: {\n    schema: EditorSchema\n  }) => string | undefined\n}\n\n/**\n * @beta\n * Add markdown behaviors for common markdown actions such as converting ### to headings, --- to HRs, and more.\n *\n * @example\n * Configure the bundled markdown behaviors\n * ```ts\n * import {EditorProvider} from '@portabletext/editor'\n * import {MarkdownPlugin} from '@portabletext/editor/plugins'\n *\n * function App() {\n *   return (\n *    <EditorProvider>\n *      <MarkdownPlugin\n *        config={{\n *          boldDecorator: ({schema}) =>\n *            schema.decorators.find((decorator) => decorator.value === 'strong')?.value,\n *          codeDecorator: ({schema}) =>\n *            schema.decorators.find((decorator) => decorator.value === 'code')?.value,\n *          italicDecorator: ({schema}) =>\n *            schema.decorators.find((decorator) => decorator.value === 'em')?.value,\n *          strikeThroughDecorator: ({schema}) =>\n *            schema.decorators.find((decorator) => decorator.value === 'strike-through')?.value,\n *          horizontalRuleObject: ({schema}) => {\n *            const name = schema.blockObjects.find(\n *              (object) => object.name === 'break',\n *            )?.name\n *            return name ? {name} : undefined\n *          },\n *          defaultStyle: ({schema}) => schema.styles[0].value,\n *          headingStyle: ({schema, level}) =>\n *            schema.styles.find((style) => style.value === `h${level}`)\n *              ?.value,\n *          blockquoteStyle: ({schema}) =>\n *            schema.styles.find((style) => style.value === 'blockquote')\n *              ?.value,\n *          unorderedListStyle: ({schema}) =>\n *            schema.lists.find((list) => list.value === 'bullet')?.value,\n *          orderedListStyle: ({schema}) =>\n *            schema.lists.find((list) => list.value === 'number')?.value,\n *        }}\n *      />\n *      {...}\n *    </EditorProvider>\n *  )\n * }\n * ```\n *\n * @deprecated Install the plugin from `@portabletext/plugin-markdown-shortcuts`\n */\nexport function MarkdownPlugin(props: {config: MarkdownPluginConfig}) {\n  const editor = useEditor()\n\n  useEffect(() => {\n    const behaviors = createMarkdownBehaviors(props.config)\n\n    const unregisterBehaviors = behaviors.map((behavior) =>\n      editor.registerBehavior({behavior}),\n    )\n\n    return () => {\n      for (const unregisterBehavior of unregisterBehaviors) {\n        unregisterBehavior()\n      }\n    }\n  }, [editor, props.config])\n\n  return (\n    <>\n      {props.config.boldDecorator ? (\n        <>\n          <DecoratorShortcutPlugin\n            decorator={props.config.boldDecorator}\n            pair={{char: '*', amount: 2}}\n          />\n          <DecoratorShortcutPlugin\n            decorator={props.config.boldDecorator}\n            pair={{char: '_', amount: 2}}\n          />\n        </>\n      ) : null}\n      {props.config.codeDecorator ? (\n        <DecoratorShortcutPlugin\n          decorator={props.config.codeDecorator}\n          pair={{char: '`', amount: 1}}\n        />\n      ) : null}\n      {props.config.italicDecorator ? (\n        <>\n          <DecoratorShortcutPlugin\n            decorator={props.config.italicDecorator}\n            pair={{char: '*', amount: 1}}\n          />\n          <DecoratorShortcutPlugin\n            decorator={props.config.italicDecorator}\n            pair={{char: '_', amount: 1}}\n          />\n        </>\n      ) : null}\n      {props.config.strikeThroughDecorator ? (\n        <DecoratorShortcutPlugin\n          decorator={props.config.strikeThroughDecorator}\n          pair={{char: '~', amount: 2}}\n        />\n      ) : null}\n    </>\n  )\n}\n", "import {defineBehavior, execute, raise} from '../behaviors'\nimport * as selectors from '../selectors'\nimport * as utils from '../utils'\nimport {BehaviorPlugin} from './plugin.behavior'\n\nconst oneLineBehaviors = [\n  /**\n   * Hitting Enter on an expanded selection should just delete that selection\n   * without causing a line break.\n   */\n  defineBehavior({\n    on: 'insert.break',\n    guard: ({snapshot}) =>\n      snapshot.context.selection && selectors.isSelectionExpanded(snapshot)\n        ? {selection: snapshot.context.selection}\n        : false,\n    actions: [(_, {selection}) => [execute({type: 'delete', at: selection})]],\n  }),\n  /**\n   * All other cases of `insert.break` should be aborted.\n   */\n  defineBehavior({\n    on: 'insert.break',\n    actions: [],\n  }),\n  /**\n   * `insert.block` `before` or `after` is not allowed in a one-line editor.\n   */\n  defineBehavior({\n    on: 'insert.block',\n    guard: ({event}) =>\n      event.placement === 'before' || event.placement === 'after',\n    actions: [],\n  }),\n  /**\n   * An ordinary `insert.block` is acceptable if it's a text block. In that\n   * case it will get merged into the existing text block.\n   */\n  defineBehavior({\n    on: 'insert.block',\n    guard: ({snapshot, event}) => {\n      const focusTextBlock = selectors.getFocusTextBlock(snapshot)\n\n      if (\n        !focusTextBlock ||\n        !utils.isTextBlock(snapshot.context, event.block)\n      ) {\n        return false\n      }\n\n      return true\n    },\n    actions: [\n      ({event}) => [\n        execute({\n          type: 'insert.block',\n          block: event.block,\n          placement: 'auto',\n          select: 'end',\n        }),\n      ],\n    ],\n  }),\n  /**\n   * Fallback Behavior to avoid `insert.block` in case the Behaviors above all\n   * end up with a falsy guard.\n   */\n  defineBehavior({\n    on: 'insert.block',\n    actions: [],\n  }),\n  /**\n   * If multiple blocks are inserted, then the non-text blocks are filtered out\n   * and the text blocks are merged into one block\n   */\n  defineBehavior({\n    on: 'insert.blocks',\n    guard: ({snapshot, event}) => {\n      const textBlocks = event.blocks.filter((block) =>\n        utils.isTextBlock(snapshot.context, block),\n      )\n\n      if (textBlocks.length === 0) {\n        return false\n      }\n\n      return textBlocks.reduce((targetBlock, incomingBlock) => {\n        return utils.mergeTextBlocks({\n          context: snapshot.context,\n          targetBlock,\n          incomingBlock,\n        })\n      })\n    },\n    actions: [\n      // `insert.block` is raised so the Behavior above can handle the\n      // insertion\n      (_, block) => [raise({type: 'insert.block', block, placement: 'auto'})],\n    ],\n  }),\n  /**\n   * Fallback Behavior to avoid `insert.blocks` in case the Behavior above\n   * ends up with a falsy guard.\n   */\n  defineBehavior({\n    on: 'insert.blocks',\n    actions: [],\n  }),\n]\n\n/**\n * @beta\n * Restrict the editor to one line. The plugin takes care of blocking\n * `insert.break` events and smart handling of other `insert.*` events.\n *\n * Place it with as high priority as possible to make sure other plugins don't\n * overwrite `insert.*` events before this plugin gets a chance to do so.\n *\n * @deprecated Install the plugin from `@portabletext/plugin-one-line`\n */\nexport function OneLinePlugin() {\n  return <BehaviorPlugin behaviors={oneLineBehaviors} />\n}\n"], "names": ["BehaviorPlugin", "props", "$", "_c", "editor", "useEditor", "t0", "t1", "behaviors", "unregisterBeh<PERSON>ors", "map", "behavior", "registerBehavior", "for<PERSON>ach", "_temp", "useEffect", "unregister", "createPairRegex", "char", "amount", "prePrefix", "prefix", "repeat", "Math", "max", "postPrefix", "content", "preSuffix", "suffix", "postSuffix", "createDecoratorPairBehavior", "config", "pair", "console", "warn", "pairRegex", "regEx", "RegExp", "define<PERSON>eh<PERSON>or", "on", "guard", "snapshot", "event", "decorator", "schema", "context", "undefined", "focusTextBlock", "selectors", "selectionStartPoint", "selectionStartOffset", "utils", "value", "selectionPoint", "newText", "text", "textToDecorate", "match", "at", "prefixOffsets", "anchor", "path", "offset", "length", "focus", "suffixOffsets", "prefixSelection", "offsets", "inlineObjectBeforePrefixFocus", "selection", "inlineObjectBeforePrefixFocusOffset", "previousInlineObject", "previousInlineObjectOffset", "actions", "execute", "_", "type", "effect", "onDecorate", "DecoratorShortcutPlugin", "input", "useActorRef", "decoratorPairMachine", "emphasisListener", "sendBack", "blockOffset", "selectionListenerCallback", "blockOffsets", "forward", "deleteBackwardListenerCallback", "setup", "types", "events", "actors", "fromCallback", "createMachine", "id", "initial", "states", "invoke", "src", "target", "assign", "offsetAfterEmphasis", "exit", "isEqual", "EditorRefPlugin", "React", "forwardRef", "ref", "portableTextEditorRef", "useRef", "Symbol", "for", "current", "useImperativeHandle", "displayName", "EventListenerPlugin", "useEffectEvent", "subscription", "unsubscribe", "createMarkdownBehaviors", "automaticBlockquoteOnSpace", "selectionCollapsed", "focusSpan", "spanSelectionPointToBlockOffset", "_key", "node", "blockText", "getTextBlockText", "caretAtTheEndOfQuote", "looksLikeMarkdownQuote", "test", "blockquoteStyle", "style", "automaticHr", "hrCharacter", "hrObject", "horizontalRuleObject", "focusBlock", "textBefore", "hrBlockOffsets", "placement", "block", "_type", "name", "automaticHrOnPaste", "originEvent", "dataTransfer", "getData", "hrRegExp", "hrCharacters", "isTextBlock", "children", "automaticHeadingOnSpace", "markdownHeadingSearch", "exec", "level", "caretAtTheEndOfHeading", "headingStyle", "clearStyleOnBackspace", "atTheBeginningOfBLock", "defaultStyle", "automaticListOnSpace", "looksLikeUnorderedList", "unorderedListStyle", "caretAtTheEndOfUnorderedList", "listItem", "listItemLength", "looksLikeOrderedList", "orderedListStyle", "caretAtTheEndOfOrderedList", "<PERSON>downP<PERSON>in", "unregister<PERSON><PERSON><PERSON><PERSON>", "t2", "boldDecorator", "jsxs", "Fragment", "jsx", "t3", "codeDecorator", "t4", "italicDecorator", "t5", "strikeThroughDecorator", "t6", "oneLineBehaviors", "select", "textBlocks", "blocks", "filter", "reduce", "targetBlock", "incomingBlock", "raise", "OneLinePlugin"], "mappings": ";;;;;;;AAOO,SAAAA,eAAAC,OAAA;AAAA,QAAAC,IAAAC,qBAAAA,EAAA,CAAA,GACLC,SAAeC,UAAAA,UAAAA;AAAW,MAAAC,IAAAC;AAAA,SAAAL,SAAAE,UAAAF,EAAA,CAAA,MAAAD,MAAAO,aAEhBF,KAAAA,MAAA;AACR,UAAAG,sBAA4BR,MAAKO,UAAAE,IAAAC,CAAAA,aAC/BP,OAAMQ,iBAAA;AAAA,MAAAD;AAAAA,IAAAA,CAA4B,CACpC;AAAC,WAAA,MAAA;AAGCF,0BAAmBI,QAAAC,KAAqC;AAAA,IAAC;AAAA,EAAA,GAE1DP,KAAA,CAACH,QAAQH,MAAKO,SAAA,GAAWN,OAAAE,QAAAF,EAAA,CAAA,IAAAD,MAAAO,WAAAN,OAAAI,IAAAJ,OAAAK,OAAAD,KAAAJ,EAAA,CAAA,GAAAK,KAAAL,EAAA,CAAA,IAR5Ba,MAAAA,UAAUT,IAQPC,EAAyB,GAAC;AAAA;AAXxB,SAAAO,MAAAE,YAAA;AAAA,SAS2CA,WAAAA;AAAY;AChBvD,SAASC,gBAAgBC,MAAcC,QAAgB;AAE5D,QAAMC,YAAY,SAASF,IAAI,KAGzBG,SAAS,KAAKH,IAAI,GAAGI,OAAOC,KAAKC,IAAIL,QAAQ,CAAC,CAAC,GAG/CM,aAAa,WAGbC,UAAU,MAAMR,IAAI,WAGpBS,YAAY,YAGZC,SAAS,KAAKV,IAAI,GAAGI,OAAOC,KAAKC,IAAIL,QAAQ,CAAC,CAAC,GAG/CU,aAAa,QAAQX,IAAI;AAE/B,SAAO,GAAGE,SAAS,GAAGC,MAAM,GAAGI,UAAU,GAAGC,OAAO,GAAGC,SAAS,GAAGC,MAAM,GAAGC,UAAU;AACvF;ACfO,SAASC,4BAA4BC,QAIzC;AACGA,SAAOC,KAAKb,SAAS,KACvBc,QAAQC,KACN,+DACF;AAGF,QAAMC,YAAYlB,gBAAgBc,OAAOC,KAAKd,MAAMa,OAAOC,KAAKb,MAAM,GAChEiB,QAAQ,IAAIC,OAAO,IAAIF,SAAS,IAAI;AAE1C,SAAOG,+BAAe;AAAA,IACpBC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,UAAIX,OAAOC,KAAKb,SAAS;AACvB,eAAO;AAGT,YAAMwB,YAAYZ,OAAOY,UAAU;AAAA,QAACC,QAAQH,SAASI,QAAQD;AAAAA,MAAAA,CAAO;AAEpE,UAAID,cAAcG;AAChB,eAAO;AAGT,YAAMC,iBAAiBC,6BAAAA,kBAA4BP,QAAQ,GACrDQ,sBAAsBD,oDAAiCP,QAAQ,GAC/DS,uBAAuBD,sBACzBE,iDAAsC;AAAA,QACpCN,SAAS;AAAA,UACPD,QAAQH,SAASI,QAAQD;AAAAA,UACzBQ,OAAOX,SAASI,QAAQO;AAAAA,QAAAA;AAAAA,QAE1BC,gBAAgBJ;AAAAA,MAAAA,CACjB,IACDH;AAEJ,UAAI,CAACC,kBAAkB,CAACG;AACtB,eAAO;AAIT,YAAMI,UAAU,GADGN,uBAAAA,mBAA6BP,QAAQ,CAC3B,GAAGC,MAAMa,IAAI,IACpCC,iBAAiBF,QAAQG,MAAMrB,KAAK,GAAGsB,GAAG,CAAC;AAEjD,UAAIF,mBAAmBV;AACrB,eAAO;AAGT,YAAMa,gBAAgB;AAAA,QACpBC,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA;AAAAA,UAErBC,QAAQR,QAAQS,SAASP,eAAeO;AAAAA,QAAAA;AAAAA,QAE1CC,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA;AAAAA,UAErBC,QACER,QAAQS,SACRP,eAAeO,SACfhC,OAAOC,KAAKd,KAAK6C,SAAShC,OAAOC,KAAKb;AAAAA,QAAAA;AAAAA,MAC1C,GAGI8C,gBAAgB;AAAA,QACpBL,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA;AAAAA,UAErBC,QACEZ,qBAAqBY,SACrBpB,MAAMa,KAAKQ,SACXhC,OAAOC,KAAKd,KAAK6C,SAAShC,OAAOC,KAAKb;AAAAA,QAAAA;AAAAA,QAE1C6C,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA;AAAAA,UAErBC,QAAQZ,qBAAqBY,SAASpB,MAAMa,KAAKQ;AAAAA,QAAAA;AAAAA,MACnD;AAKF,UAAIJ,cAAcK,MAAMF,SAASH,cAAcC,OAAOE,SAAS,GAAG;AAChE,cAAMI,kBAAkBf,sCAAAA,wBAA8B;AAAA,UACpDN,SAASJ,SAASI;AAAAA,UAClBsB,SAASR;AAAAA,QAAAA,CACV,GACKS,gCAAgCpB,qDACpC;AAAA,UACE,GAAGP;AAAAA,UACHI,SAAS;AAAA,YACP,GAAGJ,SAASI;AAAAA,YACZwB,WAAWH,kBACP;AAAA,cACEN,QAAQM,gBAAgBF;AAAAA,cACxBA,OAAOE,gBAAgBF;AAAAA,YAAAA,IAEzB;AAAA,UAAA;AAAA,QACN,CAEJ,GACMM,sCACJF,gCACIjB,uEAAuC;AAAA,UACrCN,SAAS;AAAA,YACPD,QAAQH,SAASI,QAAQD;AAAAA,YACzBQ,OAAOX,SAASI,QAAQO;AAAAA,UAAAA;AAAAA,UAE1BC,gBAAgB;AAAA,YACdQ,MAAMO,8BAA8BP;AAAAA,YACpCC,QAAQ;AAAA,UAAA;AAAA,QACV,CACD,IACDhB;AAEN,YACEwB,uCACAA,oCAAoCR,SAClCH,cAAcC,OAAOE,UACvBQ,oCAAoCR,SAClCH,cAAcK,MAAMF;AAEtB,iBAAO;AAAA,MAEX;AAIA,UAAIG,cAAcD,MAAMF,SAASG,cAAcL,OAAOE,SAAS,GAAG;AAChE,cAAMS,uBAAuBvB,6BAAAA,wBAAkCP,QAAQ,GACjE+B,6BAA6BD,uBAC/BpB,uEAAuC;AAAA,UACrCN,SAAS;AAAA,YACPD,QAAQH,SAASI,QAAQD;AAAAA,YACzBQ,OAAOX,SAASI,QAAQO;AAAAA,UAAAA;AAAAA,UAE1BC,gBAAgB;AAAA,YACdQ,MAAMU,qBAAqBV;AAAAA,YAC3BC,QAAQ;AAAA,UAAA;AAAA,QACV,CACD,IACDhB;AAEJ,YACE0B,8BACAA,2BAA2BV,SAASG,cAAcL,OAAOE,UACzDU,2BAA2BV,SAASG,cAAcD,MAAMF;AAExD,iBAAO;AAAA,MAEX;AAEA,aAAO;AAAA,QACLH;AAAAA,QACAM;AAAAA,QACAtB;AAAAA,MAAAA;AAAAA,IAEJ;AAAA,IACA8B,SAAS;AAAA;AAAA,MAEP,CAAC;AAAA,QAAC/B;AAAAA,MAAAA,MAAW,CAACgC,gBAAAA,QAAQhC,KAAK,CAAC;AAAA,MAC5B,CAACiC,GAAG;AAAA,QAAChB;AAAAA,QAAeM;AAAAA,QAAetB;AAAAA,MAAAA,MAAe;AAAA;AAAA,QAEhD+B,wBAAQ;AAAA,UACNE,MAAM;AAAA,UACNjC;AAAAA,UACAe,IAAI;AAAA,YACFE,QAAQD,cAAcK;AAAAA,YACtBA,OAAOC,cAAcL;AAAAA,UAAAA;AAAAA,QACvB,CACD;AAAA;AAAA,QAEDc,wBAAQ;AAAA,UACNE,MAAM;AAAA,UACNlB,IAAIO;AAAAA,QAAAA,CACL;AAAA;AAAA,QAEDS,wBAAQ;AAAA,UACNE,MAAM;AAAA,UACNlB,IAAIC;AAAAA,QAAAA,CACL;AAAA;AAAA,QAEDe,wBAAQ;AAAA,UACNE,MAAM;AAAA,UACNjC;AAAAA,QAAAA,CACD;AAAA,QACDkC,gBAAAA,OAAO,MAAM;AACX9C,iBAAO+C,WAAW;AAAA,YAChB,GAAGb,cAAcL;AAAAA,YACjBE,QACEG,cAAcL,OAAOE,UACpBH,cAAcK,MAAMF,SAASH,cAAcC,OAAOE;AAAAA,UAAAA,CACtD;AAAA,QACH,CAAC;AAAA,MAAA;AAAA,IAAC;AAAA,EACH,CAEJ;AACH;AC1LO,SAAAiB,wBAAAhD,QAAA;AAAA,QAAA7B,IAAAC,qBAAAA,EAAA,CAAA,GAILC,SAAeC,UAAAA,UAAAA;AAAW,MAAAC;AAAA,SAAAJ,EAAA,CAAA,MAAA6B,OAAAY,aAAAzC,EAAA,CAAA,MAAA6B,OAAAC,QAAA9B,SAAAE,UAEQE,KAAA;AAAA,IAAA0E,OAAA;AAAA,MAAA5E;AAAAA,MAAAuC,WAGnBZ,OAAMY;AAAAA,MAAAX,MACXD,OAAMC;AAAAA,IAAAA;AAAAA,EAAA,GAEf9B,EAAA,CAAA,IAAA6B,OAAAY,WAAAzC,EAAA,CAAA,IAAA6B,OAAAC,MAAA9B,OAAAE,QAAAF,OAAAI,MAAAA,KAAAJ,EAAA,CAAA,GAND+E,MAAAA,YAAAC,sBAAkC5E,EAMjC,GAAC;AAAA;AAqBJ,MAAM6E,mBAQFA,CAAC;AAAA,EAACC;AAAAA,EAAUJ;AAAK,MACAA,MAAM5E,OAAOQ,iBAAiB;AAAA,EAC/CD,UAAUmB,4BAA4B;AAAA,IACpCa,WAAWqC,MAAMrC;AAAAA,IACjBX,MAAMgD,MAAMhD;AAAAA,IACZ8C,YAAahB,CAAAA,WAAW;AACtBsB,eAAS;AAAA,QAACR,MAAM;AAAA,QAAgBS,aAAavB;AAAAA,MAAAA,CAAO;AAAA,IACtD;AAAA,EAAA,CACD;AACH,CAAC,GAKGwB,4BAIFA,CAAC;AAAA,EAACF;AAAAA,EAAUJ;AAAK,MACAA,MAAM5E,OAAOQ,iBAAiB;AAAA,EAC/CD,UAAU2B,gBAAAA,eAAe;AAAA,IACvBC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,UAAI,CAACA,MAAMgB;AACT,eAAO;AAAA,UAAC6B,cAAczC;AAAAA,QAAAA;AAGxB,YAAMc,SAAST,iBAAAA,gCAAsC;AAAA,QACnDN,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgBX,MAAMgB,GAAGE;AAAAA,MAAAA,CAC1B,GACKI,QAAQb,iDAAsC;AAAA,QAClDN,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgBX,MAAMgB,GAAGM;AAAAA,MAAAA,CAC1B;AAED,aAAI,CAACJ,UAAU,CAACI,QACP;AAAA,QAACuB,cAAczC;AAAAA,MAAAA,IAGjB;AAAA,QACLyC,cAAc;AAAA,UACZ3B;AAAAA,UACAI;AAAAA,QAAAA;AAAAA,MACF;AAAA,IAEJ;AAAA,IACAS,SAAS,CACP,CAAC;AAAA,MAAC/B;AAAAA,IAAAA,GAAQ;AAAA,MAAC6C;AAAAA,IAAAA,MAAkB,CAC3B;AAAA,MACEX,MAAM;AAAA,MACNC,QAAQA,MAAM;AACZO,iBAAS;AAAA,UAACR,MAAM;AAAA,UAAaW;AAAAA,QAAAA,CAAa;AAAA,MAC5C;AAAA,IAAA,GAEFC,gBAAAA,QAAQ9C,KAAK,CAAC,CACf;AAAA,EAAA,CAEJ;AACH,CAAC,GAKG+C,iCAIFA,CAAC;AAAA,EAACL;AAAAA,EAAUJ;AAAK,MACAA,MAAM5E,OAAOQ,iBAAiB;AAAA,EAC/CD,UAAU2B,gBAAAA,eAAe;AAAA,IACvBC,IAAI;AAAA,IACJkC,SAAS,CACP,MAAM,CACJC,wBAAQ;AAAA,MACNE,MAAM;AAAA,IAAA,CACP,GACDC,gBAAAA,OAAO,MAAM;AACXO,eAAS;AAAA,QAACR,MAAM;AAAA,MAAA,CAAkB;AAAA,IACpC,CAAC,CAAC,CACH;AAAA,EAAA,CAEJ;AACH,CAAC,GAKGM,uBAAuBQ,aAAM;AAAA,EACjCC,OAAO;AAAA,IACL9C,SAAS,CAAA;AAAA,IAMTmC,OAAO,CAAA;AAAA,IAKPY,QAAQ,CAAA;AAAA,EAAC;AAAA,EAEXC,QAAQ;AAAA,IACN,qBAAqBC,OAAAA,aAAaX,gBAAgB;AAAA,IAClD,4BAA4BW,OAAAA,aAAaL,8BAA8B;AAAA,IACvE,sBAAsBK,OAAAA,aAAaR,yBAAyB;AAAA,EAAA;AAEhE,CAAC,EAAES,cAAc;AAAA,EACfC,IAAI;AAAA,EACJnD,SAASA,CAAC;AAAA,IAACmC;AAAAA,EAAAA,OAAY;AAAA,IACrBrC,WAAWqC,MAAMrC;AAAAA,IACjBvC,QAAQ4E,MAAM5E;AAAAA,IACd4B,MAAMgD,MAAMhD;AAAAA,EAAAA;AAAAA,EAEdiE,SAAS;AAAA,EACTC,QAAQ;AAAA,IACN,MAAQ;AAAA,MACNC,QAAQ,CACN;AAAA,QACEC,KAAK;AAAA,QACLpB,OAAOA,CAAC;AAAA,UAACnC;AAAAA,QAAAA,OAAc;AAAA,UACrBF,WAAWE,QAAQF;AAAAA,UACnBvC,QAAQyC,QAAQzC;AAAAA,UAChB4B,MAAMa,QAAQb;AAAAA,QAAAA;AAAAA,MAChB,CACD;AAAA,MAEHO,IAAI;AAAA,QACF,gBAAgB;AAAA,UACd8D,QAAQ;AAAA,UACR5B,SAAS6B,OAAAA,OAAO;AAAA,YACdC,qBAAqBA,CAAC;AAAA,cAAC7D;AAAAA,YAAAA,MAAWA,MAAM2C;AAAAA,UAAAA,CACzC;AAAA,QAAA;AAAA,MACH;AAAA,IACF;AAAA,IAEF,kBAAkB;AAAA,MAChBmB,MAAM,CACJF,OAAAA,OAAO;AAAA,QACLC,qBAAqBzD;AAAAA,MAAAA,CACtB,CAAC;AAAA,MAEJqD,QAAQ,CACN;AAAA,QACEC,KAAK;AAAA,QACLpB,OAAOA,CAAC;AAAA,UAACnC;AAAAA,QAAAA,OAAc;AAAA,UAACzC,QAAQyC,QAAQzC;AAAAA,QAAAA;AAAAA,MAAM,GAEhD;AAAA,QACEgG,KAAK;AAAA,QACLpB,OAAOA,CAAC;AAAA,UAACnC;AAAAA,QAAAA,OAAc;AAAA,UAACzC,QAAQyC,QAAQzC;AAAAA,QAAAA;AAAAA,MAAM,CAC/C;AAAA,MAEHmC,IAAI;AAAA,QACF,WAAa;AAAA,UACX8D,QAAQ;AAAA,UACR7D,OAAOA,CAAC;AAAA,YAACK;AAAAA,YAASH;AAAAA,UAAAA,MACS,CAAC+D,iBAAAA,QACxB;AAAA,YACE7C,QAAQf,QAAQ0D;AAAAA,YAChBvC,OAAOnB,QAAQ0D;AAAAA,UAAAA,GAEjB7D,MAAM6C,YACR;AAAA,QAAA;AAAA,QAKJ,mBAAmB;AAAA,UACjBc,QAAQ;AAAA,QAAA;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAEJ,CAAC,GCtOYK,kBAAkBC,eAAAA,QAAMC,WAA0B,CAAAjC,GAAAkC,QAAA;AAAA,QAAA3G,IAAAC,qBAAAA,EAAA,CAAA,GAC7DC,SAAeC,uBAEfyG,wBAA8BH,eAAAA,QAAAI,OAAa3G,MAAM;AAAC,MAAAE,IAAAC;AAAA,SAAAL,EAAA,CAAA,MAAA8G,OAAAC,IAAA,2BAAA,KAEnB3G,KAAAA,MAAMwG,sBAAqBI,SAAU3G,KAAA,CAAA,GAAEL,OAAAI,IAAAJ,OAAAK,OAAAD,KAAAJ,EAAA,CAAA,GAAAK,KAAAL,EAAA,CAAA,IAAtEyG,eAAAA,QAAAQ,oBAA0BN,KAAKvG,IAAqCC,EAAE,GAAC;AAAA,CAGxE;AACDmG,gBAAgBU,cAAc;ACsCvB,SAAAC,oBAAApH,OAAA;AAAA,QAAAC,IAAAC,qBAAAA,EAAA,CAAA,GAGLC,SAAeC,uBACfkC,KAAW+E,eAAAA,eAAerH,MAAKsC,EAAG;AAAC,MAAAjC;AAAAJ,IAAA,CAAA,MAAAE,UAAAF,SAAAqC,MAEzBjC,KAAAA,MAAA;AACR,UAAAiH,eAAqBnH,OAAMmC,GAAI,KAAKA,EAAE;AAAC,WAAA,MAAA;AAGrCgF,mBAAYC,YAAAA;AAAAA,IAAc;AAAA,EAAA,GAE7BtH,OAAAE,QAAAF,OAAAqC,IAAArC,OAAAI,MAAAA,KAAAJ,EAAA,CAAA;AAAA,MAAAK;AAAA,SAAAL,SAAAE,UAAEG,MAACH,MAAM,GAACF,OAAAE,QAAAF,OAAAK,MAAAA,KAAAL,EAAA,CAAA,GANXa,gBAAUT,IAMPC,EAAQ,GAAC;AAAA;AC5CP,SAASkH,wBAAwB1F,QAAiC;AACvE,QAAM2F,6BAA6BpF,gBAAAA,eAAe;AAAA,IAChDC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAG5B,UAFgBA,MAAMa,SAAS;AAG7B,eAAO;AAGT,YAAMoE,qBAAqB3E,6BAAAA,qBAA+BP,QAAQ,GAC5DM,iBAAiBC,+CAA4BP,QAAQ,GACrDmF,YAAY5E,6BAAAA,aAAuBP,QAAQ;AAEjD,UAAI,CAACkF,sBAAsB,CAAC5E,kBAAkB,CAAC6E;AAC7C,eAAO;AAGT,YAAMrD,uBAAuBvB,6BAAAA,wBAAkCP,QAAQ,GACjE4C,cAAcwC,iBAAAA,gCAAgC;AAAA,QAClDhF,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgB;AAAA,UACdQ,MAAM,CACJ;AAAA,YAACiE,MAAM/E,eAAegF,KAAKD;AAAAA,UAAAA,GAC3B,YACA;AAAA,YAACA,MAAMF,UAAUG,KAAKD;AAAAA,UAAAA,CAAK;AAAA,UAE7BhE,QAAQrB,SAASI,QAAQwB,WAAWL,MAAMF,UAAU;AAAA,QAAA;AAAA,MACtD,CACD;AAED,UAAIS,wBAAwB,CAACc;AAC3B,eAAO;AAGT,YAAM2C,YAAYC,iBAAAA,iBAAiBlF,eAAegF,IAAI,GAChDG,uBAAuB7C,YAAYvB,WAAW,GAC9CqE,yBAAyB,KAAKC,KAAKJ,SAAS,GAC5CK,kBAAkBtG,OAAOsG,kBAAkB5F,SAASI,OAAO;AAEjE,aACEqF,wBACAC,0BACAE,oBAAoBvF,SAEb;AAAA,QAACC;AAAAA,QAAgBuF,OAAOD;AAAAA,MAAAA,IAG1B;AAAA,IACT;AAAA,IACA5D,SAAS,CACP,MAAM,CACJC,wBAAQ;AAAA,MACNE,MAAM;AAAA,MACNrB,MAAM;AAAA,IAAA,CACP,CAAC,GAEJ,CAACoB,GAAG;AAAA,MAAC5B;AAAAA,MAAgBuF;AAAAA,IAAAA,MAAW,CAC9B5D,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO,CAAC,YAAY,OAAO;AAAA,MAC3ByD,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO;AAAA,QAACqI;AAAAA,MAAAA;AAAAA,MACR5E,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAI;AAAA,QACFE,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA,UACrBC,QAAQ;AAAA,QAAA;AAAA,QAEVE,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA,UACrBC,QAAQ;AAAA,QAAA;AAAA,MACV;AAAA,IACF,CACD,CAAC,CACH;AAAA,EAAA,CAEJ,GACKyE,cAAcjG,+BAAe;AAAA,IACjCC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,YAAM8F,cACJ9F,MAAMa,SAAS,MACX,MACAb,MAAMa,SAAS,MACb,MACAb,MAAMa,SAAS,MACb,MACAT;AAEV,UAAI0F,gBAAgB1F;AAClB,eAAO;AAGT,YAAM2F,WAAW1G,OAAO2G,uBAAuBjG,SAASI,OAAO,GACzD8F,aAAa3F,6BAAAA,kBAA4BP,QAAQ,GACjDkF,qBAAqB3E,6BAAAA,qBAA+BP,QAAQ;AAElE,UAAI,CAACgG,YAAY,CAACE,cAAc,CAAChB;AAC/B,eAAO;AAGT,YAAMpD,uBAAuBvB,6BAAAA,wBAAkCP,QAAQ,GACjEmG,aAAa5F,0CAA6BP,QAAQ,GAClDoG,iBAAiB;AAAA,QACrBjF,QAAQ;AAAA,UACNC,MAAM8E,WAAW9E;AAAAA,UACjBC,QAAQ;AAAA,QAAA;AAAA,QAEVE,OAAO;AAAA,UACLH,MAAM8E,WAAW9E;AAAAA,UACjBC,QAAQ;AAAA,QAAA;AAAA,MACV;AAGF,aACE,CAACS,wBACDqE,eAAe,GAAGJ,WAAW,GAAGA,WAAW,KAEpC;AAAA,QAACC;AAAAA,QAAUE;AAAAA,QAAYH;AAAAA,QAAaK;AAAAA,MAAAA,IAGtC;AAAA,IACT;AAAA,IACApE,SAAS,CACP,CAACE,GAAG;AAAA,MAAC6D;AAAAA,IAAAA,MAAiB,CACpB9D,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNrB,MAAMiF;AAAAA,IAAAA,CACP,CAAC,GAEJ,CAAC7D,GAAG;AAAA,MAAC8D;AAAAA,MAAUI;AAAAA,IAAAA,MAAoB,CACjCnE,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNkE,WAAW;AAAA,MACXC,OAAO;AAAA,QACLC,OAAOP,SAASQ;AAAAA,QAChB,GAAIR,SAASrF,SAAS,CAAA;AAAA,MAAC;AAAA,IACzB,CACD,GACDsB,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAImF;AAAAA,IAAAA,CACL,CAAC,CACH;AAAA,EAAA,CAEJ,GACKK,qBAAqB5G,+BAAe;AAAA,IACxCC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,YAAMa,OAAOb,MAAMyG,YAAYC,aAAaC,QAAQ,YAAY,GAC1DC,WAAW,4BACXC,eAAehG,KAAKE,MAAM6F,QAAQ,IAAI,CAAC,GACvCb,WAAW1G,OAAO2G,uBAAuBjG,SAASI,OAAO,GACzD8F,aAAa3F,6BAAAA,cAAwBP,QAAQ;AAEnD,aAAI,CAAC8G,gBAAgB,CAACd,YAAY,CAACE,aAC1B,KAGF;AAAA,QAACY;AAAAA,QAAcd;AAAAA,QAAUE;AAAAA,MAAAA;AAAAA,IAClC;AAAA,IACAlE,SAAS,CACP,CAACE,GAAG;AAAA,MAAC4E;AAAAA,IAAAA,MAAkB,CACrB7E,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNrB,MAAMgG;AAAAA,IAAAA,CACP,CAAC,GAEJ,CAAC;AAAA,MAAC9G;AAAAA,IAAAA,GAAW;AAAA,MAACgG;AAAAA,MAAUE;AAAAA,IAAAA,MACtBa,iBAAAA,YAAY/G,SAASI,SAAS8F,WAAWZ,IAAI,IACzC,CACErD,wBAAQ;AAAA,MACNE,MAAM;AAAA,MACNmE,OAAO;AAAA,QACLC,OAAOvG,SAASI,QAAQD,OAAOmG,MAAME;AAAAA,QACrCQ,UAAUd,WAAWZ,KAAK0B;AAAAA,MAAAA;AAAAA,MAE5BX,WAAW;AAAA,IAAA,CACZ,GACDpE,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNmE,OAAO;AAAA,QACLC,OAAOP,SAASQ;AAAAA,QAChB,GAAIR,SAASrF,SAAS,CAAA;AAAA,MAAC;AAAA,MAEzB0F,WAAW;AAAA,IAAA,CACZ,GACDpE,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAIiF,WAAW9E;AAAAA,IAAAA,CAChB,CAAC,IAEJ,CACEa,wBAAQ;AAAA,MACNE,MAAM;AAAA,MACNmE,OAAO;AAAA,QACLC,OAAOP,SAASQ;AAAAA,QAChB,GAAIR,SAASrF,SAAS,CAAA;AAAA,MAAC;AAAA,MAEzB0F,WAAW;AAAA,IAAA,CACZ,CAAC,CACH;AAAA,EAAA,CAEV,GACKY,0BAA0BpH,+BAAe;AAAA,IAC7CC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAG5B,UAFgBA,MAAMa,SAAS;AAG7B,eAAO;AAGT,YAAMoE,qBAAqB3E,6BAAAA,qBAA+BP,QAAQ,GAC5DM,iBAAiBC,+CAA4BP,QAAQ,GACrDmF,YAAY5E,6BAAAA,aAAuBP,QAAQ;AAEjD,UAAI,CAACkF,sBAAsB,CAAC5E,kBAAkB,CAAC6E;AAC7C,eAAO;AAGT,YAAMvC,cAAcwC,iBAAAA,gCAAgC;AAAA,QAClDhF,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgB;AAAA,UACdQ,MAAM,CACJ;AAAA,YAACiE,MAAM/E,eAAegF,KAAKD;AAAAA,UAAAA,GAC3B,YACA;AAAA,YAACA,MAAMF,UAAUG,KAAKD;AAAAA,UAAAA,CAAK;AAAA,UAE7BhE,QAAQrB,SAASI,QAAQwB,WAAWL,MAAMF,UAAU;AAAA,QAAA;AAAA,MACtD,CACD;AAED,UAAI,CAACuB;AACH,eAAO;AAGT,YAAMd,uBAAuBvB,6BAAAA,wBAAkCP,QAAQ,GACjEuF,YAAYC,kCAAiBlF,eAAegF,IAAI,GAChD4B,wBAAwB,MAAMC,KAAK5B,SAAS,GAC5C6B,QAAQF,wBACVA,sBAAsB,CAAC,EAAE5F,SACzBjB,QACEgH,yBAAyBzE,YAAYvB,WAAW+F;AAEtD,UAAItF,wBAAwB,CAACuF;AAC3B,eAAO;AAGT,YAAMxB,QACJuB,UAAU/G,SACNf,OAAOgI,eAAe;AAAA,QAACnH,QAAQH,SAASI,QAAQD;AAAAA,QAAQiH;AAAAA,MAAAA,CAAM,IAC9D/G;AAEN,aAAI+G,UAAU/G,UAAawF,UAAUxF,SAC5B;AAAA,QACLC;AAAAA,QACAuF;AAAAA,QACAuB;AAAAA,MAAAA,IAIG;AAAA,IACT;AAAA,IACApF,SAAS,CACP,CAAC;AAAA,MAAC/B;AAAAA,IAAAA,MAAW,CAACgC,gBAAAA,QAAQhC,KAAK,CAAC,GAC5B,CAACiC,GAAG;AAAA,MAAC5B;AAAAA,MAAgBuF;AAAAA,MAAOuB;AAAAA,IAAAA,MAAW,CACrCnF,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO,CAAC,YAAY,OAAO;AAAA,MAC3ByD,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO;AAAA,QAACqI;AAAAA,MAAAA;AAAAA,MACR5E,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAI;AAAA,QACFE,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA,UACrBC,QAAQ;AAAA,QAAA;AAAA,QAEVE,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA,UACrBC,QAAQ+F,QAAQ;AAAA,QAAA;AAAA,MAClB;AAAA,IACF,CACD,CAAC,CACH;AAAA,EAAA,CAEJ,GACKG,wBAAwB1H,+BAAe;AAAA,IAC3CC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,IAAAA,MAAc;AACrB,YAAMkF,qBAAqB3E,6BAAAA,qBAA+BP,QAAQ,GAC5DM,iBAAiBC,+CAA4BP,QAAQ,GACrDmF,YAAY5E,6BAAAA,aAAuBP,QAAQ;AAEjD,UAAI,CAACkF,sBAAsB,CAAC5E,kBAAkB,CAAC6E;AAC7C,eAAO;AAGT,YAAMqC,wBACJlH,eAAegF,KAAK0B,SAAS,CAAC,EAAE3B,SAASF,UAAUG,KAAKD,QACxDrF,SAASI,QAAQwB,WAAWL,MAAMF,WAAW,GAEzCoG,eAAenI,OAAOmI,eAAezH,SAASI,OAAO;AAE3D,aACEoH,yBACAC,gBACAnH,eAAegF,KAAKO,UAAU4B,eAEvB;AAAA,QAACA;AAAAA,QAAcnH;AAAAA,MAAAA,IAGjB;AAAA,IACT;AAAA,IACA0B,SAAS,CACP,CAACE,GAAG;AAAA,MAACuF;AAAAA,MAAcnH;AAAAA,IAAAA,MAAoB,CACrC2B,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO;AAAA,QAACqI,OAAO4B;AAAAA,MAAAA;AAAAA,MACfxG,IAAIX,eAAec;AAAAA,IAAAA,CACpB,CAAC,CACH;AAAA,EAAA,CAEJ,GACKsG,uBAAuB7H,+BAAe;AAAA,IAC1CC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAG5B,UAFgBA,MAAMa,SAAS;AAG7B,eAAO;AAGT,YAAMoE,qBAAqB3E,6BAAAA,qBAA+BP,QAAQ,GAC5DM,iBAAiBC,+CAA4BP,QAAQ,GACrDmF,YAAY5E,6BAAAA,aAAuBP,QAAQ;AAEjD,UAAI,CAACkF,sBAAsB,CAAC5E,kBAAkB,CAAC6E;AAC7C,eAAO;AAGT,YAAMrD,uBAAuBvB,6BAAAA,wBAAkCP,QAAQ,GACjE4C,cAAcwC,iBAAAA,gCAAgC;AAAA,QAClDhF,SAASJ,SAASI;AAAAA,QAClBQ,gBAAgB;AAAA,UACdQ,MAAM,CACJ;AAAA,YAACiE,MAAM/E,eAAegF,KAAKD;AAAAA,UAAAA,GAC3B,YACA;AAAA,YAACA,MAAMF,UAAUG,KAAKD;AAAAA,UAAAA,CAAK;AAAA,UAE7BhE,QAAQrB,SAASI,QAAQwB,WAAWL,MAAMF,UAAU;AAAA,QAAA;AAAA,MACtD,CACD;AAED,UAAIS,wBAAwB,CAACc;AAC3B,eAAO;AAGT,YAAM2C,YAAYC,iBAAAA,iBAAiBlF,eAAegF,IAAI,GAChDmC,eAAenI,OAAOmI,eAAezH,SAASI,OAAO,GACrDuH,yBAAyB,UAAUhC,KAAKJ,SAAS,GACjDqC,qBAAqBtI,OAAOsI,qBAAqB5H,SAASI,OAAO,GACjEyH,+BAA+BjF,YAAYvB,WAAW;AAE5D,UACEoG,gBACAI,gCACAF,0BACAC,uBAAuBvH;AAEvB,eAAO;AAAA,UACLC;AAAAA,UACAwH,UAAUF;AAAAA,UACVG,gBAAgB;AAAA,UAChBlC,OAAO4B;AAAAA,QAAAA;AAIX,YAAMO,uBAAuB,OAAOrC,KAAKJ,SAAS,GAC5C0C,mBAAmB3I,OAAO2I,mBAAmBjI,SAASI,OAAO,GAC7D8H,6BAA6BtF,YAAYvB,WAAW;AAE1D,aACEoG,gBACAS,8BACAF,wBACAC,qBAAqB5H,SAEd;AAAA,QACLC;AAAAA,QACAwH,UAAUG;AAAAA,QACVF,gBAAgB;AAAA,QAChBlC,OAAO4B;AAAAA,MAAAA,IAIJ;AAAA,IACT;AAAA,IACAzF,SAAS,CACP,CAAC;AAAA,MAAC/B;AAAAA,IAAAA,MAAW,CAACgC,gBAAAA,QAAQhC,KAAK,CAAC,GAC5B,CAACiC,GAAG;AAAA,MAAC5B;AAAAA,MAAgBuF;AAAAA,MAAOiC;AAAAA,MAAUC;AAAAA,IAAAA,MAAoB,CACxD9F,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACN3E,OAAO;AAAA,QACLsK;AAAAA,QACAV,OAAO;AAAA,QACPvB;AAAAA,MAAAA;AAAAA,MAEF5E,IAAIX,eAAec;AAAAA,IAAAA,CACpB,GACDa,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNlB,IAAI;AAAA,QACFE,QAAQ;AAAA,UACNC,MAAMd,eAAec;AAAAA,UACrBC,QAAQ;AAAA,QAAA;AAAA,QAEVE,OAAO;AAAA,UACLH,MAAMd,eAAec;AAAAA,UACrBC,QAAQ0G,iBAAiB;AAAA,QAAA;AAAA,MAC3B;AAAA,IACF,CACD,CAAC,CACH;AAAA,EAAA,CAEJ;AAWD,SAT0B,CACxB9C,4BACAgC,yBACAnB,aACAW,oBACAc,uBACAG,oBAAoB;AAIxB;AC/YO,SAAAS,eAAA3K,OAAA;AAAA,QAAAC,IAAAC,qBAAAA,EAAA,EAAA,GACLC,SAAeC,UAAAA,UAAAA;AAAW,MAAAC,IAAAC;AAAAL,WAAAE,UAAAF,EAAA,CAAA,MAAAD,MAAA8B,UAEhBzB,KAAAA,MAAA;AAGR,UAAAG,sBAFkBgH,wBAAwBxH,MAAK8B,MAAO,EAEjBrB,IAAAC,CAAAA,aACnCP,OAAMQ,iBAAA;AAAA,MAAAD;AAAAA,IAAAA,CAA4B,CACpC;AAAC,WAAA,MAAA;AAAA,iBAGMkK,sBAA4BpK;AAC/BoK,2BAAAA;AAAAA,IAAoB;AAAA,EAAA,GAGvBtK,KAAA,CAACH,QAAQH,MAAK8B,MAAA,GAAQ7B,OAAAE,QAAAF,EAAA,CAAA,IAAAD,MAAA8B,QAAA7B,OAAAI,IAAAJ,OAAAK,OAAAD,KAAAJ,EAAA,CAAA,GAAAK,KAAAL,EAAA,CAAA,IAZzBa,MAAAA,UAAUT,IAYPC,EAAsB;AAAC,MAAAuK;AAAA5K,IAAA,CAAA,MAAAD,MAAA8B,OAAAgJ,iBAIrBD,KAAA7K,MAAK8B,OAAAgJ,gBAAAC,2BAAAA,KAAAC,WAAAA,UAAA,EAEF,UAAA;AAAA,IAAAC,2BAAAA,IAAC,yBAAA,EACY,WAAAjL,MAAK8B,OAAAgJ,eACV,MAAA;AAAA,MAAA7J,MAAO;AAAA,MAAGC,QAAA;AAAA,IAAA,GAAY;AAAA,mCAE7B,yBAAA,EACY,WAAAlB,MAAK8B,OAAAgJ,eACV,MAAA;AAAA,MAAA7J,MAAO;AAAA,MAAGC,QAAA;AAAA,IAAA,EAAA;KAChB,IAAA,MAEEjB,EAAA,CAAA,IAAAD,MAAA8B,OAAAgJ,eAAA7K,OAAA4K,MAAAA,KAAA5K,EAAA,CAAA;AAAA,MAAAiL;AAAAjL,IAAA,CAAA,MAAAD,MAAA8B,OAAAqJ,iBACPD,KAAAlL,MAAK8B,OAAAqJ,+CACH,yBAAA,EACY,WAAAnL,MAAK8B,OAAAqJ,eACV,MAAA;AAAA,IAAAlK,MAAO;AAAA,IAAGC,QAAA;AAAA,EAAA,EAAA,KAChB,MACIjB,EAAA,CAAA,IAAAD,MAAA8B,OAAAqJ,eAAAlL,OAAAiL,MAAAA,KAAAjL,EAAA,CAAA;AAAA,MAAAmL;AAAAnL,IAAA,CAAA,MAAAD,MAAA8B,OAAAuJ,mBACPD,KAAApL,MAAK8B,OAAAuJ,kBAAAN,2BAAAA,KAAAC,WAAAA,UAAA,EAEF,UAAA;AAAA,IAAAC,2BAAAA,IAAC,yBAAA,EACY,WAAAjL,MAAK8B,OAAAuJ,iBACV,MAAA;AAAA,MAAApK,MAAO;AAAA,MAAGC,QAAA;AAAA,IAAA,GAAY;AAAA,mCAE7B,yBAAA,EACY,WAAAlB,MAAK8B,OAAAuJ,iBACV,MAAA;AAAA,MAAApK,MAAO;AAAA,MAAGC,QAAA;AAAA,IAAA,EAAA;KAChB,IAAA,MAEEjB,EAAA,CAAA,IAAAD,MAAA8B,OAAAuJ,iBAAApL,OAAAmL,MAAAA,KAAAnL,EAAA,CAAA;AAAA,MAAAqL;AAAArL,IAAA,EAAA,MAAAD,MAAA8B,OAAAyJ,0BACPD,KAAAtL,MAAK8B,OAAAyJ,wDACH,yBAAA,EACY,WAAAvL,MAAK8B,OAAAyJ,wBACV,MAAA;AAAA,IAAAtK,MAAO;AAAA,IAAGC,QAAA;AAAA,EAAA,EAAA,KAChB,MACIjB,EAAA,EAAA,IAAAD,MAAA8B,OAAAyJ,wBAAAtL,QAAAqL,MAAAA,KAAArL,EAAA,EAAA;AAAA,MAAAuL;AAAA,SAAAvL,EAAA,EAAA,MAAA4K,MAAA5K,EAAA,EAAA,MAAAiL,MAAAjL,EAAA,EAAA,MAAAmL,MAAAnL,UAAAqL,MApCVE,KAAAT,2BAAAA,KAAAC,qBAAA,EACGH,UAAAA;AAAAA,IAAAA;AAAAA,IAYAK;AAAAA,IAMAE;AAAAA,IAYAE;AAAAA,EAAAA,EAAAA,CAKO,GACPrL,QAAA4K,IAAA5K,QAAAiL,IAAAjL,QAAAmL,IAAAnL,QAAAqL,IAAArL,QAAAuL,MAAAA,KAAAvL,EAAA,EAAA,GArCHuL;AAqCG;AC3HP,MAAMC,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvBpJ,+BAAe;AAAA,IACbC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,IAAAA,MACPA,SAASI,QAAQwB,aAAarB,6BAAAA,oBAA8BP,QAAQ,IAChE;AAAA,MAAC4B,WAAW5B,SAASI,QAAQwB;AAAAA,IAAAA,IAC7B;AAAA,IACNI,SAAS,CAAC,CAACE,GAAG;AAAA,MAACN;AAAAA,IAAAA,MAAe,CAACK,gBAAAA,QAAQ;AAAA,MAACE,MAAM;AAAA,MAAUlB,IAAIW;AAAAA,IAAAA,CAAU,CAAC,CAAC;AAAA,EAAA,CACzE;AAAA;AAAA;AAAA;AAAA,EAID/B,+BAAe;AAAA,IACbC,IAAI;AAAA,IACJkC,SAAS,CAAA;AAAA,EAAA,CACV;AAAA;AAAA;AAAA;AAAA,EAIDnC,+BAAe;AAAA,IACbC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACE;AAAAA,IAAAA,MACPA,MAAMoG,cAAc,YAAYpG,MAAMoG,cAAc;AAAA,IACtDrE,SAAS,CAAA;AAAA,EAAA,CACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKDnC,+BAAe;AAAA,IACbC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAIf,EAAA,CAHqBM,+CAA4BP,QAAQ,KAIzD,CAACU,iCAAkBV,SAASI,SAASH,MAAMqG,KAAK;AAAA,IAOpDtE,SAAS,CACP,CAAC;AAAA,MAAC/B;AAAAA,IAAAA,MAAW,CACXgC,gBAAAA,QAAQ;AAAA,MACNE,MAAM;AAAA,MACNmE,OAAOrG,MAAMqG;AAAAA,MACbD,WAAW;AAAA,MACX6C,QAAQ;AAAA,IAAA,CACT,CAAC,CACH;AAAA,EAAA,CAEJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKDrJ,+BAAe;AAAA,IACbC,IAAI;AAAA,IACJkC,SAAS,CAAA;AAAA,EAAA,CACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKDnC,+BAAe;AAAA,IACbC,IAAI;AAAA,IACJC,OAAOA,CAAC;AAAA,MAACC;AAAAA,MAAUC;AAAAA,IAAAA,MAAW;AAC5B,YAAMkJ,aAAalJ,MAAMmJ,OAAOC,OAAQ/C,CAAAA,UACtC5F,qBAAAA,YAAkBV,SAASI,SAASkG,KAAK,CAC3C;AAEA,aAAI6C,WAAW7H,WAAW,IACjB,KAGF6H,WAAWG,OAAO,CAACC,aAAaC,kBAC9B9I,qCAAsB;AAAA,QAC3BN,SAASJ,SAASI;AAAAA,QAClBmJ;AAAAA,QACAC;AAAAA,MAAAA,CACD,CACF;AAAA,IACH;AAAA,IACAxH,SAAS;AAAA;AAAA;AAAA,MAGP,CAACE,GAAGoE,UAAU,CAACmD,sBAAM;AAAA,QAACtH,MAAM;AAAA,QAAgBmE;AAAAA,QAAOD,WAAW;AAAA,MAAA,CAAO,CAAC;AAAA,IAAA;AAAA,EAAC,CAE1E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKDxG,+BAAe;AAAA,IACbC,IAAI;AAAA,IACJkC,SAAS,CAAA;AAAA,EAAA,CACV;AAAC;AAaG,SAAA0H,gBAAA;AAAA,QAAAjM,IAAAC,qBAAAA,EAAA,CAAA;AAAA,MAAAG;AAAA,SAAAJ,EAAA,CAAA,MAAA8G,OAAAC,IAAA,2BAAA,KACE3G,oCAAC,gBAAA,EAA0BoL,WAAAA,iBAAAA,CAAgB,GAAIxL,OAAAI,MAAAA,KAAAJ,EAAA,CAAA,GAA/CI;AAA+C;;;;;;;"}