#!/bin/bash

# 🚨 BAKASANA SECURITY CLEANUP SCRIPT
# Usuwa hardcoded credentials z repository i konfiguruje bezpieczne środowisko

echo "🚨 BAKASANA SECURITY CLEANUP - Usuwanie hardcoded credentials..."
echo "================================================================"

# 1. Backup current .env.local (jeśli istnieje)
if [ -f ".env.local" ]; then
    echo "📋 Tworzenie backup .env.local..."
    cp .env.local .env.local.backup
    echo "✅ Backup zapisany jako .env.local.backup"
fi

# 2. Usuń .env.local z git history (NIEBEZPIECZNE - zmienia historię)
echo "🗑️  Usuwanie .env.local z git history..."
echo "⚠️  UWAGA: To zmieni historię git repository!"
read -p "<PERSON><PERSON> kont<PERSON>? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git filter-branch --force --index-filter 'git rm --cached --ignore-unmatch .env.local' HEAD
    echo "✅ .env.local usunięty z git history"
else
    echo "❌ Anulowano usuwanie z git history"
fi

# 3. Usuń .env.local z working directory
if [ -f ".env.local" ]; then
    rm .env.local
    echo "✅ .env.local usunięty z working directory"
fi

# 4. Dodaj do .gitignore (jeśli nie ma)
if ! grep -q ".env.local" .gitignore; then
    echo ".env.local" >> .gitignore
    echo "✅ .env.local dodany do .gitignore"
fi

# 5. Generuj nowe bezpieczne wartości
echo ""
echo "🔐 Generowanie nowych bezpiecznych wartości..."
echo "=============================================="

# Generuj JWT Secret
JWT_SECRET=$(openssl rand -hex 32)
echo "🔑 Nowy JWT_SECRET: $JWT_SECRET"

# Generuj Admin Password
ADMIN_PASSWORD=$(openssl rand -base64 24)
echo "🔒 Nowy ADMIN_PASSWORD: $ADMIN_PASSWORD"

# 6. Stwórz nowy .env.local z bezpiecznymi wartościami
echo ""
echo "📝 Tworzenie nowego .env.local..."
cat > .env.local << EOF
# BAKASANA - Bezpieczne zmienne środowiskowe
# Wygenerowane automatycznie $(date)

# Podstawowe ustawienia strony
NEXT_PUBLIC_SITE_NAME=bakasana-travel.blog
NEXT_PUBLIC_SITE_DESCRIPTION=Odkryj piękno Bali z nami
NEXT_PUBLIC_BASE_URL=https://bakasana-travel.blog
NEXT_PUBLIC_SITE_URL=https://bakasana-travel.blog
NEXT_PUBLIC_SITE_IMAGE_URL=/og-image.jpg

# Google Analytics (opcjonalne)
NEXT_PUBLIC_GA_ID=G-M780DCS04D
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-M780DCS04D

# Google Search Console Verification
NEXT_PUBLIC_GOOGLE_VERIFICATION=your-google-verification-code

# Bundle analyzer (wyłączony w produkcji)
ANALYZE=false

# Środowisko
NODE_ENV=development

# Sanity CMS Configuration - USTAW PRAWDZIWE WARTOŚCI!
NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-sanity-token

# Sanity Studio URL (po wdrożeniu)
NEXT_PUBLIC_SANITY_STUDIO_URL=https://bakasana-travel.sanity.studio

# BEZPIECZEŃSTWO - WYGENEROWANE AUTOMATYCZNIE
# Admin panel - bezpieczne hasło
ADMIN_PASSWORD=$ADMIN_PASSWORD

# JWT Secret - bezpieczny klucz
JWT_SECRET=$JWT_SECRET

# ConvertKit API (opcjonalne) - USTAW PRAWDZIWE KLUCZE
CONVERTKIT_API_KEY=ck_your_real_api_key_here
CONVERTKIT_FORM_ID=**********************

# Email service (Resend, SendGrid, etc.) - USTAW PRAWDZIWY KLUCZ
RESEND_API_KEY=re_your_real_resend_key_here
SMTP_HOST=smtp.resend.com
SMTP_PORT=587
SMTP_USER=resend
SMTP_PASS=re_your_real_resend_key_here
EOF

echo "✅ Nowy .env.local utworzony z bezpiecznymi wartościami"

# 7. Instrukcje dla Vercel
echo ""
echo "🚀 NASTĘPNE KROKI - KONFIGURACJA VERCEL:"
echo "======================================="
echo "1. Zaloguj się do Vercel Dashboard"
echo "2. Przejdź do Settings > Environment Variables"
echo "3. Dodaj następujące zmienne:"
echo ""
echo "JWT_SECRET=$JWT_SECRET"
echo "ADMIN_PASSWORD=$ADMIN_PASSWORD"
echo "NEXT_PUBLIC_SANITY_PROJECT_ID=your-real-sanity-project-id"
echo "NEXT_PUBLIC_SANITY_DATASET=production"
echo "SANITY_API_TOKEN=your-real-sanity-token"
echo ""
echo "4. Ustaw Environment na 'Production'"
echo "5. Kliknij 'Save'"
echo ""
echo "⚠️  WAŻNE: Zapisz te wartości w bezpiecznym miejscu!"
echo "⚠️  Nie commituj .env.local do repository!"
echo ""
echo "✅ Security cleanup zakończony pomyślnie!"
