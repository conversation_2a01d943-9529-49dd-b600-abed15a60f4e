{"name": "@architect/inventory", "version": "4.0.9", "description": "Architect project resource enumeration utility", "main": "src/index.js", "scripts": {"test": "npm run lint && npm run test:integration && npm run coverage", "test:nolint": "npm run test:integration && npm run coverage", "test:unit": "cross-env tape 'test/unit/**/*-test.js' | tap-arc", "test:integration": "cross-env tape 'test/integration/**/*-test.js' | tap-arc", "coverage": "nyc --reporter=lcov --reporter=text npm run test:unit", "lint": "eslint . --fix", "rc": "npm version prerelease --preid RC", "vendor": "scripts/vendor"}, "engines": {"node": ">=16"}, "repository": {"type": "git", "url": "git+https://github.com/architect/inventory.git"}, "license": "Apache-2.0", "dependencies": {"@architect/asap": "~7.0.10", "@architect/parser": "~7.0.1", "@architect/utils": "~4.0.6", "@aws-lite/client": "^0.21.1", "@aws-lite/ssm": "^0.2.3", "lambda-runtimes": "~2.0.5"}, "devDependencies": {"@architect/eslint-config": "~3.0.0", "cross-env": "~7.0.3", "dotenv": "~16.4.5", "eslint": "~9.1.1", "mock-tmp": "~0.0.4", "nyc": "~15.1.0", "proxyquire": "~2.1.3", "tap-arc": "^1.2.2", "tape": "^5.7.5"}, "nyc": {"check-coverage": true, "branches": 100, "lines": 100, "functions": 100, "statements": 100}}