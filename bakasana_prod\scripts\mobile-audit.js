#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const glob = require('glob');

console.log('🔍 BAKASANA Mobile Responsiveness Audit\n');

// Configuration
const srcDir = path.join(__dirname, '../src');
const auditResults = {
  pages: [],
  components: [],
  issues: [],
  recommendations: [],
};

// Responsive breakpoints to check for
const breakpoints = ['sm:', 'md:', 'lg:', 'xl:', '2xl:'];
const mobileFirstClasses = [
  'text-xs',
  'text-sm',
  'text-base',
  'text-lg',
  'text-xl',
  'p-1',
  'p-2',
  'p-3',
  'p-4',
  'px-2',
  'px-4',
  'py-2',
  'py-4',
  'gap-1',
  'gap-2',
  'gap-4',
  'gap-6',
  'gap-8',
  'grid-cols-1',
  'grid-cols-2',
  'grid-cols-3',
  'flex-col',
  'flex-row',
  'hidden',
  'block',
  'inline-block',
];

// Touch target minimum sizes (44px for mobile)
const touchTargetClasses = [
  'min-h-\\[44px\\]',
  'h-11',
  'h-12',
  'h-14',
  'h-16',
  'min-w-\\[44px\\]',
  'w-11',
  'w-12',
  'w-14',
  'w-16',
];

function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(srcDir, filePath);

  const analysis = {
    file: relativePath,
    hasResponsiveClasses: false,
    breakpointsUsed: [],
    mobileFirstApproach: false,
    touchTargetCompliant: false,
    issues: [],
    score: 0,
  };

  // Check for responsive breakpoints
  breakpoints.forEach(bp => {
    if (content.includes(bp)) {
      analysis.hasResponsiveClasses = true;
      analysis.breakpointsUsed.push(bp.replace(':', ''));
    }
  });

  // Check for mobile-first approach
  const hasMobileClasses = mobileFirstClasses.some(
    cls =>
      content.includes(cls) ||
      content.includes(`"${cls}"`) ||
      content.includes(`'${cls}'`)
  );
  analysis.mobileFirstApproach = hasMobileClasses;

  // Check for touch targets
  const hasTouchTargets = touchTargetClasses.some(
    cls => content.includes(cls) || new RegExp(cls).test(content)
  );
  analysis.touchTargetCompliant = hasTouchTargets;

  // Specific checks for common issues
  if (
    content.includes('fixed') &&
    !content.includes('sm:') &&
    !content.includes('md:')
  ) {
    analysis.issues.push('Fixed positioning without responsive variants');
  }

  if (
    content.includes('text-xs') &&
    !content.includes('sm:text-') &&
    !content.includes('md:text-')
  ) {
    analysis.issues.push('Very small text without responsive scaling');
  }

  if (
    content.includes('overflow-hidden') &&
    content.includes('max-w-') &&
    !analysis.hasResponsiveClasses
  ) {
    analysis.issues.push(
      'Container with overflow but no responsive breakpoints'
    );
  }

  // Check for viewport meta tag (in layout files)
  if (filePath.includes('layout.jsx') || filePath.includes('layout.tsx')) {
    if (!content.includes('viewport') && !content.includes('device-width')) {
      analysis.issues.push('Missing viewport meta tag');
    }
  }

  // Calculate score
  let score = 0;
  if (analysis.hasResponsiveClasses) score += 30;
  if (analysis.breakpointsUsed.length >= 2) score += 20;
  if (analysis.mobileFirstApproach) score += 25;
  if (analysis.touchTargetCompliant) score += 15;
  if (analysis.issues.length === 0) score += 10;

  analysis.score = score;

  return analysis;
}

function auditPages() {
  console.log('📱 Auditing Pages...');

  const pageFiles = glob.sync(
    path.join(srcDir, 'app/**/page.jsx').replace(/\\/g, '/')
  );

  pageFiles.forEach(file => {
    const analysis = analyzeFile(file);
    auditResults.pages.push(analysis);

    if (analysis.score < 60) {
      auditResults.issues.push(
        `Low mobile score (${analysis.score}/100): ${analysis.file}`
      );
    }
  });

  console.log(`✅ Analyzed ${pageFiles.length} pages`);
}

function auditComponents() {
  console.log('🧩 Auditing Components...');

  const componentFiles = glob.sync(
    path.join(srcDir, 'components/**/*.jsx').replace(/\\/g, '/')
  );

  componentFiles.forEach(file => {
    const analysis = analyzeFile(file);
    auditResults.components.push(analysis);

    if (analysis.score < 50) {
      auditResults.issues.push(
        `Component needs mobile optimization (${analysis.score}/100): ${analysis.file}`
      );
    }
  });

  console.log(`✅ Analyzed ${componentFiles.length} components`);
}

function generateRecommendations() {
  console.log('💡 Generating Recommendations...');

  const lowScorePages = auditResults.pages.filter(p => p.score < 70);
  const lowScoreComponents = auditResults.components.filter(c => c.score < 60);

  if (lowScorePages.length > 0) {
    auditResults.recommendations.push({
      type: 'Pages',
      priority: 'High',
      message: `${lowScorePages.length} pages need mobile optimization`,
      files: lowScorePages.map(p => p.file),
    });
  }

  if (lowScoreComponents.length > 0) {
    auditResults.recommendations.push({
      type: 'Components',
      priority: 'Medium',
      message: `${lowScoreComponents.length} components need mobile optimization`,
      files: lowScoreComponents.map(c => c.file),
    });
  }

  // Check for missing breakpoints
  const allBreakpoints = new Set();
  [...auditResults.pages, ...auditResults.components].forEach(item => {
    item.breakpointsUsed.forEach(bp => allBreakpoints.add(bp));
  });

  const missingBreakpoints = breakpoints
    .map(bp => bp.replace(':', ''))
    .filter(bp => !allBreakpoints.has(bp));
  if (missingBreakpoints.length > 0) {
    auditResults.recommendations.push({
      type: 'Breakpoints',
      priority: 'Low',
      message: `Consider using these breakpoints: ${missingBreakpoints.join(', ')}`,
      files: [],
    });
  }
}

function printResults() {
  console.log('\n📊 MOBILE RESPONSIVENESS AUDIT RESULTS\n');

  // Summary
  const totalFiles = auditResults.pages.length + auditResults.components.length;
  const avgScore = Math.round(
    [...auditResults.pages, ...auditResults.components].reduce(
      (sum, item) => sum + item.score,
      0
    ) / totalFiles
  );

  console.log(`📈 Overall Score: ${avgScore}/100`);
  console.log(`📄 Files Analyzed: ${totalFiles}`);
  console.log(`⚠️  Issues Found: ${auditResults.issues.length}`);
  console.log(`💡 Recommendations: ${auditResults.recommendations.length}\n`);

  // Top performing files
  const topFiles = [...auditResults.pages, ...auditResults.components]
    .sort((a, b) => b.score - a.score)
    .slice(0, 5);

  console.log('🏆 TOP PERFORMING FILES:');
  topFiles.forEach(file => {
    console.log(`   ${file.score}/100 - ${file.file}`);
  });

  // Issues
  if (auditResults.issues.length > 0) {
    console.log('\n⚠️  ISSUES FOUND:');
    auditResults.issues.forEach(issue => {
      console.log(`   • ${issue}`);
    });
  }

  // Recommendations
  if (auditResults.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    auditResults.recommendations.forEach(rec => {
      console.log(`   [${rec.priority}] ${rec.message}`);
      if (rec.files.length > 0 && rec.files.length <= 5) {
        rec.files.forEach(file => console.log(`     - ${file}`));
      } else if (rec.files.length > 5) {
        console.log(`     - ${rec.files.length} files affected`);
      }
    });
  }

  // Breakpoint usage
  const breakpointUsage = {};
  [...auditResults.pages, ...auditResults.components].forEach(item => {
    item.breakpointsUsed.forEach(bp => {
      breakpointUsage[bp] = (breakpointUsage[bp] || 0) + 1;
    });
  });

  console.log('\n📱 BREAKPOINT USAGE:');
  Object.entries(breakpointUsage)
    .sort(([, a], [, b]) => b - a)
    .forEach(([bp, count]) => {
      console.log(`   ${bp}: ${count} files`);
    });
}

function saveReport() {
  const reportPath = path.join(__dirname, '../mobile-audit-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2));
  console.log(`\n💾 Detailed report saved to: ${reportPath}`);
}

// Run audit
async function runAudit() {
  try {
    auditPages();
    auditComponents();
    generateRecommendations();
    printResults();
    saveReport();

    console.log('\n✅ Mobile responsiveness audit completed!');

    // Exit with error code if critical issues found
    const criticalIssues = auditResults.issues.filter(
      issue =>
        issue.includes('Low mobile score') || issue.includes('Missing viewport')
    );

    if (criticalIssues.length > 0) {
      console.log('\n❌ Critical mobile issues found. Please address them.');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Audit failed:', error.message);
    process.exit(1);
  }
}

runAudit();
