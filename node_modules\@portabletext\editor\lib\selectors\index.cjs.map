{"version": 3, "file": "index.cjs", "sources": ["../../src/selectors/selector.get-anchor-block.ts", "../../src/selectors/selector.get-anchor-text-block.ts", "../../src/selectors/selector.get-anchor-child.ts", "../../src/selectors/selector.get-anchor-span.ts", "../../src/selectors/selector.get-block-offsets.ts", "../../src/selectors/selector.get-list-state.ts", "../../src/selectors/selector.get-selected-slice.ts", "../../src/selectors/selector.get-selection.ts", "../../src/selectors/selector.get-selection-end-child.ts", "../../src/selectors/selector.get-selection-start-child.ts", "../../src/selectors/selector.get-value.ts"], "sourcesContent": ["import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {getBlockKeyFromSelectionPoint} from '../selection/selection-point'\nimport type {BlockPath} from '../types/paths'\n\n/**\n * @public\n */\nexport const getAnchorBlock: EditorSelector<\n  {node: PortableTextBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  const key = getBlockKeyFromSelectionPoint(snapshot.context.selection.anchor)\n  const index = key ? snapshot.blockIndexMap.get(key) : undefined\n  const node =\n    index !== undefined ? snapshot.context.value.at(index) : undefined\n\n  return node && key ? {node, path: [{_key: key}]} : undefined\n}\n", "import type {PortableTextTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport type {BlockPath} from '../types/paths'\nimport {getAnchorBlock} from './selector.get-anchor-block'\n\n/**\n * @public\n */\nexport const getAnchorTextBlock: EditorSelector<\n  {node: PortableTextTextBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  const anchorBlock = getAnchorBlock(snapshot)\n\n  return anchorBlock && isTextBlock(snapshot.context, anchorBlock.node)\n    ? {node: anchorBlock.node, path: anchorBlock.path}\n    : undefined\n}\n", "import type {PortableTextObject, PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {getChildKeyFromSelectionPoint} from '../selection/selection-point'\nimport type {ChildPath} from '../types/paths'\nimport {getAnchorTextBlock} from './selector.get-anchor-text-block'\n\n/**\n * @public\n */\nexport const getAnchorChild: EditorSelector<\n  | {\n      node: PortableTextObject | PortableTextSpan\n      path: ChildPath\n    }\n  | undefined\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  const anchorBlock = getAnchorTextBlock(snapshot)\n\n  if (!anchorBlock) {\n    return undefined\n  }\n\n  const key = getChildKeyFromSelectionPoint(snapshot.context.selection.anchor)\n\n  const node = key\n    ? anchorBlock.node.children.find((span) => span._key === key)\n    : undefined\n\n  return node && key\n    ? {node, path: [...anchorBlock.path, 'children', {_key: key}]}\n    : undefined\n}\n", "import {isPortableTextSpan, type PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {ChildPath} from '../types/paths'\nimport {getAnchorChild} from './selector.get-anchor-child'\n\n/**\n * @public\n */\nexport const getAnchorSpan: EditorSelector<\n  {node: PortableTextSpan; path: ChildPath} | undefined\n> = (snapshot) => {\n  const anchorChild = getAnchorChild(snapshot)\n\n  return anchorChild && isPortableTextSpan(anchorChild.node)\n    ? {node: anchorChild.node, path: anchorChild.path}\n    : undefined\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockOffset} from '../types/block-offset'\nimport * as utils from '../utils'\nimport {getSelectionEndPoint} from './selector.get-selection-end-point'\nimport {getSelectionStartPoint} from './selector.get-selection-start-point'\n\n/**\n * @public\n */\nexport const getBlockOffsets: EditorSelector<\n  {start: BlockOffset; end: BlockOffset} | undefined\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  const selectionStartPoint = getSelectionStartPoint(snapshot)\n  const selectionEndPoint = getSelectionEndPoint(snapshot)\n\n  if (!selectionStartPoint || !selectionEndPoint) {\n    return undefined\n  }\n\n  const start = utils.spanSelectionPointToBlockOffset({\n    context: snapshot.context,\n    selectionPoint: selectionStartPoint,\n  })\n  const end = utils.spanSelectionPointToBlockOffset({\n    context: snapshot.context,\n    selectionPoint: selectionEndPoint,\n  })\n\n  return start && end ? {start, end} : undefined\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport type {BlockPath} from '../types/paths'\nimport {getFocusTextBlock} from './selector.get-focus-text-block'\n\n/**\n * @beta\n * @deprecated Use the precomputed `data-list-index` on text blocks instead.\n * Given the `path` of a block, this selector will return the \"list index\" of\n * the block.\n */\nexport function getListIndex({\n  path,\n}: {\n  path: BlockPath\n}): EditorSelector<number | undefined> {\n  return (snapshot) => {\n    const selection = {\n      anchor: {\n        path,\n        offset: 0,\n      },\n      focus: {\n        path,\n        offset: 0,\n      },\n    }\n\n    const focusTextBlock = getFocusTextBlock({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection,\n      },\n    })\n\n    if (!focusTextBlock) {\n      return undefined\n    }\n\n    if (\n      focusTextBlock.node.listItem === undefined ||\n      focusTextBlock.node.level === undefined\n    ) {\n      return undefined\n    }\n\n    const targetListItem = focusTextBlock.node.listItem\n    const targetLevel = focusTextBlock.node.level\n    const targetKey = focusTextBlock.node._key\n\n    // Find the target block's index\n    const targetIndex = snapshot.blockIndexMap.get(targetKey)\n\n    if (targetIndex === undefined) {\n      return undefined\n    }\n\n    // Walk backwards from the target block and count consecutive list items\n    // of the same type and level\n    let listIndex = 1 // Start at 1 for the target block itself\n\n    for (let i = targetIndex - 1; i >= 0; i--) {\n      const block = snapshot.context.value[i]\n\n      if (!isTextBlock(snapshot.context, block)) {\n        // Non-text block breaks the sequence\n        break\n      }\n\n      if (block.listItem === undefined || block.level === undefined) {\n        // Non-list item breaks the sequence\n        break\n      }\n\n      if (block.listItem !== targetListItem) {\n        // Different list type breaks the sequence\n        break\n      }\n\n      if (block.level < targetLevel) {\n        // Lower level breaks the sequence\n        break\n      }\n\n      if (block.level === targetLevel) {\n        // Same level - continue counting\n        listIndex++\n      }\n\n      // Higher level items don't affect the count for the target level\n    }\n\n    return listIndex\n  }\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {getSelectedValue} from './selector.get-selected-value'\n\n/**\n * @public\n * @deprecated Renamed to `getSelectedValue`.\n */\nexport const getSelectedSlice: EditorSelector<Array<PortableTextBlock>> = (\n  snapshot,\n) => {\n  return getSelectedValue(snapshot)\n}\n", "import type {EditorSelection} from '..'\nimport type {EditorSelector} from '../editor/editor-selector'\n\n/**\n * @public\n */\nexport const getSelection: EditorSelector<EditorSelection> = (snapshot) => {\n  return snapshot.context.selection\n}\n", "import type {PortableTextObject, PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {ChildPath} from '../types/paths'\nimport {getSelectionEndPoint} from '../utils/util.get-selection-end-point'\nimport {getFocusChild} from './selector.get-focus-child'\n\n/**\n * @public\n */\nexport const getSelectionEndChild: EditorSelector<\n  | {\n      node: PortableTextSpan | PortableTextObject\n      path: ChildPath\n    }\n  | undefined\n> = (snapshot) => {\n  const endPoint = getSelectionEndPoint(snapshot.context.selection)\n\n  if (!endPoint) {\n    return undefined\n  }\n\n  return getFocusChild({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: {\n        anchor: endPoint,\n        focus: endPoint,\n      },\n    },\n  })\n}\n", "import type {PortableTextObject, PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {ChildPath} from '../types/paths'\nimport {getSelectionStartPoint} from '../utils/util.get-selection-start-point'\nimport {getFocusChild} from './selector.get-focus-child'\n\n/**\n * @public\n */\nexport const getSelectionStartChild: EditorSelector<\n  | {\n      node: PortableTextSpan | PortableTextObject\n      path: ChildPath\n    }\n  | undefined\n> = (snapshot) => {\n  const startPoint = getSelectionStartPoint(snapshot.context.selection)\n\n  if (!startPoint) {\n    return undefined\n  }\n\n  return getFocusChild({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: {\n        anchor: startPoint,\n        focus: startPoint,\n      },\n    },\n  })\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\n\n/**\n * @public\n */\nexport const getValue: EditorSelector<Array<PortableTextBlock>> = (\n  snapshot,\n) => {\n  return snapshot.context.value\n}\n"], "names": ["getAnchorBlock", "snapshot", "context", "selection", "key", "getBlockKeyFromSelectionPoint", "anchor", "index", "blockIndexMap", "get", "undefined", "node", "value", "at", "path", "_key", "getAnchorTextBlock", "anchorBlock", "isTextBlock", "getAnchor<PERSON><PERSON>d", "getChildKeyFromSelectionPoint", "children", "find", "span", "getAnchorSpan", "anchor<PERSON><PERSON><PERSON>", "isPortableTextSpan", "getBlockOffsets", "selectionStartPoint", "getSelectionStartPoint", "selectionEndPoint", "getSelectionEndPoint", "start", "utils", "selectionPoint", "end", "getListIndex", "offset", "focus", "focusTextBlock", "getFocusTextBlock", "listItem", "level", "targetListItem", "targetLevel", "<PERSON><PERSON><PERSON>", "targetIndex", "listIndex", "i", "block", "getSelectedSlice", "getSelectedValue", "getSelection", "getSelectionEndChild", "endPoint", "getFocusChild", "getSelectionStartChild", "startPoint", "getValue"], "mappings": ";;;AAQO,MAAMA,iBAERC,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASC,QAAQC;AACpB;AAGF,QAAMC,MAAMC,iBAAAA,8BAA8BJ,SAASC,QAAQC,UAAUG,MAAM,GACrEC,QAAQH,MAAMH,SAASO,cAAcC,IAAIL,GAAG,IAAIM,QAChDC,OACJJ,UAAUG,SAAYT,SAASC,QAAQU,MAAMC,GAAGN,KAAK,IAAIG;AAE3D,SAAOC,QAAQP,MAAM;AAAA,IAACO;AAAAA,IAAMG,MAAM,CAAC;AAAA,MAACC,MAAMX;AAAAA,IAAAA,CAAI;AAAA,EAAA,IAAKM;AACrD,GCZaM,qBAERf,CAAAA,aAAa;AAChB,QAAMgB,cAAcjB,eAAeC,QAAQ;AAE3C,SAAOgB,eAAeC,iBAAAA,YAAYjB,SAASC,SAASe,YAAYN,IAAI,IAChE;AAAA,IAACA,MAAMM,YAAYN;AAAAA,IAAMG,MAAMG,YAAYH;AAAAA,EAAAA,IAC3CJ;AACN,GCRaS,iBAMRlB,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASC,QAAQC;AACpB;AAGF,QAAMc,cAAcD,mBAAmBf,QAAQ;AAE/C,MAAI,CAACgB;AACH;AAGF,QAAMb,MAAMgB,iBAAAA,8BAA8BnB,SAASC,QAAQC,UAAUG,MAAM,GAErEK,OAAOP,MACTa,YAAYN,KAAKU,SAASC,KAAMC,UAASA,KAAKR,SAASX,GAAG,IAC1DM;AAEJ,SAAOC,QAAQP,MACX;AAAA,IAACO;AAAAA,IAAMG,MAAM,CAAC,GAAGG,YAAYH,MAAM,YAAY;AAAA,MAACC,MAAMX;AAAAA,IAAAA,CAAI;AAAA,EAAA,IAC1DM;AACN,GC3Bac,gBAERvB,CAAAA,aAAa;AAChB,QAAMwB,cAAcN,eAAelB,QAAQ;AAE3C,SAAOwB,eAAeC,MAAAA,mBAAmBD,YAAYd,IAAI,IACrD;AAAA,IAACA,MAAMc,YAAYd;AAAAA,IAAMG,MAAMW,YAAYX;AAAAA,EAAAA,IAC3CJ;AACN,GCPaiB,kBAER1B,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASC,QAAQC;AACpB;AAGF,QAAMyB,sBAAsBC,6BAAAA,uBAAuB5B,QAAQ,GACrD6B,oBAAoBC,iCAAAA,qBAAqB9B,QAAQ;AAEvD,MAAI,CAAC2B,uBAAuB,CAACE;AAC3B;AAGF,QAAME,QAAQC,iBAAAA,gCAAsC;AAAA,IAClD/B,SAASD,SAASC;AAAAA,IAClBgC,gBAAgBN;AAAAA,EAAAA,CACjB,GACKO,MAAMF,iDAAsC;AAAA,IAChD/B,SAASD,SAASC;AAAAA,IAClBgC,gBAAgBJ;AAAAA,EAAAA,CACjB;AAED,SAAOE,SAASG,MAAM;AAAA,IAACH;AAAAA,IAAOG;AAAAA,EAAAA,IAAOzB;AACvC;ACtBO,SAAS0B,aAAa;AAAA,EAC3BtB;AAGF,GAAuC;AACrC,SAAQb,CAAAA,aAAa;AACnB,UAAME,YAAY;AAAA,MAChBG,QAAQ;AAAA,QACNQ;AAAAA,QACAuB,QAAQ;AAAA,MAAA;AAAA,MAEVC,OAAO;AAAA,QACLxB;AAAAA,QACAuB,QAAQ;AAAA,MAAA;AAAA,IACV,GAGIE,iBAAiBC,6BAAAA,kBAAkB;AAAA,MACvC,GAAGvC;AAAAA,MACHC,SAAS;AAAA,QACP,GAAGD,SAASC;AAAAA,QACZC;AAAAA,MAAAA;AAAAA,IACF,CACD;AAMD,QAJI,CAACoC,kBAKHA,eAAe5B,KAAK8B,aAAa/B,UACjC6B,eAAe5B,KAAK+B,UAAUhC;AAE9B;AAGF,UAAMiC,iBAAiBJ,eAAe5B,KAAK8B,UACrCG,cAAcL,eAAe5B,KAAK+B,OAClCG,YAAYN,eAAe5B,KAAKI,MAGhC+B,cAAc7C,SAASO,cAAcC,IAAIoC,SAAS;AAExD,QAAIC,gBAAgBpC;AAClB;AAKF,QAAIqC,YAAY;AAEhB,aAASC,IAAIF,cAAc,GAAGE,KAAK,GAAGA,KAAK;AACzC,YAAMC,QAAQhD,SAASC,QAAQU,MAAMoC,CAAC;AAiBtC,UAfI,CAAC9B,iBAAAA,YAAYjB,SAASC,SAAS+C,KAAK,KAKpCA,MAAMR,aAAa/B,UAAauC,MAAMP,UAAUhC,UAKhDuC,MAAMR,aAAaE,kBAKnBM,MAAMP,QAAQE;AAEhB;AAGEK,YAAMP,UAAUE,eAElBG;AAAAA,IAIJ;AAEA,WAAOA;AAAAA,EACT;AACF;ACvFO,MAAMG,mBACXjD,CAAAA,aAEOkD,6BAAAA,iBAAiBlD,QAAQ,GCLrBmD,eAAiDnD,CAAAA,aACrDA,SAASC,QAAQC,WCEbkD,uBAMRpD,CAAAA,aAAa;AAChB,QAAMqD,WAAWvB,iBAAAA,qBAAqB9B,SAASC,QAAQC,SAAS;AAEhE,MAAKmD;AAIL,WAAOC,2CAAc;AAAA,MACnB,GAAGtD;AAAAA,MACHC,SAAS;AAAA,QACP,GAAGD,SAASC;AAAAA,QACZC,WAAW;AAAA,UACTG,QAAQgD;AAAAA,UACRhB,OAAOgB;AAAAA,QAAAA;AAAAA,MACT;AAAA,IACF,CACD;AACH,GCvBaE,yBAMRvD,CAAAA,aAAa;AAChB,QAAMwD,aAAa5B,iBAAAA,uBAAuB5B,SAASC,QAAQC,SAAS;AAEpE,MAAKsD;AAIL,WAAOF,2CAAc;AAAA,MACnB,GAAGtD;AAAAA,MACHC,SAAS;AAAA,QACP,GAAGD,SAASC;AAAAA,QACZC,WAAW;AAAA,UACTG,QAAQmD;AAAAA,UACRnB,OAAOmB;AAAAA,QAAAA;AAAAA,MACT;AAAA,IACF,CACD;AACH,GC1BaC,WACXzD,CAAAA,aAEOA,SAASC,QAAQU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}