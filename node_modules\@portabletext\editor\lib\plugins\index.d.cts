import { <PERSON><PERSON><PERSON>, Editor, EditorEmitted<PERSON>vent, EditorSchema } from "../_chunks-dts/behavior.types.action.cjs";
import * as react22 from "react";
import React from "react";
/**
 * @beta
 */
declare function BehaviorPlugin(props: {
  behaviors: Array<Behavior>;
}): null;
/**
 * @beta
 * @deprecated Install the plugin from `@portabletext/plugin-character-pair-decorator`
 */
declare function DecoratorShortcutPlugin(config: {
  decorator: ({
    schema
  }: {
    schema: EditorSchema;
  }) => string | undefined;
  pair: {
    char: string;
    amount: number;
  };
}): null;
/**
 * @beta
 */
declare const EditorRefPlugin: React.ForwardRefExoticComponent<React.RefAttributes<Editor | null>>;
/**
 * @public
 * Listen for events emitted by the editor. Must be used inside `EditorProvider`. Events available include:
 *  - 'blurred'
 *  - 'done loading'
 *  - 'editable'
 *  - 'error'
 *  - 'focused'
 *  - 'invalid value'
 *  - 'loading'
 *  - 'mutation'
 *  - 'patch'
 *  - 'read only'
 *  - 'ready'
 *  - 'selection'
 *  - 'value changed'
 *
 * @example
 * Listen and log events.
 * ```tsx
 * import {EditorProvider} from '@portabletext/editor'
 * import {EventListenerPlugin} from '@portabletext/editor/plugins'
 *
 * function MyComponent() {
 *  return (
 *  <EditorProvider>
 *   <EventListenerPlugin
 *    on={(event) => {
 *     console.log(event)
 *    }
 *   } />
 *   { ... }
 * </EditorProvider>
 *  )
 * }
 * ```
 * @example
 * Handle events when there is a mutation.
 * ```tsx
 * <EventListenerPlugin
 *  on={(event) => {
 *    if (event.type === 'mutation') {
 *      console.log('Value changed:', event.snapshot)
 *    }
 *  }}
 * />
 * ```
 * @group Components
 */
declare function EventListenerPlugin(props: {
  on: (event: EditorEmittedEvent) => void;
}): null;
type MarkdownBehaviorsConfig = {
  horizontalRuleObject?: (context: {
    schema: EditorSchema;
  }) => {
    name: string;
    value?: {
      [prop: string]: unknown;
    };
  } | undefined;
  defaultStyle?: (context: {
    schema: EditorSchema;
  }) => string | undefined;
  headingStyle?: (context: {
    schema: EditorSchema;
    level: number;
  }) => string | undefined;
  blockquoteStyle?: (context: {
    schema: EditorSchema;
  }) => string | undefined;
  unorderedListStyle?: (context: {
    schema: EditorSchema;
  }) => string | undefined;
  orderedListStyle?: (context: {
    schema: EditorSchema;
  }) => string | undefined;
};
/**
 * @beta
 */
type MarkdownPluginConfig = MarkdownBehaviorsConfig & {
  boldDecorator?: ({
    schema
  }: {
    schema: EditorSchema;
  }) => string | undefined;
  codeDecorator?: ({
    schema
  }: {
    schema: EditorSchema;
  }) => string | undefined;
  italicDecorator?: ({
    schema
  }: {
    schema: EditorSchema;
  }) => string | undefined;
  strikeThroughDecorator?: ({
    schema
  }: {
    schema: EditorSchema;
  }) => string | undefined;
};
/**
 * @beta
 * Add markdown behaviors for common markdown actions such as converting ### to headings, --- to HRs, and more.
 *
 * @example
 * Configure the bundled markdown behaviors
 * ```ts
 * import {EditorProvider} from '@portabletext/editor'
 * import {MarkdownPlugin} from '@portabletext/editor/plugins'
 *
 * function App() {
 *   return (
 *    <EditorProvider>
 *      <MarkdownPlugin
 *        config={{
 *          boldDecorator: ({schema}) =>
 *            schema.decorators.find((decorator) => decorator.value === 'strong')?.value,
 *          codeDecorator: ({schema}) =>
 *            schema.decorators.find((decorator) => decorator.value === 'code')?.value,
 *          italicDecorator: ({schema}) =>
 *            schema.decorators.find((decorator) => decorator.value === 'em')?.value,
 *          strikeThroughDecorator: ({schema}) =>
 *            schema.decorators.find((decorator) => decorator.value === 'strike-through')?.value,
 *          horizontalRuleObject: ({schema}) => {
 *            const name = schema.blockObjects.find(
 *              (object) => object.name === 'break',
 *            )?.name
 *            return name ? {name} : undefined
 *          },
 *          defaultStyle: ({schema}) => schema.styles[0].value,
 *          headingStyle: ({schema, level}) =>
 *            schema.styles.find((style) => style.value === `h${level}`)
 *              ?.value,
 *          blockquoteStyle: ({schema}) =>
 *            schema.styles.find((style) => style.value === 'blockquote')
 *              ?.value,
 *          unorderedListStyle: ({schema}) =>
 *            schema.lists.find((list) => list.value === 'bullet')?.value,
 *          orderedListStyle: ({schema}) =>
 *            schema.lists.find((list) => list.value === 'number')?.value,
 *        }}
 *      />
 *      {...}
 *    </EditorProvider>
 *  )
 * }
 * ```
 *
 * @deprecated Install the plugin from `@portabletext/plugin-markdown-shortcuts`
 */
declare function MarkdownPlugin(props: {
  config: MarkdownPluginConfig;
}): react22.JSX.Element;
/**
 * @beta
 * Restrict the editor to one line. The plugin takes care of blocking
 * `insert.break` events and smart handling of other `insert.*` events.
 *
 * Place it with as high priority as possible to make sure other plugins don't
 * overwrite `insert.*` events before this plugin gets a chance to do so.
 *
 * @deprecated Install the plugin from `@portabletext/plugin-one-line`
 */
declare function OneLinePlugin(): react22.JSX.Element;
export { BehaviorPlugin, DecoratorShortcutPlugin, EditorRefPlugin, EventListenerPlugin, MarkdownPlugin, type MarkdownPluginConfig, OneLinePlugin };