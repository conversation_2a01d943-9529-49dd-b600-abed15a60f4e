"use strict";
var util_sliceBlocks = require("./util.slice-blocks.cjs"), util_isSelectionCollapsed = require("./util.is-selection-collapsed.cjs"), selector_isSelectionExpanded = require("./selector.is-selection-expanded.cjs"), types = require("@sanity/types");
function isSelectionExpanded(selection) {
  return selection ? !util_isSelectionCollapsed.isSelectionCollapsed(selection) : !1;
}
const getSelectionEndBlock = (snapshot) => {
  const endPoint = util_sliceBlocks.getSelectionEndPoint(snapshot.context.selection);
  if (endPoint)
    return selector_isSelectionExpanded.getFocusBlock({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: endPoint,
          focus: endPoint
        }
      }
    });
}, getSelectionEndPoint = (snapshot) => {
  if (snapshot.context.selection)
    return snapshot.context.selection.backward ? snapshot.context.selection.anchor : snapshot.context.selection.focus;
}, getNextSpan = (snapshot) => {
  const selectionEndBlock = getSelectionEndBlock(snapshot), selectionEndPoint = getSelectionEndPoint(snapshot);
  if (!selectionEndBlock || !selectionEndPoint || !util_sliceBlocks.isTextBlock(snapshot.context, selectionEndBlock.node))
    return;
  const selectionEndPointChildKey = util_sliceBlocks.getChildKeyFromSelectionPoint(selectionEndPoint);
  let endPointChildFound = !1, nextSpan;
  for (const child of selectionEndBlock.node.children) {
    if (child._key === selectionEndPointChildKey) {
      endPointChildFound = !0;
      continue;
    }
    if (util_sliceBlocks.isSpan(snapshot.context, child) && endPointChildFound) {
      nextSpan = {
        node: child,
        path: [...selectionEndBlock.path, "children", {
          _key: child._key
        }]
      };
      break;
    }
  }
  return nextSpan;
}, getSelectionStartBlock = (snapshot) => {
  const startPoint = util_sliceBlocks.getSelectionStartPoint(snapshot.context.selection);
  if (startPoint)
    return selector_isSelectionExpanded.getFocusBlock({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: startPoint,
          focus: startPoint
        }
      }
    });
}, getPreviousSpan = (snapshot) => {
  const selectionStartBlock = getSelectionStartBlock(snapshot), selectionStartPoint = selector_isSelectionExpanded.getSelectionStartPoint(snapshot);
  if (!selectionStartBlock || !selectionStartPoint || !util_sliceBlocks.isTextBlock(snapshot.context, selectionStartBlock.node))
    return;
  const selectionStartPointChildKey = util_sliceBlocks.getChildKeyFromSelectionPoint(selectionStartPoint);
  let previousSpan;
  for (const child of selectionStartBlock.node.children) {
    if (child._key === selectionStartPointChildKey)
      break;
    util_sliceBlocks.isSpan(snapshot.context, child) && (previousSpan = {
      node: child,
      path: [...selectionStartBlock.path, "children", {
        _key: child._key
      }]
    });
  }
  return previousSpan;
}, getSelectedSpans = (snapshot) => {
  if (!snapshot.context.selection)
    return [];
  const selectedSpans = [], startPoint = selector_isSelectionExpanded.getSelectionStartPoint(snapshot), endPoint = getSelectionEndPoint(snapshot);
  if (!startPoint || !endPoint)
    return selectedSpans;
  const startBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(startPoint), endBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(endPoint), startSpanKey = util_sliceBlocks.getChildKeyFromSelectionPoint(startPoint), endSpanKey = util_sliceBlocks.getChildKeyFromSelectionPoint(endPoint);
  if (!startBlockKey || !endBlockKey)
    return selectedSpans;
  const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey), endBlockIndex = snapshot.blockIndexMap.get(endBlockKey);
  if (startBlockIndex === void 0 || endBlockIndex === void 0)
    return selectedSpans;
  const slicedValue = snapshot.context.value.slice(startBlockIndex, endBlockIndex + 1);
  let startBlockFound = !1;
  for (const block of slicedValue)
    if (block._key === startBlockKey && (startBlockFound = !0), !!util_sliceBlocks.isTextBlock(snapshot.context, block)) {
      if (block._key === startBlockKey) {
        for (const child of block.children)
          if (util_sliceBlocks.isSpan(snapshot.context, child)) {
            if (startSpanKey && child._key === startSpanKey) {
              if (startPoint.offset < child.text.length && selectedSpans.push({
                node: child,
                path: [{
                  _key: block._key
                }, "children", {
                  _key: child._key
                }]
              }), startSpanKey === endSpanKey)
                break;
              continue;
            }
            if (endSpanKey && child._key === endSpanKey) {
              endPoint.offset > 0 && selectedSpans.push({
                node: child,
                path: [{
                  _key: block._key
                }, "children", {
                  _key: child._key
                }]
              });
              break;
            }
            selectedSpans.length > 0 && selectedSpans.push({
              node: child,
              path: [{
                _key: block._key
              }, "children", {
                _key: child._key
              }]
            });
          }
        if (startBlockKey === endBlockKey)
          break;
        continue;
      }
      if (block._key === endBlockKey) {
        for (const child of block.children)
          if (util_sliceBlocks.isSpan(snapshot.context, child)) {
            if (endSpanKey && child._key === endSpanKey) {
              endPoint.offset > 0 && selectedSpans.push({
                node: child,
                path: [{
                  _key: block._key
                }, "children", {
                  _key: child._key
                }]
              });
              break;
            }
            selectedSpans.push({
              node: child,
              path: [{
                _key: block._key
              }, "children", {
                _key: child._key
              }]
            });
          }
        break;
      }
      if (startBlockFound)
        for (const child of block.children)
          util_sliceBlocks.isSpan(snapshot.context, child) && selectedSpans.push({
            node: child,
            path: [{
              _key: block._key
            }, "children", {
              _key: child._key
            }]
          });
    }
  return selectedSpans;
}, getMarkState = (snapshot) => {
  if (!snapshot.context.selection)
    return;
  const focusTextBlock = selector_isSelectionExpanded.getFocusTextBlock(snapshot), focusSpan = selector_isSelectionExpanded.getFocusSpan(snapshot);
  if (!focusTextBlock || !focusSpan)
    return;
  if (isSelectionExpanded(snapshot.context.selection)) {
    const selectedSpans = getSelectedSpans(snapshot);
    let index = 0, marks2 = [];
    for (const span of selectedSpans) {
      if (index === 0)
        marks2 = span.node.marks ?? [];
      else {
        if (span.node.marks?.length === 0) {
          marks2 = [];
          continue;
        }
        marks2 = marks2.filter((mark) => (span.node.marks ?? []).some((spanMark) => spanMark === mark));
      }
      index++;
    }
    return {
      state: "unchanged",
      marks: marks2
    };
  }
  const decorators = snapshot.context.schema.decorators.map((decorator) => decorator.name), marks = focusSpan.node.marks ?? [], marksWithoutAnnotations = marks.filter((mark) => decorators.includes(mark)), spanHasAnnotations = marks.length > marksWithoutAnnotations.length, spanIsEmpty = focusSpan.node.text.length === 0, atTheBeginningOfSpan = snapshot.context.selection.anchor.offset === 0, atTheEndOfSpan = snapshot.context.selection.anchor.offset === focusSpan.node.text.length, previousSpan = getPreviousSpan(snapshot), nextSpan = getNextSpan(snapshot), nextSpanAnnotations = nextSpan?.node?.marks?.filter((mark) => !decorators.includes(mark)) ?? [], spanAnnotations = marks.filter((mark) => !decorators.includes(mark)), previousSpanHasAnnotations = previousSpan ? previousSpan.node.marks?.some((mark) => !decorators.includes(mark)) : !1, previousSpanHasSameAnnotations = previousSpan ? previousSpan.node.marks?.filter((mark) => !decorators.includes(mark)).every((mark) => marks.includes(mark)) : !1, previousSpanHasSameAnnotation = previousSpan ? previousSpan.node.marks?.some((mark) => !decorators.includes(mark) && marks.includes(mark)) : !1, previousSpanHasSameMarks = previousSpan ? previousSpan.node.marks?.every((mark) => marks.includes(mark)) : !1, nextSpanSharesSomeAnnotations = spanAnnotations.some((mark) => nextSpanAnnotations?.includes(mark));
  if (spanHasAnnotations && !spanIsEmpty) {
    if (atTheBeginningOfSpan) {
      if (previousSpanHasSameMarks)
        return {
          state: "changed",
          marks: previousSpan?.node.marks ?? []
        };
      if (previousSpanHasSameAnnotations)
        return {
          state: "changed",
          marks: previousSpan?.node.marks ?? []
        };
      if (previousSpanHasSameAnnotation)
        return {
          state: "unchanged",
          marks: focusSpan.node.marks ?? []
        };
      if (!previousSpan)
        return {
          state: "changed",
          marks: []
        };
    }
    if (atTheEndOfSpan) {
      if (nextSpan && nextSpanSharesSomeAnnotations && nextSpanAnnotations.length < spanAnnotations.length || !nextSpanSharesSomeAnnotations)
        return {
          state: "changed",
          marks: nextSpan?.node.marks ?? []
        };
      if (!nextSpan)
        return {
          state: "changed",
          marks: []
        };
    }
  }
  return atTheBeginningOfSpan && !spanIsEmpty && previousSpan ? previousSpanHasAnnotations ? {
    state: "changed",
    marks: []
  } : {
    state: "changed",
    marks: (previousSpan?.node.marks ?? []).filter((mark) => decorators.includes(mark))
  } : {
    state: "unchanged",
    marks: focusSpan.node.marks ?? []
  };
}, getSelectedBlocks = (snapshot) => {
  if (!snapshot.context.selection)
    return [];
  const selectedBlocks = [], startPoint = util_sliceBlocks.getSelectionStartPoint(snapshot.context.selection), endPoint = util_sliceBlocks.getSelectionEndPoint(snapshot.context.selection), startKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(startPoint), endKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(endPoint);
  if (!startKey || !endKey)
    return selectedBlocks;
  const startBlockIndex = snapshot.blockIndexMap.get(startKey), endBlockIndex = snapshot.blockIndexMap.get(endKey);
  if (startBlockIndex === void 0 || endBlockIndex === void 0)
    return selectedBlocks;
  const slicedValue = snapshot.context.value.slice(startBlockIndex, endBlockIndex + 1);
  for (const block of slicedValue) {
    if (block._key === startKey) {
      if (selectedBlocks.push({
        node: block,
        path: [{
          _key: block._key
        }]
      }), startKey === endKey)
        break;
      continue;
    }
    if (block._key === endKey) {
      selectedBlocks.push({
        node: block,
        path: [{
          _key: block._key
        }]
      });
      break;
    }
    selectedBlocks.length > 0 && selectedBlocks.push({
      node: block,
      path: [{
        _key: block._key
      }]
    });
  }
  return selectedBlocks;
}, getActiveAnnotations = (snapshot) => {
  if (!snapshot.context.selection)
    return [];
  const selectedBlocks = getSelectedBlocks(snapshot), activeAnnotations = (getMarkState(snapshot)?.marks ?? []).filter((mark) => !snapshot.context.schema.decorators.map((decorator) => decorator.name).includes(mark));
  return selectedBlocks.flatMap((block) => util_sliceBlocks.isTextBlock(snapshot.context, block.node) ? block.node.markDefs ?? [] : []).filter((markDef) => activeAnnotations.includes(markDef._key));
}, getActiveListItem = (snapshot) => {
  if (!snapshot.context.selection)
    return;
  const selectedTextBlocks = getSelectedBlocks(snapshot).map((block) => block.node).filter((block) => util_sliceBlocks.isTextBlock(snapshot.context, block)), firstTextBlock = selectedTextBlocks.at(0);
  if (!firstTextBlock)
    return;
  const firstListItem = firstTextBlock.listItem;
  if (firstListItem && selectedTextBlocks.every((block) => block.listItem === firstListItem))
    return firstListItem;
}, getActiveStyle = (snapshot) => {
  if (!snapshot.context.selection)
    return;
  const selectedTextBlocks = getSelectedBlocks(snapshot).map((block) => block.node).filter((block) => util_sliceBlocks.isTextBlock(snapshot.context, block)), firstTextBlock = selectedTextBlocks.at(0);
  if (!firstTextBlock)
    return;
  const firstStyle = firstTextBlock.style;
  if (firstStyle && selectedTextBlocks.every((block) => block.style === firstStyle))
    return firstStyle;
}, getNextInlineObject = (snapshot) => {
  const focusTextBlock = selector_isSelectionExpanded.getFocusTextBlock(snapshot), selectionEndPoint = getSelectionEndPoint(snapshot), selectionEndPointChildKey = selectionEndPoint && types.isKeySegment(selectionEndPoint.path[2]) ? selectionEndPoint.path[2]._key : void 0;
  if (!focusTextBlock || !selectionEndPointChildKey)
    return;
  let endPointChildFound = !1, inlineObject;
  for (const child of focusTextBlock.node.children) {
    if (child._key === selectionEndPointChildKey) {
      endPointChildFound = !0;
      continue;
    }
    if (!util_sliceBlocks.isSpan$1(snapshot.context, child) && endPointChildFound) {
      inlineObject = {
        node: child,
        path: [...focusTextBlock.path, "children", {
          _key: child._key
        }]
      };
      break;
    }
  }
  return inlineObject;
}, getCaretWordSelection = (snapshot) => {
  if (!snapshot.context.selection || !selector_isSelectionExpanded.isSelectionCollapsed(snapshot))
    return null;
  const focusTextBlock = selector_isSelectionExpanded.getFocusTextBlock(snapshot), selectionStartPoint = selector_isSelectionExpanded.getSelectionStartPoint(snapshot), selectionStartOffset = selectionStartPoint ? util_sliceBlocks.spanSelectionPointToBlockOffset({
    context: snapshot.context,
    selectionPoint: selectionStartPoint
  }) : void 0;
  if (!focusTextBlock || !selectionStartPoint || !selectionStartOffset)
    return null;
  const previousInlineObject = selector_isSelectionExpanded.getPreviousInlineObject(snapshot), blockStartPoint = util_sliceBlocks.getBlockStartPoint({
    context: snapshot.context,
    block: focusTextBlock
  }), textDirectlyBefore = selector_isSelectionExpanded.getSelectionText({
    ...snapshot,
    context: {
      ...snapshot.context,
      selection: {
        anchor: previousInlineObject ? {
          path: previousInlineObject.path,
          offset: 0
        } : blockStartPoint,
        focus: selectionStartPoint
      }
    }
  }).split(/\s+/).at(-1), nextInlineObject = getNextInlineObject(snapshot), blockEndPoint = util_isSelectionCollapsed.getBlockEndPoint({
    context: snapshot.context,
    block: focusTextBlock
  }), textDirectlyAfter = selector_isSelectionExpanded.getSelectionText({
    ...snapshot,
    context: {
      ...snapshot.context,
      selection: {
        anchor: selectionStartPoint,
        focus: nextInlineObject ? {
          path: nextInlineObject.path,
          offset: 0
        } : blockEndPoint
      }
    }
  }).split(/\s+/).at(0);
  if ((textDirectlyBefore === void 0 || textDirectlyBefore === "") && (textDirectlyAfter === void 0 || textDirectlyAfter === ""))
    return null;
  const caretWordStartOffset = textDirectlyBefore ? {
    ...selectionStartOffset,
    offset: selectionStartOffset.offset - textDirectlyBefore.length
  } : selectionStartOffset, caretWordEndOffset = textDirectlyAfter ? {
    ...selectionStartOffset,
    offset: selectionStartOffset.offset + textDirectlyAfter.length
  } : selectionStartOffset, caretWordStartSelectionPoint = util_sliceBlocks.blockOffsetToSpanSelectionPoint({
    context: snapshot.context,
    blockOffset: caretWordStartOffset,
    direction: "backward"
  }), caretWordEndSelectionPoint = util_sliceBlocks.blockOffsetToSpanSelectionPoint({
    context: snapshot.context,
    blockOffset: caretWordEndOffset,
    direction: "forward"
  });
  if (!caretWordStartSelectionPoint || !caretWordEndSelectionPoint)
    return null;
  const caretWordSelection = {
    anchor: caretWordStartSelectionPoint,
    focus: caretWordEndSelectionPoint
  };
  return selector_isSelectionExpanded.isSelectionExpanded({
    context: {
      ...snapshot.context,
      selection: caretWordSelection
    }
  }) ? caretWordSelection : null;
}, getFirstBlock = (snapshot) => {
  const node = snapshot.context.value[0];
  return node ? {
    node,
    path: [{
      _key: node._key
    }]
  } : void 0;
}, getFocusBlockObject = (snapshot) => {
  const focusBlock = selector_isSelectionExpanded.getFocusBlock(snapshot);
  return focusBlock && !util_sliceBlocks.isTextBlock(snapshot.context, focusBlock.node) ? {
    node: focusBlock.node,
    path: focusBlock.path
  } : void 0;
}, getFocusInlineObject = (snapshot) => {
  const focusChild = selector_isSelectionExpanded.getFocusChild(snapshot);
  return focusChild && !types.isPortableTextSpan(focusChild.node) ? {
    node: focusChild.node,
    path: focusChild.path
  } : void 0;
}, getFocusListBlock = (snapshot) => {
  const focusTextBlock = selector_isSelectionExpanded.getFocusTextBlock(snapshot);
  return focusTextBlock && util_sliceBlocks.isListBlock(snapshot.context, focusTextBlock.node) ? {
    node: focusTextBlock.node,
    path: focusTextBlock.path
  } : void 0;
}, getLastBlock = (snapshot) => {
  const node = snapshot.context.value[snapshot.context.value.length - 1] ? snapshot.context.value[snapshot.context.value.length - 1] : void 0;
  return node ? {
    node,
    path: [{
      _key: node._key
    }]
  } : void 0;
}, getNextBlock = (snapshot) => {
  const selectionEndBlock = getSelectionEndBlock(snapshot);
  if (!selectionEndBlock)
    return;
  const index = snapshot.blockIndexMap.get(selectionEndBlock.node._key);
  if (index === void 0 || index === snapshot.context.value.length - 1)
    return;
  const nextBlock = snapshot.context.value.at(index + 1);
  return nextBlock ? {
    node: nextBlock,
    path: [{
      _key: nextBlock._key
    }]
  } : void 0;
}, getPreviousBlock = (snapshot) => {
  const selectionStartBlock = getSelectionStartBlock(snapshot);
  if (!selectionStartBlock)
    return;
  const index = snapshot.blockIndexMap.get(selectionStartBlock.node._key);
  if (index === void 0 || index === 0)
    return;
  const previousBlock = snapshot.context.value.at(index - 1);
  return previousBlock ? {
    node: previousBlock,
    path: [{
      _key: previousBlock._key
    }]
  } : void 0;
}, getSelectedTextBlocks = (snapshot) => {
  if (!snapshot.context.selection)
    return [];
  const selectedTextBlocks = [], startPoint = util_sliceBlocks.getSelectionStartPoint(snapshot.context.selection), endPoint = util_sliceBlocks.getSelectionEndPoint(snapshot.context.selection), startBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(startPoint), endBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(endPoint);
  if (!startBlockKey || !endBlockKey)
    return selectedTextBlocks;
  const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey), endBlockIndex = snapshot.blockIndexMap.get(endBlockKey);
  if (startBlockIndex === void 0 || endBlockIndex === void 0)
    return selectedTextBlocks;
  const slicedValue = snapshot.context.value.slice(startBlockIndex, endBlockIndex + 1);
  for (const block of slicedValue) {
    if (block._key === startBlockKey) {
      if (util_sliceBlocks.isTextBlock(snapshot.context, block) && selectedTextBlocks.push({
        node: block,
        path: [{
          _key: block._key
        }]
      }), startBlockKey === endBlockKey)
        break;
      continue;
    }
    if (block._key === endBlockKey) {
      util_sliceBlocks.isTextBlock(snapshot.context, block) && selectedTextBlocks.push({
        node: block,
        path: [{
          _key: block._key
        }]
      });
      break;
    }
    selectedTextBlocks.length > 0 && util_sliceBlocks.isTextBlock(snapshot.context, block) && selectedTextBlocks.push({
      node: block,
      path: [{
        _key: block._key
      }]
    });
  }
  return selectedTextBlocks;
}, getTrimmedSelection = (snapshot) => {
  if (!snapshot.context.selection)
    return snapshot.context.selection;
  const startPoint = util_sliceBlocks.getSelectionStartPoint(snapshot.context.selection), endPoint = util_sliceBlocks.getSelectionEndPoint(snapshot.context.selection), startBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(startPoint), startChildKey = util_sliceBlocks.getChildKeyFromSelectionPoint(startPoint), endBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(endPoint), endChildKey = util_sliceBlocks.getChildKeyFromSelectionPoint(endPoint);
  if (!startBlockKey || !endBlockKey)
    return snapshot.context.selection;
  const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey), endBlockIndex = snapshot.blockIndexMap.get(endBlockKey);
  if (startBlockIndex === void 0 || endBlockIndex === void 0)
    return snapshot.context.selection;
  const slicedValue = snapshot.context.value.slice(startBlockIndex, endBlockIndex + 1);
  let startBlockFound = !1, adjustedStartPoint, trimStartPoint = !1, adjustedEndPoint, trimEndPoint = !1, previousPotentialEndpoint;
  for (const block of slicedValue)
    if (!(block._key === startBlockKey && (startBlockFound = !0, util_sliceBlocks.isTextBlock(snapshot.context, block) && util_isSelectionCollapsed.isEmptyTextBlock(snapshot.context, block))) && startBlockFound && util_sliceBlocks.isTextBlock(snapshot.context, block)) {
      if (block._key === endBlockKey && util_isSelectionCollapsed.isEmptyTextBlock(snapshot.context, block))
        break;
      for (const child of block.children) {
        if (child._key === endChildKey && (!util_sliceBlocks.isSpan(snapshot.context, child) || endPoint.offset === 0)) {
          adjustedEndPoint = previousPotentialEndpoint ? {
            path: [{
              _key: previousPotentialEndpoint.blockKey
            }, "children", {
              _key: previousPotentialEndpoint.span._key
            }],
            offset: previousPotentialEndpoint.span.text.length
          } : void 0, trimEndPoint = !0;
          break;
        }
        if (trimStartPoint) {
          const lonelySpan = util_sliceBlocks.isSpan(snapshot.context, child) && block.children.length === 1;
          (util_sliceBlocks.isSpan(snapshot.context, child) && child.text.length > 0 || lonelySpan) && (adjustedStartPoint = {
            path: [{
              _key: block._key
            }, "children", {
              _key: child._key
            }],
            offset: 0
          }, previousPotentialEndpoint = {
            blockKey: block._key,
            span: child
          }, trimStartPoint = !1);
          continue;
        }
        if (child._key === startChildKey) {
          if (!util_sliceBlocks.isSpan(snapshot.context, child)) {
            trimStartPoint = !0;
            continue;
          }
          if (startPoint.offset === child.text.length) {
            trimStartPoint = !0, previousPotentialEndpoint = child.text.length > 0 ? {
              blockKey: block._key,
              span: child
            } : previousPotentialEndpoint;
            continue;
          }
        }
        previousPotentialEndpoint = util_sliceBlocks.isSpan(snapshot.context, child) && child.text.length > 0 ? {
          blockKey: block._key,
          span: child
        } : previousPotentialEndpoint;
      }
      if (block._key === endBlockKey)
        break;
    }
  const trimmedSelection = snapshot.context.selection.backward ? {
    anchor: trimEndPoint && adjustedEndPoint ? adjustedEndPoint : endPoint,
    focus: adjustedStartPoint ?? startPoint,
    backward: !0
  } : {
    anchor: adjustedStartPoint ?? startPoint,
    focus: trimEndPoint && adjustedEndPoint ? adjustedEndPoint : endPoint
  };
  if (selector_isSelectionExpanded.isSelectionCollapsed({
    context: {
      ...snapshot.context,
      selection: trimmedSelection
    }
  })) {
    const focusTextBlock = selector_isSelectionExpanded.getFocusTextBlock({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: trimmedSelection
      }
    });
    if (focusTextBlock && !util_isSelectionCollapsed.isEmptyTextBlock(snapshot.context, focusTextBlock.node))
      return null;
  }
  return trimmedSelection;
};
function getActiveAnnotationsMarks(snapshot) {
  const schema = snapshot.context.schema;
  return (getMarkState(snapshot)?.marks ?? []).filter((mark) => !schema.decorators.map((decorator) => decorator.name).includes(mark));
}
function isActiveAnnotation(annotation) {
  return (snapshot) => {
    const selectionMarkDefs = getSelectedBlocks(snapshot).flatMap((block) => util_sliceBlocks.isTextBlock(snapshot.context, block.node) ? block.node.markDefs ?? [] : []), activeAnnotations = getActiveAnnotationsMarks(snapshot);
    return selectionMarkDefs.filter((markDef) => markDef._type === annotation && activeAnnotations.includes(markDef._key)).length > 0;
  };
}
function getActiveDecorators(snapshot) {
  const schema = snapshot.context.schema, decoratorState = snapshot.decoratorState, markState = getMarkState(snapshot), decorators = schema.decorators.map((decorator) => decorator.name);
  let activeDecorators = (markState?.marks ?? []).filter((mark) => decorators.includes(mark));
  for (const decorator in decoratorState)
    decoratorState[decorator] === !1 ? activeDecorators = activeDecorators.filter((activeDecorator) => activeDecorator !== decorator) : decoratorState[decorator] === !0 && (activeDecorators.includes(decorator) || activeDecorators.push(decorator));
  return activeDecorators;
}
function isActiveDecorator(decorator) {
  return (snapshot) => {
    if (selector_isSelectionExpanded.isSelectionExpanded(snapshot)) {
      const selectedSpans = getSelectedSpans(snapshot);
      return selectedSpans.length > 0 && selectedSpans.every((span) => span.node.marks?.includes(decorator));
    }
    return getActiveDecorators(snapshot).includes(decorator);
  };
}
function isActiveListItem(listItem) {
  return (snapshot) => getActiveListItem(snapshot) === listItem;
}
function isActiveStyle(style) {
  return (snapshot) => getActiveStyle(snapshot) === style;
}
function isAtTheEndOfBlock(block) {
  return (snapshot) => {
    if (!snapshot.context.selection || !selector_isSelectionExpanded.isSelectionCollapsed(snapshot))
      return !1;
    const blockEndPoint = util_isSelectionCollapsed.getBlockEndPoint({
      context: snapshot.context,
      block
    });
    return util_isSelectionCollapsed.isEqualSelectionPoints(snapshot.context.selection.focus, blockEndPoint);
  };
}
function isAtTheStartOfBlock(block) {
  return (snapshot) => {
    if (!snapshot.context.selection || !selector_isSelectionExpanded.isSelectionCollapsed(snapshot))
      return !1;
    const blockStartPoint = util_sliceBlocks.getBlockStartPoint({
      context: snapshot.context,
      block
    });
    return util_isSelectionCollapsed.isEqualSelectionPoints(snapshot.context.selection.focus, blockStartPoint);
  };
}
function isPointAfterSelection(point) {
  return (snapshot) => {
    if (!snapshot.context.selection)
      return !1;
    const endPoint = util_sliceBlocks.getSelectionEndPoint(snapshot.context.selection), endBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(endPoint), endChildKey = util_sliceBlocks.getChildKeyFromSelectionPoint(endPoint), pointBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(point), pointChildKey = util_sliceBlocks.getChildKeyFromSelectionPoint(point);
    if (!pointBlockKey || !endBlockKey)
      return !1;
    const pointBlockIndex = snapshot.blockIndexMap.get(pointBlockKey), endBlockIndex = snapshot.blockIndexMap.get(endBlockKey);
    if (pointBlockIndex === void 0 || endBlockIndex === void 0)
      return !1;
    if (pointBlockIndex > endBlockIndex)
      return !0;
    if (pointBlockIndex < endBlockIndex)
      return !1;
    const pointBlock = snapshot.context.value.at(pointBlockIndex);
    if (!pointBlock || !util_sliceBlocks.isTextBlock(snapshot.context, pointBlock))
      return !1;
    let pointChildIndex, endChildIndex, childIndex = -1;
    for (const child of pointBlock.children) {
      if (childIndex++, child._key === pointChildKey && child._key === endChildKey)
        return point.offset > endPoint.offset;
      if (child._key === pointChildKey && (pointChildIndex = childIndex), child._key === endChildKey && (endChildIndex = childIndex), pointChildIndex !== void 0 && endChildIndex !== void 0)
        break;
    }
    return pointChildIndex === void 0 || endChildIndex === void 0 ? !1 : pointChildIndex > endChildIndex;
  };
}
function isPointBeforeSelection(point) {
  return (snapshot) => {
    if (!snapshot.context.selection)
      return !1;
    const startPoint = util_sliceBlocks.getSelectionStartPoint(snapshot.context.selection), startBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(startPoint), startChildKey = util_sliceBlocks.getChildKeyFromSelectionPoint(startPoint), pointBlockKey = util_sliceBlocks.getBlockKeyFromSelectionPoint(point), pointChildKey = util_sliceBlocks.getChildKeyFromSelectionPoint(point);
    if (!pointBlockKey || !startBlockKey)
      return !1;
    const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey), pointBlockIndex = snapshot.blockIndexMap.get(pointBlockKey);
    if (startBlockIndex === void 0 || pointBlockIndex === void 0)
      return !1;
    if (pointBlockIndex < startBlockIndex)
      return !0;
    if (pointBlockIndex > startBlockIndex)
      return !1;
    const pointBlock = snapshot.context.value.at(pointBlockIndex);
    if (!pointBlock || !util_sliceBlocks.isTextBlock(snapshot.context, pointBlock))
      return !1;
    let pointChildIndex, startChildIndex, childIndex = -1;
    for (const child of pointBlock.children) {
      if (childIndex++, child._key === pointChildKey && child._key === startChildKey)
        return point.offset < startPoint.offset;
      if (child._key === pointChildKey && (pointChildIndex = childIndex), child._key === startChildKey && (startChildIndex = childIndex), pointChildIndex !== void 0 && startChildIndex !== void 0)
        break;
    }
    return pointChildIndex === void 0 || startChildIndex === void 0 ? !1 : pointChildIndex < startChildIndex;
  };
}
function isOverlappingSelection(selection) {
  return (snapshot) => {
    if (!selection || !snapshot.context.selection)
      return !1;
    const selectionStartPoint = selector_isSelectionExpanded.getSelectionStartPoint({
      context: {
        ...snapshot.context,
        selection
      }
    }), selectionEndPoint = getSelectionEndPoint({
      context: {
        ...snapshot.context,
        selection
      }
    }), originalSelectionStartPoint = selector_isSelectionExpanded.getSelectionStartPoint(snapshot), originalSelectionEndPoint = getSelectionEndPoint(snapshot);
    if (!selectionStartPoint || !selectionEndPoint || !originalSelectionStartPoint || !originalSelectionEndPoint)
      return !1;
    const startPointBeforeSelection = isPointBeforeSelection(selectionStartPoint)(snapshot), startPointAfterSelection = isPointAfterSelection(selectionStartPoint)(snapshot), endPointBeforeSelection = isPointBeforeSelection(selectionEndPoint)(snapshot), endPointAfterSelection = isPointAfterSelection(selectionEndPoint)(snapshot), originalStartPointBeforeStartPoint = isPointBeforeSelection(originalSelectionStartPoint)({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: selectionStartPoint,
          focus: selectionStartPoint
        }
      }
    }), originalStartPointAfterStartPoint = isPointAfterSelection(originalSelectionStartPoint)({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: selectionStartPoint,
          focus: selectionStartPoint
        }
      }
    }), originalEndPointBeforeEndPoint = isPointBeforeSelection(originalSelectionEndPoint)({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: selectionEndPoint,
          focus: selectionEndPoint
        }
      }
    }), originalEndPointAfterEndPoint = isPointAfterSelection(originalSelectionEndPoint)({
      ...snapshot,
      context: {
        ...snapshot.context,
        selection: {
          anchor: selectionEndPoint,
          focus: selectionEndPoint
        }
      }
    }), endPointEqualToOriginalStartPoint = util_isSelectionCollapsed.isEqualSelectionPoints(selectionEndPoint, originalSelectionStartPoint), startPointEqualToOriginalEndPoint = util_isSelectionCollapsed.isEqualSelectionPoints(selectionStartPoint, originalSelectionEndPoint);
    return endPointBeforeSelection && !endPointEqualToOriginalStartPoint || startPointAfterSelection && !startPointEqualToOriginalEndPoint ? !1 : !originalStartPointBeforeStartPoint && originalStartPointAfterStartPoint && !originalEndPointBeforeEndPoint && originalEndPointAfterEndPoint ? !endPointEqualToOriginalStartPoint : originalStartPointBeforeStartPoint && !originalStartPointAfterStartPoint && originalEndPointBeforeEndPoint && !originalEndPointAfterEndPoint ? !startPointEqualToOriginalEndPoint : !startPointAfterSelection || !startPointBeforeSelection || !endPointAfterSelection || !endPointBeforeSelection;
  };
}
const isSelectingEntireBlocks = (snapshot) => {
  if (!snapshot.context.selection)
    return !1;
  const startPoint = snapshot.context.selection.backward ? snapshot.context.selection.focus : snapshot.context.selection.anchor, endPoint = snapshot.context.selection.backward ? snapshot.context.selection.anchor : snapshot.context.selection.focus, startBlock = getSelectionStartBlock(snapshot), endBlock = getSelectionEndBlock(snapshot);
  if (!startBlock || !endBlock)
    return !1;
  const startBlockStartPoint = util_sliceBlocks.getBlockStartPoint({
    context: snapshot.context,
    block: startBlock
  }), endBlockEndPoint = util_isSelectionCollapsed.getBlockEndPoint({
    context: snapshot.context,
    block: endBlock
  });
  return util_isSelectionCollapsed.isEqualSelectionPoints(startBlockStartPoint, startPoint) && util_isSelectionCollapsed.isEqualSelectionPoints(endBlockEndPoint, endPoint);
};
exports.getActiveAnnotations = getActiveAnnotations;
exports.getActiveAnnotationsMarks = getActiveAnnotationsMarks;
exports.getActiveDecorators = getActiveDecorators;
exports.getActiveListItem = getActiveListItem;
exports.getActiveStyle = getActiveStyle;
exports.getCaretWordSelection = getCaretWordSelection;
exports.getFirstBlock = getFirstBlock;
exports.getFocusBlockObject = getFocusBlockObject;
exports.getFocusInlineObject = getFocusInlineObject;
exports.getFocusListBlock = getFocusListBlock;
exports.getLastBlock = getLastBlock;
exports.getMarkState = getMarkState;
exports.getNextBlock = getNextBlock;
exports.getNextInlineObject = getNextInlineObject;
exports.getPreviousBlock = getPreviousBlock;
exports.getSelectedBlocks = getSelectedBlocks;
exports.getSelectedSpans = getSelectedSpans;
exports.getSelectedTextBlocks = getSelectedTextBlocks;
exports.getSelectionEndBlock = getSelectionEndBlock;
exports.getSelectionEndPoint = getSelectionEndPoint;
exports.getSelectionStartBlock = getSelectionStartBlock;
exports.getTrimmedSelection = getTrimmedSelection;
exports.isActiveAnnotation = isActiveAnnotation;
exports.isActiveDecorator = isActiveDecorator;
exports.isActiveListItem = isActiveListItem;
exports.isActiveStyle = isActiveStyle;
exports.isAtTheEndOfBlock = isAtTheEndOfBlock;
exports.isAtTheStartOfBlock = isAtTheStartOfBlock;
exports.isOverlappingSelection = isOverlappingSelection;
exports.isPointAfterSelection = isPointAfterSelection;
exports.isPointBeforeSelection = isPointBeforeSelection;
exports.isSelectingEntireBlocks = isSelectingEntireBlocks;
//# sourceMappingURL=selector.is-selecting-entire-blocks.cjs.map
