// 🔍 BAKASANA Z-INDEX BROWSER CHECKER
// Wklej ten kod w konsoli przeglądarki na dowolnej stronie

console.log(
  '🔍 BAKASANA Z-INDEX CHECKER - Sprawdzanie nakładających się elementów...\n'
);

// Funkcja do sprawdzenia wszystkich elementów z wysokim z-index
function checkHighZIndex() {
  const results = [];
  const elements = document.querySelectorAll('*');

  elements.forEach(el => {
    const computedStyle = getComputedStyle(el);
    const zIndex = computedStyle.zIndex;

    if (zIndex !== 'auto' && parseInt(zIndex) > 10) {
      const rect = el.getBoundingClientRect();

      results.push({
        element: el,
        tagName: el.tagName.toLowerCase(),
        className: el.className || '',
        id: el.id || '',
        zIndex: parseInt(zIndex),
        position: computedStyle.position,
        visible: rect.width > 0 && rect.height > 0,
        rect: {
          top: Math.round(rect.top),
          left: Math.round(rect.left),
          width: Math.round(rect.width),
          height: Math.round(rect.height),
        },
        selector:
          el.tagName.toLowerCase() +
          (el.id ? `#${el.id}` : '') +
          (el.className
            ? `.${el.className
                .split(' ')
                .filter(c => c)
                .join('.')}`
            : ''),
      });
    }
  });

  return results.sort((a, b) => b.zIndex - a.zIndex);
}

// Funkcja do sprawdzenia nakładających się elementów
function checkOverlappingElements() {
  const overlaps = [];
  const positionedElements = Array.from(document.querySelectorAll('*')).filter(
    el => {
      const style = getComputedStyle(el);
      return style.position !== 'static' && style.position !== '';
    }
  );

  for (let i = 0; i < positionedElements.length; i++) {
    for (let j = i + 1; j < positionedElements.length; j++) {
      const el1 = positionedElements[i];
      const el2 = positionedElements[j];
      const rect1 = el1.getBoundingClientRect();
      const rect2 = el2.getBoundingClientRect();

      // Sprawdź czy elementy się nakładają
      const overlap = !(
        rect1.right < rect2.left ||
        rect2.right < rect1.left ||
        rect1.bottom < rect2.top ||
        rect2.bottom < rect1.top
      );

      if (
        overlap &&
        rect1.width > 0 &&
        rect1.height > 0 &&
        rect2.width > 0 &&
        rect2.height > 0
      ) {
        const style1 = getComputedStyle(el1);
        const style2 = getComputedStyle(el2);

        overlaps.push({
          element1: {
            element: el1,
            selector:
              el1.tagName.toLowerCase() +
              (el1.id ? `#${el1.id}` : '') +
              (el1.className
                ? `.${el1.className
                    .split(' ')
                    .filter(c => c)
                    .join('.')}`
                : ''),
            zIndex: style1.zIndex,
            position: style1.position,
          },
          element2: {
            element: el2,
            selector:
              el2.tagName.toLowerCase() +
              (el2.id ? `#${el2.id}` : '') +
              (el2.className
                ? `.${el2.className
                    .split(' ')
                    .filter(c => c)
                    .join('.')}`
                : ''),
            zIndex: style2.zIndex,
            position: style2.position,
          },
        });
      }
    }
  }

  return overlaps;
}

// Funkcja do podświetlenia elementów
function highlightElement(element, color = 'red') {
  const originalOutline = element.style.outline;
  element.style.outline = `3px solid ${color}`;
  element.style.outlineOffset = '2px';

  setTimeout(() => {
    element.style.outline = originalOutline;
  }, 3000);
}

// Funkcja do wyświetlenia szczegółowego raportu
function generateDetailedReport() {
  const highZIndexElements = checkHighZIndex();
  const overlappingElements = checkOverlappingElements();

  console.log('📊 SZCZEGÓŁOWY RAPORT Z-INDEX');
  console.log('================================\n');

  // Podsumowanie
  console.log(`🎯 PODSUMOWANIE:`);
  console.log(
    `   📊 Elementów z wysokim z-index (>10): ${highZIndexElements.length}`
  );
  console.log(
    `   ⚠️  Nakładających się elementów: ${overlappingElements.length}`
  );
  console.log(
    `   🔝 Najwyższy z-index: ${highZIndexElements.length > 0 ? highZIndexElements[0].zIndex : 'brak'}\n`
  );

  // Elementy z wysokim z-index
  if (highZIndexElements.length > 0) {
    console.log('🔝 ELEMENTY Z WYSOKIM Z-INDEX:');
    console.log('------------------------------');

    highZIndexElements.forEach((item, index) => {
      const status = item.visible ? '👁️ widoczny' : '🙈 ukryty';
      console.log(
        `${index + 1}. z-index: ${item.zIndex} | ${status} | ${item.position}`
      );
      console.log(`   Selektor: ${item.selector}`);
      console.log(`   Rozmiar: ${item.rect.width}x${item.rect.height}px`);
      console.log(
        `   Pozycja: top:${item.rect.top}px, left:${item.rect.left}px`
      );
      console.log('   Element:', item.element);
      console.log('');
    });
  }

  // Nakładające się elementy
  if (overlappingElements.length > 0) {
    console.log('⚠️  NAKŁADAJĄCE SIĘ ELEMENTY:');
    console.log('-----------------------------');

    overlappingElements.forEach((overlap, index) => {
      console.log(`${index + 1}. KONFLIKT:`);
      console.log(
        `   Element 1: ${overlap.element1.selector} (z-index: ${overlap.element1.zIndex})`
      );
      console.log(
        `   Element 2: ${overlap.element2.selector} (z-index: ${overlap.element2.zIndex})`
      );
      console.log(
        '   Elementy:',
        overlap.element1.element,
        overlap.element2.element
      );
      console.log('');
    });
  }

  // Rekomendacje
  console.log('💡 REKOMENDACJE:');
  console.log('----------------');

  const veryHighZIndex = highZIndexElements.filter(el => el.zIndex > 1000);
  if (veryHighZIndex.length > 0) {
    console.log(
      `⚠️  ${veryHighZIndex.length} elementów ma z-index > 1000 - rozważ użycie niższych wartości`
    );
  }

  if (overlappingElements.length > 0) {
    console.log(
      `❌ ${overlappingElements.length} par elementów się nakłada - może powodować problemy UX`
    );
  }

  const autoZIndex = highZIndexElements.filter(el => el.position === 'static');
  if (autoZIndex.length > 0) {
    console.log(
      `🔧 ${autoZIndex.length} elementów ma z-index ale position: static - z-index nie będzie działać`
    );
  }

  return {
    highZIndexElements,
    overlappingElements,
    summary: {
      totalHighZIndex: highZIndexElements.length,
      totalOverlaps: overlappingElements.length,
      maxZIndex:
        highZIndexElements.length > 0 ? highZIndexElements[0].zIndex : 0,
      veryHighCount: veryHighZIndex.length,
    },
  };
}

// Funkcja do podświetlenia wszystkich problemowych elementów
function highlightProblems() {
  const highZIndexElements = checkHighZIndex();
  const overlappingElements = checkOverlappingElements();

  console.log('🎨 Podświetlam problemowe elementy...');

  // Podświetl elementy z bardzo wysokim z-index (czerwony)
  highZIndexElements
    .filter(el => el.zIndex > 1000)
    .forEach(item => {
      highlightElement(item.element, 'red');
    });

  // Podświetl elementy z wysokim z-index (pomarańczowy)
  highZIndexElements
    .filter(el => el.zIndex <= 1000 && el.zIndex > 100)
    .forEach(item => {
      highlightElement(item.element, 'orange');
    });

  // Podświetl nakładające się elementy (niebieski)
  overlappingElements.forEach(overlap => {
    highlightElement(overlap.element1.element, 'blue');
    highlightElement(overlap.element2.element, 'cyan');
  });

  console.log('✅ Elementy podświetlone na 3 sekundy');
  console.log('🔴 Czerwony: z-index > 1000');
  console.log('🟠 Pomarańczowy: z-index 100-1000');
  console.log('🔵 Niebieski/Cyan: nakładające się elementy');
}

// Uruchom podstawowe sprawdzenie
console.log('🚀 Uruchamiam sprawdzenie z-index...\n');

const basicResults = checkHighZIndex();
const basicOverlaps = checkOverlappingElements();

console.log(`📊 SZYBKIE PODSUMOWANIE:`);
console.log(`   Elementów z wysokim z-index: ${basicResults.length}`);
console.log(`   Nakładających się elementów: ${basicOverlaps.length}`);

if (basicResults.length > 0) {
  console.log('\n🔝 TOP 5 NAJWYŻSZYCH Z-INDEX:');
  basicResults.slice(0, 5).forEach((item, index) => {
    console.log(`${index + 1}. z-index: ${item.zIndex} - ${item.selector}`);
  });
}

console.log('\n💡 DOSTĘPNE FUNKCJE:');
console.log('   generateDetailedReport() - szczegółowy raport');
console.log('   highlightProblems() - podświetl problemowe elementy');
console.log('   checkHighZIndex() - sprawdź wysokie z-index');
console.log('   checkOverlappingElements() - sprawdź nakładające się elementy');

// Zwróć wyniki dla dalszego użycia
window.zIndexResults = {
  highZIndex: basicResults,
  overlapping: basicOverlaps,
  generateDetailedReport,
  highlightProblems,
  checkHighZIndex,
  checkOverlappingElements,
};
