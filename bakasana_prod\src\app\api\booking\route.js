import { NextResponse } from 'next/server';

import {
  asyncHand<PERSON>,
  validators,
  validateSchema,
  ValidationError,
  RateLimitError,
  handleApiError,
} from '@/lib/errorHandler';

const requestCounts = new Map();
const RATE_LIMIT = 3; // 3 booking requests na IP na 15 minut
const RATE_WINDOW = 15 * 60 * 1000; // 15 minut

function getClientIP(request) {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return 'unknown';
}

function isRateLimited(ip) {
  const now = Date.now();
  const requests = requestCounts.get(ip) || [];

  // Usuń stare requesty
  const recentRequests = requests.filter(time => now - time < RATE_WINDOW);

  if (recentRequests.length >= RATE_LIMIT) {
    return true;
  }

  // Dodaj nowy request
  recentRequests.push(now);
  requestCounts.set(ip, recentRequests);

  return false;
}

// Validation schema for booking
const bookingSchema = {
  'formData.firstName': [
    validators.required,
    { validator: validators.minLength, min: 2 },
  ],
  'formData.lastName': [
    validators.required,
    { validator: validators.minLength, min: 2 },
  ],
  'formData.email': [validators.required, validators.email],
  'formData.phone': [validators.required, validators.phone],
  'formData.emergencyContact': [validators.required],
  'formData.emergencyPhone': [validators.required, validators.phone],
  retreat: [validators.required],
};

export const POST = asyncHandler(async request => {
  const clientIP = getClientIP(request);

  // Check rate limiting
  if (isRateLimited(clientIP)) {
    console.warn(`Rate limit exceeded for booking API from IP: ${clientIP}`);
    throw new RateLimitError(
      'Zbyt wiele prób rezerwacji. Spróbuj ponownie za 15 minut.'
    );
  }

  const { retreat, formData, timestamp } = await request.json();

  // Validate required fields
  if (!retreat || !formData) {
    return NextResponse.json(
      { success: false, error: 'Missing required data' },
      { status: 400 }
    );
  }

  // Validate personal data
  const requiredFields = [
    'firstName',
    'lastName',
    'email',
    'phone',
    'emergencyContact',
    'emergencyPhone',
  ];
  for (const field of requiredFields) {
    if (!formData[field]?.trim()) {
      return NextResponse.json(
        { success: false, error: `Missing required field: ${field}` },
        { status: 400 }
      );
    }
  }

  // Validate email format
  if (!formData.email.includes('@')) {
    return NextResponse.json(
      { success: false, error: 'Invalid email format' },
      { status: 400 }
    );
  }

  // Calculate total price
  const basePrice = retreat.price || 0;
  const roomSupplement = formData.roomPreference === 'single' ? 500 : 0;
  const totalPrice = basePrice + roomSupplement;
  const depositAmount = Math.round(totalPrice * 0.3);

  // Generate booking reference
  const bookingRef = `BR${Date.now().toString().slice(-6)}${Math.random().toString(36).substr(2, 3).toUpperCase()}`;

  // Prepare booking data
  const bookingData = {
    bookingReference: bookingRef,
    retreat: {
      id: retreat.id,
      title: retreat.title,
      startDate: retreat.start,
      endDate: retreat.end,
      basePrice: basePrice,
    },
    customer: {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      phone: formData.phone,
      birthDate: formData.birthDate,
      emergencyContact: formData.emergencyContact,
      emergencyPhone: formData.emergencyPhone,
    },
    preferences: {
      yogaExperience: formData.yogaExperience,
      dietaryRestrictions: formData.dietaryRestrictions,
      medicalConditions: formData.medicalConditions,
      roomPreference: formData.roomPreference,
      specialRequests: formData.specialRequests,
    },
    pricing: {
      basePrice,
      roomSupplement,
      totalPrice,
      depositAmount,
      remainingAmount: totalPrice - depositAmount,
    },
    payment: {
      method: formData.paymentMethod,
      depositPaid: false,
      fullPaymentPaid: false,
    },
    agreements: {
      termsAccepted: formData.agreeTerms,
      newsletterOptIn: formData.agreeNewsletter,
    },
    status: 'pending_deposit',
    createdAt: timestamp || new Date().toISOString(),
  };

  // TODO: Implement database save, payment processing
  // Log booking for monitoring (consider using proper logging service in production)

  // Send booking confirmation email
  let emailSent = false;
  try {
    const { sendBookingConfirmation } = await import('../../../lib/emailService');
    await sendBookingConfirmation(bookingData);
    emailSent = true;
    console.log('Booking confirmation email sent successfully');
  } catch (error) {
    console.error('Failed to send booking confirmation email:', error);
    // Don't fail the booking if email fails
  }

  // Add to newsletter if opted in
  if (formData.agreeNewsletter) {
    try {
      await fetch(
        `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/newsletter`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: formData.email,
            tags: ['retreat-booking', 'customer'],
            source: 'booking-form',
          }),
        }
      );
    } catch (error) {
      console.warn('Newsletter signup failed:', error);
    }
  }

  return NextResponse.json({
    success: true,
    message: 'Booking created successfully',
    booking: {
      reference: bookingRef,
      totalPrice,
      depositAmount,
      paymentInstructions: generatePaymentInstructions(
        formData.paymentMethod,
        depositAmount,
        bookingRef
      ),
    },
  });
});



// Generate payment instructions based on method
function generatePaymentInstructions(method, amount, reference) {
  if (method === 'transfer') {
    return {
      type: 'bank_transfer',
      amount,
      reference,
      instructions: `
        Dane do przelewu:
        
        Odbiorca: Julia Jakubowicz
        Numer konta: 12 3456 7890 1234 5678 9012 3456
        Kwota: ${amount} PLN
        Tytuł: Zadatek retreat ${reference}
        
        Po wpłacie zadatku Twoja rezerwacja zostanie potwierdzona.
        Resztę kwoty (${amount * 2.33} PLN) wpłać 30 dni przed wyjazdem.
      `,
    };
  } else if (method === 'blik') {
    return {
      type: 'blik',
      amount,
      reference,
      instructions: `
        Link do płatności BLIK zostanie wysłany na Twój email w ciągu 15 minut.
        
        Kwota zadatku: ${amount} PLN
        Numer rezerwacji: ${reference}
        
        Po wpłacie zadatku Twoja rezerwacja zostanie potwierdzona.
      `,
    };
  }

  return {
    type: 'contact',
    instructions:
      'Skontaktuj się z nami w sprawie płatności: <EMAIL>',
  };
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Booking API is working',
    timestamp: new Date().toISOString(),
  });
}
