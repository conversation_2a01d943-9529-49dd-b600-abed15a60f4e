{"version": 3, "file": "util.slice-text-block.js", "sources": ["../../src/utils/util.selection-point-to-block-offset.ts", "../../src/utils/util.slice-text-block.ts"], "sourcesContent": ["import type {EditorContext} from '../editor/editor-snapshot'\nimport {getBlockKeyFromSelectionPoint} from '../selection/selection-point'\nimport type {BlockOffset} from '../types/block-offset'\nimport type {EditorSelectionPoint} from '../types/editor'\nimport {childSelectionPointToBlockOffset} from './util.child-selection-point-to-block-offset'\n\n/**\n * @public\n */\nexport function selectionPointToBlockOffset({\n  context,\n  selectionPoint,\n}: {\n  context: Pick<EditorContext, 'schema' | 'value'>\n  selectionPoint: EditorSelectionPoint\n}): BlockOffset | undefined {\n  const blockKey = getBlockKeyFromSelectionPoint(selectionPoint)\n\n  if (selectionPoint.path.length === 1 && blockKey !== undefined) {\n    return {\n      path: [{_key: blockKey}],\n      offset: selectionPoint.offset,\n    }\n  }\n\n  return childSelectionPointToBlockOffset({\n    context,\n    selectionPoint,\n  })\n}\n", "import type {PortableTextChild, PortableTextTextBlock} from '@sanity/types'\nimport type {EditorContext} from '..'\nimport {isSpan} from '../internal-utils/parse-blocks'\nimport {\n  getBlockKeyFromSelectionPoint,\n  getChildKeyFromSelectionPoint,\n} from '../selection/selection-point'\nimport {getSelectionEndPoint} from './util.get-selection-end-point'\nimport {getSelectionStartPoint} from './util.get-selection-start-point'\n\nexport function sliceTextBlock({\n  context,\n  block,\n}: {\n  context: Pick<EditorContext, 'schema' | 'selection'>\n  block: PortableTextTextBlock\n}): PortableTextTextBlock {\n  const startPoint = getSelectionStartPoint(context.selection)\n  const endPoint = getSelectionEndPoint(context.selection)\n\n  if (!startPoint || !endPoint) {\n    return block\n  }\n\n  const startBlockKey = getBlockKeyFromSelectionPoint(startPoint)\n  const endBlockKey = getBlockKeyFromSelectionPoint(endPoint)\n\n  if (startBlockKey !== endBlockKey || startBlockKey !== block._key) {\n    return block\n  }\n\n  const startChildKey = getChildKeyFromSelectionPoint(startPoint)\n  const endChildKey = getChildKeyFromSelectionPoint(endPoint)\n\n  if (!startChildKey || !endChildKey) {\n    return block\n  }\n\n  let startChildFound = false\n  const children: Array<PortableTextChild> = []\n\n  for (const child of block.children) {\n    if (child._key === startChildKey) {\n      startChildFound = true\n\n      if (isSpan(context, child)) {\n        const text =\n          child._key === endChildKey\n            ? child.text.slice(startPoint.offset, endPoint.offset)\n            : child.text.slice(startPoint.offset)\n\n        children.push({\n          ...child,\n          text,\n        })\n      } else {\n        children.push(child)\n      }\n\n      if (startChildKey === endChildKey) {\n        break\n      }\n\n      continue\n    }\n\n    if (child._key === endChildKey) {\n      if (isSpan(context, child)) {\n        children.push({\n          ...child,\n          text: child.text.slice(0, endPoint.offset),\n        })\n      } else {\n        children.push(child)\n      }\n\n      break\n    }\n\n    if (startChildFound) {\n      children.push(child)\n    }\n  }\n\n  return {\n    ...block,\n    children,\n  }\n}\n"], "names": ["selectionPointToBlockOffset", "context", "selectionPoint", "blockKey", "getBlockKeyFromSelectionPoint", "path", "length", "undefined", "_key", "offset", "childSelectionPointToBlockOffset", "sliceTextBlock", "block", "startPoint", "getSelectionStartPoint", "selection", "endPoint", "getSelectionEndPoint", "startBlockKey", "endBlockKey", "start<PERSON>hild<PERSON>ey", "getChildKeyFromSelectionPoint", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startChildFound", "children", "child", "isSpan", "text", "slice", "push"], "mappings": ";;AASO,SAASA,4BAA4B;AAAA,EAC1CC;AAAAA,EACAC;AAIF,GAA4B;AAC1B,QAAMC,WAAWC,8BAA8BF,cAAc;AAE7D,SAAIA,eAAeG,KAAKC,WAAW,KAAKH,aAAaI,SAC5C;AAAA,IACLF,MAAM,CAAC;AAAA,MAACG,MAAML;AAAAA,IAAAA,CAAS;AAAA,IACvBM,QAAQP,eAAeO;AAAAA,EAAAA,IAIpBC,iCAAiC;AAAA,IACtCT;AAAAA,IACAC;AAAAA,EAAAA,CACD;AACH;ACnBO,SAASS,eAAe;AAAA,EAC7BV;AAAAA,EACAW;AAIF,GAA0B;AACxB,QAAMC,aAAaC,uBAAuBb,QAAQc,SAAS,GACrDC,WAAWC,qBAAqBhB,QAAQc,SAAS;AAEvD,MAAI,CAACF,cAAc,CAACG;AAClB,WAAOJ;AAGT,QAAMM,gBAAgBd,8BAA8BS,UAAU,GACxDM,cAAcf,8BAA8BY,QAAQ;AAE1D,MAAIE,kBAAkBC,eAAeD,kBAAkBN,MAAMJ;AAC3D,WAAOI;AAGT,QAAMQ,gBAAgBC,8BAA8BR,UAAU,GACxDS,cAAcD,8BAA8BL,QAAQ;AAE1D,MAAI,CAACI,iBAAiB,CAACE;AACrB,WAAOV;AAGT,MAAIW,kBAAkB;AACtB,QAAMC,WAAqC,CAAA;AAE3C,aAAWC,SAASb,MAAMY,UAAU;AAClC,QAAIC,MAAMjB,SAASY,eAAe;AAGhC,UAFAG,kBAAkB,IAEdG,OAAOzB,SAASwB,KAAK,GAAG;AAC1B,cAAME,OACJF,MAAMjB,SAASc,cACXG,MAAME,KAAKC,MAAMf,WAAWJ,QAAQO,SAASP,MAAM,IACnDgB,MAAME,KAAKC,MAAMf,WAAWJ,MAAM;AAExCe,iBAASK,KAAK;AAAA,UACZ,GAAGJ;AAAAA,UACHE;AAAAA,QAAAA,CACD;AAAA,MACH;AACEH,iBAASK,KAAKJ,KAAK;AAGrB,UAAIL,kBAAkBE;AACpB;AAGF;AAAA,IACF;AAEA,QAAIG,MAAMjB,SAASc,aAAa;AAC1BI,aAAOzB,SAASwB,KAAK,IACvBD,SAASK,KAAK;AAAA,QACZ,GAAGJ;AAAAA,QACHE,MAAMF,MAAME,KAAKC,MAAM,GAAGZ,SAASP,MAAM;AAAA,MAAA,CAC1C,IAEDe,SAASK,KAAKJ,KAAK;AAGrB;AAAA,IACF;AAEIF,uBACFC,SAASK,KAAKJ,KAAK;AAAA,EAEvB;AAEA,SAAO;AAAA,IACL,GAAGb;AAAAA,IACHY;AAAAA,EAAAA;AAEJ;"}