// This file is auto-generated via npm run vendor
let [ a, b, c, d, e, f, g ] = [ 'application', 'audio', 'image', 'font', 'message', 'text', 'video' ]
module.exports = {
  atom: `${a}/atom+xml`,
  epub: `${a}/epub+zip`,
  js: `${a}/javascript`,
  mjs: `${a}/javascript`,
  json: `${a}/json`,
  map: `${a}/json`,
  jsonld: `${a}/ld+json`,
  mrc: `${a}/marc`,
  doc: `${a}/msword`,
  dot: `${a}/msword`,
  pdf: `${a}/pdf`,
  pgp: `${a}/pgp-encrypted`,
  asc: `${a}/pgp-signature`,
  sig: `${a}/pgp-signature`,
  ai: `${a}/postscript`,
  eps: `${a}/postscript`,
  ps: `${a}/postscript`,
  rdf: `${a}/rdf+xml`,
  owl: `${a}/rdf+xml`,
  rss: `${a}/rss+xml`,
  rtf: `${a}/rtf`,
  apk: `${a}/vnd.android.package-archive`,
  kml: `${a}/vnd.google-earth.kml+xml`,
  kmz: `${a}/vnd.google-earth.kmz`,
  xls: `${a}/vnd.ms-excel`,
  xlm: `${a}/vnd.ms-excel`,
  xla: `${a}/vnd.ms-excel`,
  xlc: `${a}/vnd.ms-excel`,
  xlt: `${a}/vnd.ms-excel`,
  xlw: `${a}/vnd.ms-excel`,
  ppt: `${a}/vnd.ms-powerpoint`,
  pps: `${a}/vnd.ms-powerpoint`,
  pot: `${a}/vnd.ms-powerpoint`,
  docm: `${a}/vnd.ms-word.document.macroenabled.12`,
  odt: `${a}/vnd.oasis.opendocument.text`,
  pptx: `${a}/vnd.openxmlformats-officedocument.presentationml.presentation`,
  xlsx: `${a}/vnd.openxmlformats-officedocument.spreadsheetml.sheet`,
  docx: `${a}/vnd.openxmlformats-officedocument.wordprocessingml.document`,
  torrent: `${a}/x-bittorrent`,
  deb: `${a}/x-debian-package`,
  udeb: `${a}/x-debian-package`,
  php: `${a}/x-httpd-php`,
  jnlp: `${a}/x-java-jnlp-file`,
  prc: `${a}/x-mobipocket-ebook`,
  mobi: `${a}/x-mobipocket-ebook`,
  exe: `${a}/x-msdownload`,
  dll: `${a}/x-msdownload`,
  com: `${a}/x-msdownload`,
  bat: `${a}/x-msdownload`,
  msi: `${a}/x-msdownload`,
  nc: `${a}/x-netcdf`,
  cdf: `${a}/x-netcdf`,
  rar: `${a}/x-rar-compressed`,
  ris: `${a}/x-research-info-systems`,
  swf: `${a}/x-shockwave-flash`,
  tar: `${a}/x-tar`,
  tex: `${a}/x-tex`,
  xhtml: `${a}/xhtml+xml`,
  xht: `${a}/xhtml+xml`,
  xml: `${f}/xml`,
  xsl: `${a}/xml`,
  xsd: `${a}/xml`,
  rng: `${a}/xml`,
  zip: `${a}/zip`,
  mp3: `${b}/mpeg`,
  m4a: `${b}/mp4`,
  mp4a: `${b}/mp4`,
  mpga: `${b}/mpeg`,
  mp2: `${b}/mpeg`,
  mp2a: `${b}/mpeg`,
  m2a: `${b}/mpeg`,
  m3a: `${b}/mpeg`,
  m3u: `${b}/x-mpegurl`,
  ram: `${b}/x-pn-realaudio`,
  ra: `${b}/x-pn-realaudio`,
  wav: `${b}/x-wav`,
  otf: `${d}/otf`,
  ttf: `${d}/ttf`,
  woff: `${d}/woff`,
  woff2: `${d}/woff2`,
  avif: `${c}/avif`,
  gif: `${c}/gif`,
  jp2: `${c}/jp2`,
  jpg2: `${c}/jp2`,
  jpeg: `${c}/jpeg`,
  jpg: `${c}/jpeg`,
  jpe: `${c}/jpeg`,
  jpm: `${c}/jpm`,
  jpx: `${c}/jpx`,
  jpf: `${c}/jpx`,
  png: `${c}/png`,
  svg: `${c}/svg+xml`,
  svgz: `${c}/svg+xml`,
  tif: `${c}/tiff`,
  tiff: `${c}/tiff`,
  djvu: `${c}/vnd.djvu`,
  djv: `${c}/vnd.djvu`,
  webp: `${c}/webp`,
  eml: `${e}/rfc822`,
  mime: `${e}/rfc822`,
  ics: `${f}/calendar`,
  ifb: `${f}/calendar`,
  css: `${f}/css`,
  csv: `${f}/csv`,
  html: `${f}/html`,
  htm: `${f}/html`,
  shtml: `${f}/html`,
  txt: `${f}/plain`,
  text: `${f}/plain`,
  conf: `${f}/plain`,
  def: `${f}/plain`,
  list: `${f}/plain`,
  log: `${f}/plain`,
  in: `${f}/plain`,
  ini: `${f}/plain`,
  tsv: `${f}/tab-separated-values`,
  ttl: `${f}/turtle`,
  vcard: `${f}/vcard`,
  wml: `${f}/vnd.wap.wml`,
  c: `${f}/x-c`,
  cc: `${f}/x-c`,
  cxx: `${f}/x-c`,
  cpp: `${f}/x-c`,
  h: `${f}/x-c`,
  hh: `${f}/x-c`,
  dic: `${f}/x-c`,
  vcs: `${f}/x-vcalendar`,
  vcf: `${f}/x-vcard`,
  mj2: `${g}/mj2`,
  mjp2: `${g}/mj2`,
  mp4: `${g}/mp4`,
  mp4v: `${g}/mp4`,
  mpg4: `${g}/mp4`,
  webm: `${g}/webm`,
  asf: `${g}/x-ms-asf`,
  asx: `${g}/x-ms-asf`,
}
