#!/usr/bin/env node

/**
 * HTML Validation and SEO Analysis Script for Next.js Project
 * Analyzes JSX files for HTML structure, SEO elements, and accessibility
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

class HTMLSEOValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.suggestions = [];
    this.seoIssues = [];
    this.accessibilityIssues = [];
  }

  log(message, color = 'white') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  // Analyze JSX files for HTML structure and SEO
  analyzeJSXFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const fileName = path.basename(filePath);
      
      this.log(`\n📄 Analyzing: ${fileName}`, 'cyan');
      
      // Check for basic HTML structure issues
      this.checkHTMLStructure(content, fileName);
      
      // Check SEO elements
      this.checkSEOElements(content, fileName);
      
      // Check accessibility
      this.checkAccessibility(content, fileName);
      
      // Check meta tags and structured data
      this.checkMetaTags(content, fileName);
      
    } catch (error) {
      this.errors.push(`Error reading file ${filePath}: ${error.message}`);
    }
  }

  checkHTMLStructure(content, fileName) {
    // Check for proper semantic HTML
    const semanticTags = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'];
    const foundSemanticTags = semanticTags.filter(tag => 
      content.includes(`<${tag}`) || content.includes(`<${tag} `)
    );
    
    if (foundSemanticTags.length === 0 && content.includes('return')) {
      this.warnings.push(`${fileName}: No semantic HTML tags found. Consider using header, nav, main, section, article, aside, footer`);
    }

    // Check for heading hierarchy
    const headings = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
    const foundHeadings = headings.filter(h => 
      content.includes(`<${h}>`) || content.includes(`<${h} `)
    );
    
    if (foundHeadings.length === 0 && content.includes('return')) {
      this.warnings.push(`${fileName}: No heading tags found. Proper heading hierarchy is important for SEO and accessibility`);
    }

    // Check for missing alt attributes on images
    const imgRegex = /<img[^>]*>/gi;
    const images = content.match(imgRegex) || [];
    images.forEach((img, index) => {
      if (!img.includes('alt=')) {
        this.errors.push(`${fileName}: Image ${index + 1} missing alt attribute: ${img}`);
      }
    });

    // Check for proper form labels
    const inputRegex = /<input[^>]*>/gi;
    const inputs = content.match(inputRegex) || [];
    inputs.forEach((input, index) => {
      if (!input.includes('aria-label') && !content.includes('<label')) {
        this.warnings.push(`${fileName}: Input ${index + 1} may be missing proper labeling`);
      }
    });
  }

  checkSEOElements(content, fileName) {
    // Check for title tag or metadata
    if (!content.includes('title:') && !content.includes('<title>') && fileName.includes('page.jsx')) {
      this.seoIssues.push(`${fileName}: Missing page title metadata`);
    }

    // Check for description
    if (!content.includes('description:') && !content.includes('meta name="description"') && fileName.includes('page.jsx')) {
      this.seoIssues.push(`${fileName}: Missing meta description`);
    }

    // Check for keywords
    if (!content.includes('keywords:') && fileName.includes('page.jsx')) {
      this.suggestions.push(`${fileName}: Consider adding keywords metadata for better SEO`);
    }

    // Check for Open Graph tags
    if (!content.includes('openGraph:') && !content.includes('og:') && fileName.includes('page.jsx')) {
      this.seoIssues.push(`${fileName}: Missing Open Graph metadata for social sharing`);
    }

    // Check for structured data
    if (!content.includes('application/ld+json') && !content.includes('@type') && fileName.includes('page.jsx')) {
      this.suggestions.push(`${fileName}: Consider adding structured data (JSON-LD) for better search engine understanding`);
    }

    // Check for canonical URLs
    if (!content.includes('canonical') && fileName.includes('page.jsx')) {
      this.suggestions.push(`${fileName}: Consider adding canonical URL to prevent duplicate content issues`);
    }
  }

  checkAccessibility(content, fileName) {
    // Check for ARIA attributes
    const ariaAttributes = ['aria-label', 'aria-labelledby', 'aria-describedby', 'role'];
    const hasAria = ariaAttributes.some(attr => content.includes(attr));
    
    if (!hasAria && content.includes('button') && content.includes('return')) {
      this.accessibilityIssues.push(`${fileName}: Consider adding ARIA attributes for better accessibility`);
    }

    // Check for focus management
    if (content.includes('onClick') && !content.includes('onKeyDown')) {
      this.accessibilityIssues.push(`${fileName}: Interactive elements should be keyboard accessible`);
    }

    // Check for color contrast (basic check for color usage)
    if (content.includes('color:') || content.includes('background:')) {
      this.suggestions.push(`${fileName}: Ensure sufficient color contrast for accessibility (WCAG 2.1 AA: 4.5:1 for normal text)`);
    }
  }

  checkMetaTags(content, fileName) {
    // Check for viewport meta tag in layout files
    if (fileName.includes('layout.jsx')) {
      if (!content.includes('viewport') && !content.includes('device-width')) {
        this.errors.push(`${fileName}: Missing viewport meta tag for responsive design`);
      }
    }

    // Check for robots meta tag
    if (fileName.includes('page.jsx') && !content.includes('robots')) {
      this.suggestions.push(`${fileName}: Consider adding robots metadata to control search engine indexing`);
    }

    // Check for language attribute
    if (fileName.includes('layout.jsx') && !content.includes('lang=')) {
      this.errors.push(`${fileName}: Missing lang attribute on html element`);
    }
  }

  // Analyze all JSX files in the project
  analyzeProject() {
    this.log('🔍 Starting HTML and SEO validation for Next.js project...', 'blue');
    
    const srcDir = path.join(process.cwd(), 'src');
    
    if (!fs.existsSync(srcDir)) {
      this.errors.push('src directory not found');
      return;
    }

    // Find all JSX files
    const findJSXFiles = (dir) => {
      const files = [];
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.includes('node_modules')) {
          files.push(...findJSXFiles(fullPath));
        } else if (item.endsWith('.jsx') || item.endsWith('.js')) {
          files.push(fullPath);
        }
      }
      
      return files;
    };

    const jsxFiles = findJSXFiles(srcDir);
    
    this.log(`Found ${jsxFiles.length} JSX/JS files to analyze`, 'green');
    
    // Analyze each file
    jsxFiles.forEach(file => {
      this.analyzeJSXFile(file);
    });

    // Generate report
    this.generateReport();
  }

  generateReport() {
    this.log('\n📊 VALIDATION REPORT', 'magenta');
    this.log('='.repeat(50), 'magenta');

    // Errors
    if (this.errors.length > 0) {
      this.log(`\n❌ ERRORS (${this.errors.length}):`, 'red');
      this.errors.forEach(error => this.log(`  • ${error}`, 'red'));
    }

    // SEO Issues
    if (this.seoIssues.length > 0) {
      this.log(`\n🔍 SEO ISSUES (${this.seoIssues.length}):`, 'yellow');
      this.seoIssues.forEach(issue => this.log(`  • ${issue}`, 'yellow'));
    }

    // Accessibility Issues
    if (this.accessibilityIssues.length > 0) {
      this.log(`\n♿ ACCESSIBILITY ISSUES (${this.accessibilityIssues.length}):`, 'yellow');
      this.accessibilityIssues.forEach(issue => this.log(`  • ${issue}`, 'yellow'));
    }

    // Warnings
    if (this.warnings.length > 0) {
      this.log(`\n⚠️  WARNINGS (${this.warnings.length}):`, 'yellow');
      this.warnings.forEach(warning => this.log(`  • ${warning}`, 'yellow'));
    }

    // Suggestions
    if (this.suggestions.length > 0) {
      this.log(`\n💡 SUGGESTIONS (${this.suggestions.length}):`, 'cyan');
      this.suggestions.forEach(suggestion => this.log(`  • ${suggestion}`, 'cyan'));
    }

    // Summary
    this.log('\n📈 SUMMARY:', 'blue');
    this.log(`  Errors: ${this.errors.length}`, this.errors.length > 0 ? 'red' : 'green');
    this.log(`  SEO Issues: ${this.seoIssues.length}`, this.seoIssues.length > 0 ? 'yellow' : 'green');
    this.log(`  Accessibility Issues: ${this.accessibilityIssues.length}`, this.accessibilityIssues.length > 0 ? 'yellow' : 'green');
    this.log(`  Warnings: ${this.warnings.length}`, this.warnings.length > 0 ? 'yellow' : 'green');
    this.log(`  Suggestions: ${this.suggestions.length}`, 'cyan');

    // Save report to file
    this.saveReport();
  }

  saveReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        errors: this.errors.length,
        seoIssues: this.seoIssues.length,
        accessibilityIssues: this.accessibilityIssues.length,
        warnings: this.warnings.length,
        suggestions: this.suggestions.length
      },
      details: {
        errors: this.errors,
        seoIssues: this.seoIssues,
        accessibilityIssues: this.accessibilityIssues,
        warnings: this.warnings,
        suggestions: this.suggestions
      }
    };

    const reportPath = path.join(process.cwd(), 'html-seo-validation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log(`\n💾 Report saved to: ${reportPath}`, 'green');
  }
}

// Run the validation
if (require.main === module) {
  const validator = new HTMLSEOValidator();
  validator.analyzeProject();
}

module.exports = HTMLSEOValidator;
