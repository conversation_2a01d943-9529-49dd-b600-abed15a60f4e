#!/usr/bin/env node

/**
 * SEO Verification Setup Helper - Bakasana Studio
 * 
 * This script helps verify and setup SEO verification codes
 * for Google Search Console, Bing Webmaster Tools, Facebook, and Pinterest
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

const log = (color, message) => console.log(`${colors[color]}${message}${colors.reset}`);

class SEOVerificationHelper {
  constructor() {
    this.envPath = path.join(process.cwd(), '.env.local');
    this.envExamplePath = path.join(process.cwd(), '.env.example');
    this.requiredVars = [
      'GOOGLE_SITE_VERIFICATION',
      'BING_VERIFICATION', 
      'YANDEX_VERIFICATION',
      'FACEBOOK_VERIFICATION',
      'PINTEREST_VERIFICATION',
      'NEXT_PUBLIC_SITE_URL'
    ];
  }

  checkEnvironmentFile() {
    log('cyan', '\n🔍 Checking environment setup...\n');
    
    if (!fs.existsSync(this.envPath)) {
      log('yellow', '⚠️  .env.local file not found');
      
      if (fs.existsSync(this.envExamplePath)) {
        log('blue', '📋 Creating .env.local from .env.example...');
        fs.copyFileSync(this.envExamplePath, this.envPath);
        log('green', '✅ Created .env.local file');
      } else {
        log('red', '❌ .env.example file not found');
        return false;
      }
    }
    
    return true;
  }

  parseEnvFile() {
    if (!fs.existsSync(this.envPath)) return {};
    
    const envContent = fs.readFileSync(this.envPath, 'utf8');
    const env = {};
    
    envContent.split('\n').forEach(line => {
      if (line.trim() && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return env;
  }

  checkVerificationCodes() {
    log('cyan', '🎯 Checking verification codes...\n');
    
    const env = this.parseEnvFile();
    const missing = [];
    const configured = [];
    
    this.requiredVars.forEach(varName => {
      const value = env[varName];
      if (!value || value.includes('your_') || value.includes('XXXXXXXXXX')) {
        missing.push(varName);
        log('red', `❌ ${varName}: Not configured`);
      } else {
        configured.push(varName);
        log('green', `✅ ${varName}: Configured (${value.substring(0, 10)}...)`);
      }
    });
    
    return { missing, configured };
  }

  generateVerificationReport() {
    log('cyan', '\n📊 SEO Verification Report\n');
    log('magenta', '='.repeat(50));
    
    const { missing, configured } = this.checkVerificationCodes();
    
    log('green', `✅ Configured: ${configured.length}/${this.requiredVars.length}`);
    log('red', `❌ Missing: ${missing.length}/${this.requiredVars.length}`);
    
    if (missing.length > 0) {
      log('yellow', '\n⚠️  Missing verification codes:');
      missing.forEach(varName => {
        log('yellow', `   • ${varName}`);
      });
      
      log('cyan', '\n📋 Next Steps:');
      log('white', '1. Get verification codes from respective platforms');
      log('white', '2. Add them to .env.local file');
      log('white', '3. Run this script again to verify');
      log('white', '4. Deploy your changes');
      
      this.showPlatformLinks();
    } else {
      log('green', '\n🎉 All verification codes configured!');
      log('cyan', '\n🚀 Ready to deploy and verify on platforms');
    }
  }

  showPlatformLinks() {
    log('cyan', '\n🔗 Platform Links:');
    log('white', '• Google Search Console: https://search.google.com/search-console');
    log('white', '• Bing Webmaster Tools: https://www.bing.com/webmasters');
    log('white', '• Facebook Business Manager: https://business.facebook.com');
    log('white', '• Pinterest Business: https://business.pinterest.com');
  }

  checkLayoutImplementation() {
    log('cyan', '\n🔧 Checking layout.jsx implementation...\n');
    
    const layoutPath = path.join(process.cwd(), 'src/app/layout.jsx');
    
    if (!fs.existsSync(layoutPath)) {
      log('red', '❌ layout.jsx not found');
      return false;
    }
    
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');
    
    // Check for verification implementation
    const checks = [
      { pattern: /verification:\s*{/, name: 'Basic verification object' },
      { pattern: /google:\s*process\.env\.GOOGLE_SITE_VERIFICATION/, name: 'Google verification' },
      { pattern: /bing:\s*process\.env\.BING_VERIFICATION/, name: 'Bing verification' },
      { pattern: /facebook-domain-verification/, name: 'Facebook verification meta tag' },
      { pattern: /p:domain_verify/, name: 'Pinterest verification meta tag' }
    ];
    
    checks.forEach(check => {
      if (check.pattern.test(layoutContent)) {
        log('green', `✅ ${check.name}: Implemented`);
      } else {
        log('red', `❌ ${check.name}: Missing`);
      }
    });
    
    return true;
  }

  validateSiteUrl() {
    log('cyan', '\n🌐 Validating site URL...\n');
    
    const env = this.parseEnvFile();
    const siteUrl = env.NEXT_PUBLIC_SITE_URL;
    
    if (!siteUrl) {
      log('red', '❌ NEXT_PUBLIC_SITE_URL not configured');
      return false;
    }
    
    if (!siteUrl.startsWith('https://')) {
      log('yellow', '⚠️  Site URL should use HTTPS');
    }
    
    if (siteUrl.includes('localhost') || siteUrl.includes('127.0.0.1')) {
      log('yellow', '⚠️  Using localhost URL (update for production)');
    }
    
    log('green', `✅ Site URL: ${siteUrl}`);
    return true;
  }

  run() {
    log('magenta', '\n' + '='.repeat(60));
    log('cyan', '🎯 SEO VERIFICATION SETUP HELPER - BAKASANA STUDIO');
    log('magenta', '='.repeat(60));
    
    // Check environment file
    if (!this.checkEnvironmentFile()) {
      log('red', '\n❌ Setup failed - environment file issues');
      return;
    }
    
    // Check layout implementation
    this.checkLayoutImplementation();
    
    // Validate site URL
    this.validateSiteUrl();
    
    // Generate report
    this.generateVerificationReport();
    
    log('magenta', '\n' + '='.repeat(60));
    log('cyan', '🚀 Run: npm run build && npm run start (after configuration)');
    log('magenta', '='.repeat(60) + '\n');
  }
}

// Run the helper
if (require.main === module) {
  const helper = new SEOVerificationHelper();
  helper.run();
}

module.exports = SEOVerificationHelper;