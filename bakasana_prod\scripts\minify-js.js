#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const glob = require('glob');

console.log('📦 Starting JavaScript minification...');

// Create dist directory if it doesn't exist
const distDir = path.join(process.cwd(), 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Find all JS/JSX files
const jsFiles = glob.sync('src/**/*.{js,jsx}', { 
  ignore: ['src/**/*.test.js', 'src/**/*.spec.js', 'node_modules/**']
});

console.log(`Found ${jsFiles.length} JavaScript files to minify`);

if (jsFiles.length === 0) {
  console.log('⚠️ No JavaScript files found to minify');
  process.exit(0);
}

// Create a temporary file list for terser
const fileListPath = path.join(distDir, 'js-files.txt');
fs.writeFileSync(fileListPath, jsFiles.join('\n'));

try {
  // Use terser with file list
  console.log('⚡ Minifying JavaScript files...');
  
  // Minify each file individually and combine
  let combinedContent = '';
  let totalOriginalSize = 0;
  let totalMinifiedSize = 0;

  jsFiles.forEach(file => {
    try {
      const originalContent = fs.readFileSync(file, 'utf8');
      totalOriginalSize += originalContent.length;
      
      // Create temporary file for this specific file
      const tempFile = path.join(distDir, 'temp.js');
      fs.writeFileSync(tempFile, originalContent);
      
      // Minify individual file
      const minifiedFile = path.join(distDir, 'temp.min.js');
      execSync(`npx terser ${tempFile} --compress --mangle --output ${minifiedFile}`, { stdio: 'pipe' });
      
      const minifiedContent = fs.readFileSync(minifiedFile, 'utf8');
      totalMinifiedSize += minifiedContent.length;
      
      combinedContent += `\n/* ${path.basename(file)} */\n${minifiedContent}`;
      
      // Clean up temp files
      fs.unlinkSync(tempFile);
      fs.unlinkSync(minifiedFile);
      
      console.log(`  ✅ ${path.basename(file)}: ${originalContent.length} → ${minifiedContent.length} bytes`);
    } catch (error) {
      console.error(`  ❌ Error minifying ${file}:`, error.message);
    }
  });

  // Write combined minified file
  const outputFile = path.join(distDir, 'app.min.js');
  fs.writeFileSync(outputFile, combinedContent);

  // Calculate savings
  const savings = Math.round(((totalOriginalSize - totalMinifiedSize) / totalOriginalSize) * 100);
  
  console.log(`\n📊 JavaScript Minification Results:`);
  console.log(`   Original size: ${Math.round(totalOriginalSize / 1024)} KB`);
  console.log(`   Minified size: ${Math.round(totalMinifiedSize / 1024)} KB`);
  console.log(`   Space saved: ${savings}%`);
  console.log(`   Output: ${outputFile}`);

  // Clean up
  fs.unlinkSync(fileListPath);

  console.log('✅ JavaScript minification completed!');

} catch (error) {
  console.error('❌ Error during JavaScript minification:', error.message);
  
  // Clean up on error
  if (fs.existsSync(fileListPath)) {
    fs.unlinkSync(fileListPath);
  }
  
  process.exit(1);
}
