{"name": "@architect/parser", "version": "7.0.1", "description": "Architect <PERSON><PERSON><PERSON> accepts plaintext, JSON, or YAML .arc manifests and returns a plain JavaScript Object", "main": "./src/index.js", "scripts": {"deno:test": "deno test -A --unstable mod.test.ts", "deno:fmt": "deno fmt mod.ts && deno fmt mod.test.ts", "lint": "eslint . --fix", "test:unit": "cross-env tape 'test/*-test.js' | tap-arc", "coverage": "nyc --reporter=lcov --reporter=text-summary npm run test:unit", "test:nolint": "npm run coverage", "test": "npm run lint && npm run coverage", "rc": "npm version prerelease --preid RC", "build": "rollup -c", "vendor:js-yaml": "mkdir -p tmp && browserify --node --standalone js-yaml --im --no-builtins node_modules/js-yaml/index.js > tmp/js-yaml.js && mkdir -p src/compat/vendor && terser tmp/js-yaml.js -o src/compat/vendor/js-yaml.min.js", "vendor": "npm run vendor:js-yaml"}, "engines": {"node": ">=16"}, "repository": {"type": "git", "url": "git+https://github.com/architect/parser.git"}, "author": "<PERSON>", "license": "Apache-2.0", "files": ["src/*"], "devDependencies": {"@architect/eslint-config": "~3.0.0", "@rollup/plugin-commonjs": "~25.0.3", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-terser": "~0.4.3", "browserify": "~17.0.0", "cross-env": "~7.0.3", "eslint": "~9.1.1", "js-yaml": "~4.1.0", "nyc": "^15.1.0", "rollup": "^3.27.0", "tap-arc": "~1.2.2", "tape": "~5.7.5", "terser": "~5.31.0"}, "eslintConfig": {"extends": "@architect/eslint-config"}}