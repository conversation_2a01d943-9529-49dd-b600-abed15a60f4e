{"name": "@sanity/cli", "version": "4.1.1", "description": "Sanity CLI tool for managing Sanity installations, managing plugins, schemas and datasets", "keywords": ["sanity", "cms", "headless", "realtime", "content", "cli", "tool"], "homepage": "https://www.sanity.io/", "bugs": {"url": "https://github.com/sanity-io/sanity/issues"}, "repository": {"type": "git", "url": "git+https://github.com/sanity-io/sanity.git", "directory": "packages/@sanity/cli"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "commonjs", "exports": {".": {"source": "./src/index.ts", "import": "./lib/index.mjs", "require": "./lib/index.js", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "bin": {"sanity": "./bin/sanity"}, "files": ["bin", "codemods", "lib", "templates"], "dependencies": {"@babel/traverse": "^7.27.4", "@sanity/client": "^7.8.0", "@sanity/runtime-cli": "^10.0.0", "@sanity/telemetry": "^0.8.0", "@sanity/template-validator": "^2.4.3", "@sanity/util": "4.1.1", "chalk": "^4.1.2", "debug": "^4.3.4", "decompress": "^4.2.0", "esbuild": "0.25.8", "esbuild-register": "^3.6.0", "get-it": "^8.6.10", "groq-js": "^1.17.2", "pkg-dir": "^5.0.0", "prettier": "^3.5.3", "semver": "^7.3.5", "validate-npm-package-name": "^3.0.0", "@sanity/codegen": "4.1.1"}, "devDependencies": {"@rexxars/gitconfiglocal": "^3.0.1", "@rollup/plugin-node-resolve": "^16.0.1", "@sanity/eslint-config-studio": "^5.0.2", "@sanity/generate-help-url": "^3.0.0", "@types/babel__traverse": "^7.20.5", "@types/configstore": "^5.0.1", "@types/cpx": "^1.5.2", "@types/debug": "^4.1.7", "@types/decompress": "^4.2.4", "@types/inquirer": "^6.0.0", "@types/lodash": "^4.17.7", "@types/minimist": "^1.2.5", "@types/node": "^22.10.0", "@types/semver": "^7.5.6", "@types/semver-compare": "^1.0.1", "@types/tar": "^6.1.3", "@types/validate-npm-package-name": "^3.0.3", "@types/which": "^2.0.1", "@vercel/frameworks": "1.6.0", "@vercel/fs-detectors": "4.1.3", "boxen": "^4.1.0", "clean-stack": "^3.0.0", "configstore": "^5.0.1", "cpx": "^1.5.0", "deep-sort-object": "^1.0.1", "dotenv": "^16.0.3", "dotenv-expand": "^9.0.0", "execa": "^2.0.0", "get-latest-version": "^5.0.0", "git-user-info": "^2.0.3", "globals": "^16.2.0", "inquirer": "^6.0.0", "is-installed-globally": "^0.4.0", "leven": "^3.1.0", "lodash": "^4.17.21", "minimist": "^1.2.5", "open": "^8.4.0", "ora": "^8.0.1", "p-map": "^4.0.0", "p-timeout": "^4.0.0", "preferred-pm": "^3.0.3", "promise-props-recursive": "^2.0.2", "recast": "^0.23.11", "resolve-from": "^5.0.0", "rimraf": "^5.0.10", "semver": "^7.3.5", "semver-compare": "^1.0.0", "tar": "^6.1.11", "vite": "^6.3.5", "vitest": "^3.2.3", "which": "^2.0.2", "xdg-basedir": "^4.0.0", "@repo/test-config": "4.1.1", "@sanity/types": "4.1.1", "@repo/eslint-config": "4.1.1", "@repo/package.config": "4.1.1"}, "engines": {"node": ">=20.19"}, "scripts": {"build": "pkg-utils build --strict --check --clean", "check:types": "(cd ../../.. && tsc --project packages/@sanity/cli/tsconfig.lib.json --erasableSyntaxOnly)", "clean": "<PERSON><PERSON><PERSON> lib", "lint": "eslint .", "test": "vitest", "ts": "node -r esbuild-register", "watch": "pkg-utils watch"}}