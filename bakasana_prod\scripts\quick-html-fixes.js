#!/usr/bin/env node

/**
 * Quick HTML/SEO Fixes Script
 * Automatically fixes the most critical HTML and SEO issues
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

class QuickHTMLFixer {
  constructor() {
    this.fixes = [];
    this.errors = [];
  }

  log(message, color = 'white') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  // Fix layout.jsx issues
  fixLayoutIssues() {
    const layoutPath = path.join(process.cwd(), 'src/app/layout.jsx');
    
    if (!fs.existsSync(layoutPath)) {
      this.errors.push('layout.jsx not found');
      return;
    }

    let content = fs.readFileSync(layoutPath, 'utf8');
    let modified = false;

    // Check if lang attribute is missing
    if (!content.includes('lang=') || content.includes('<html className=')) {
      content = content.replace(
        /<html className=/g,
        '<html lang=\'pl\' className='
      );
      modified = true;
      this.fixes.push('Added lang="pl" attribute to html element in layout.jsx');
    }

    // Check viewport configuration
    if (!content.includes('viewport') || !content.includes('device-width')) {
      // The viewport is already configured in the existing code, just verify it's correct
      if (content.includes('export const viewport')) {
        this.log('✅ Viewport configuration already exists in layout.jsx', 'green');
      } else {
        this.fixes.push('Viewport configuration needs to be added to layout.jsx');
      }
    }

    if (modified) {
      fs.writeFileSync(layoutPath, content);
      this.log('✅ Fixed layout.jsx issues', 'green');
    }
  }

  // Add basic metadata to pages missing it
  fixPageMetadata() {
    const pagesDir = path.join(process.cwd(), 'src/app');
    
    const findPageFiles = (dir) => {
      const files = [];
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('api')) {
          const pageFile = path.join(fullPath, 'page.jsx');
          if (fs.existsSync(pageFile)) {
            files.push(pageFile);
          }
          files.push(...findPageFiles(fullPath));
        }
      }
      
      return files;
    };

    const pageFiles = findPageFiles(pagesDir);
    
    pageFiles.forEach(pageFile => {
      this.checkAndFixPageMetadata(pageFile);
    });
  }

  checkAndFixPageMetadata(pageFile) {
    const content = fs.readFileSync(pageFile, 'utf8');
    const fileName = path.basename(path.dirname(pageFile));
    
    // Skip if metadata already exists
    if (content.includes('export const metadata') || content.includes('generateMetadata')) {
      return;
    }

    // Generate basic metadata based on file path
    const metadata = this.generateBasicMetadata(fileName);
    
    // Find the right place to insert metadata (after imports, before component)
    const lines = content.split('\n');
    let insertIndex = 0;
    
    // Find last import or first non-import line
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ') || lines[i].startsWith('\'use client\'') || lines[i].trim() === '') {
        insertIndex = i + 1;
      } else {
        break;
      }
    }

    // Insert metadata
    lines.splice(insertIndex, 0, '', metadata, '');
    
    const newContent = lines.join('\n');
    fs.writeFileSync(pageFile, newContent);
    
    this.fixes.push(`Added basic metadata to ${fileName}/page.jsx`);
    this.log(`✅ Added metadata to ${fileName}/page.jsx`, 'green');
  }

  generateBasicMetadata(pageName) {
    const titles = {
      'kontakt': 'Kontakt - BAKASANA',
      'o-mnie': 'O mnie - Julia Jakubowicz - BAKASANA',
      'galeria': 'Galeria - BAKASANA',
      'blog': 'Blog - BAKASANA',
      'program': 'Program Retreatu - BAKASANA',
      'retreaty': 'Retreaty Jogi - BAKASANA',
      'wellness': 'Wellness - BAKASANA',
      'mapa': 'Mapa Bali - BAKASANA'
    };

    const descriptions = {
      'kontakt': 'Skontaktuj się z nami w sprawie retreatów jogi na Bali i Sri Lanka. Julia Jakubowicz - certyfikowana instruktorka jogi.',
      'o-mnie': 'Julia Jakubowicz - certyfikowana instruktorka jogi i fizjoterapeutka. Poznaj moją historię i doświadczenie w prowadzeniu retreatów.',
      'galeria': 'Zobacz zdjęcia z naszych retreatów jogi na Bali i Sri Lanka. Magiczne miejsca, praktyki jogi i niezapomniane chwile.',
      'blog': 'Blog o jodze, medytacji i podróżach duchowych. Praktyczne porady i inspiracje od Julii Jakubowicz.',
      'program': 'Szczegółowy program retreatu jogi na Bali. Praktyki, zwiedzanie, medytacje i transformacyjne doświadczenia.',
      'retreaty': 'Retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz. Transformacyjne podróże łączące praktykę z odkrywaniem Azji.',
      'wellness': 'Wellness i joga na Bali. Holistyczne podejście do zdrowia i dobrostanu podczas retreatu.',
      'mapa': 'Interaktywna mapa miejsc na Bali, które odwiedzimy podczas retreatu jogi. Świątynie, plaże i magiczne lokacje.'
    };

    const title = titles[pageName] || `${pageName.charAt(0).toUpperCase() + pageName.slice(1)} - BAKASANA`;
    const description = descriptions[pageName] || `Strona ${pageName} - BAKASANA retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz.`;

    return `export const metadata = {
  title: '${title}',
  description: '${description}',
  openGraph: {
    title: '${title}',
    description: '${description}',
    images: ['/images/og-image.jpg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: '${title}',
    description: '${description}',
  }
};`;
  }

  // Fix common image alt text issues
  fixImageAltText() {
    const srcDir = path.join(process.cwd(), 'src');
    
    const findJSXFiles = (dir) => {
      const files = [];
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.includes('node_modules')) {
          files.push(...findJSXFiles(fullPath));
        } else if (item.endsWith('.jsx')) {
          files.push(fullPath);
        }
      }
      
      return files;
    };

    const jsxFiles = findJSXFiles(srcDir);
    let fixedFiles = 0;

    jsxFiles.forEach(file => {
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;

      // Fix Image components without alt
      content = content.replace(
        /<Image([^>]*?)src=["']([^"']*?)["']([^>]*?)(?!.*alt=)/g,
        '<Image$1src="$2"$3 alt="Image"'
      );

      // Fix img tags without alt
      content = content.replace(
        /<img([^>]*?)src=["']([^"']*?)["']([^>]*?)(?!.*alt=)/g,
        '<img$1src="$2"$3 alt="Image"'
      );

      if (content !== originalContent) {
        fs.writeFileSync(file, content);
        fixedFiles++;
        this.fixes.push(`Added alt attributes to images in ${path.basename(file)}`);
      }
    });

    if (fixedFiles > 0) {
      this.log(`✅ Fixed alt attributes in ${fixedFiles} files`, 'green');
    }
  }

  // Run all fixes
  runAllFixes() {
    this.log('🔧 Starting quick HTML/SEO fixes...', 'blue');
    
    this.fixLayoutIssues();
    this.fixPageMetadata();
    this.fixImageAltText();
    
    this.generateReport();
  }

  generateReport() {
    this.log('\n📊 QUICK FIXES REPORT', 'cyan');
    this.log('='.repeat(40), 'cyan');

    if (this.fixes.length > 0) {
      this.log(`\n✅ FIXES APPLIED (${this.fixes.length}):`, 'green');
      this.fixes.forEach(fix => this.log(`  • ${fix}`, 'green'));
    }

    if (this.errors.length > 0) {
      this.log(`\n❌ ERRORS (${this.errors.length}):`, 'red');
      this.errors.forEach(error => this.log(`  • ${error}`, 'red'));
    }

    this.log('\n📝 NEXT STEPS:', 'blue');
    this.log('  • Run npm run validate:all to check remaining issues', 'cyan');
    this.log('  • Review and customize the generated metadata', 'cyan');
    this.log('  • Test the fixes in development mode', 'cyan');
    this.log('  • Run accessibility audit', 'cyan');
  }
}

// Run the fixes
if (require.main === module) {
  const fixer = new QuickHTMLFixer();
  fixer.runAllFixes();
}

module.exports = QuickHTMLFixer;
