// This file is auto-generated via npm run vendor
module.exports = [ '3g2', '3gp', '3gpp', '7z', 'aif', 'aifc', 'aiff', 'au', 'avi', 'avif', 'azw', 'bin', 'bmp', 'boz', 'bpk', 'buffer', 'bz', 'bz2', 'deb', 'deploy', 'dist', 'distz', 'dll', 'dmg', 'dms', 'doc', 'docx', 'dot', 'dump', 'ear', 'elc', 'epub', 'exe', 'gif', 'h264', 'heic', 'heics', 'heif', 'heifs', 'ico', 'img', 'iso', 'jar', 'jp2', 'jpe', 'jpeg', 'jpg', 'jpg2', 'lrf', 'm1v', 'm2a', 'm2v', 'm3a', 'mar', 'mj2', 'mjp2', 'mov', 'mp2', 'mp2a', 'mp3', 'mp4', 'mp4v', 'mpe', 'mpeg', 'mpg', 'mpg4', 'mpga', 'mpkg', 'msi', 'msm', 'msp', 'oga', 'ogg', 'ogv', 'opus', 'otf', 'pdf', 'pkg', 'png', 'pot', 'pps', 'ppt', 'pptx', 'qt', 'rar', 'rtf', 'snd', 'so', 'spx', 'tar', 'tif', 'tiff', 'ts', 'war', 'wav', 'webm', 'webp', 'woff', 'woff2', 'xla', 'xlc', 'xlm', 'xls', 'xlsx', 'xlt', 'xlw', 'zip' ]
