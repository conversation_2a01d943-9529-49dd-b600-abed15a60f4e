# MDN data

> **Note:** We are in the process of deprecating the `mdn/data` package in favor of [`w3c/webref`](https://github.com/w3c/webref). If this could present a problem to your project, please contact us via our [GitHub discussions](https://github.com/mdn/mdn-community/discussions/categories/platform). Thank you.

[https://github.com/mdn/data](https://github.com/mdn/data)

Maintained by the [MDN team at Mozilla](https://wiki.mozilla.org/MDN).

This repository contains general data for Web technologies.

This data is used in MDN documentation, to build
[information boxes](https://developer.mozilla.org/en-US/docs/Web/CSS/background)
or [sidebar navigation](https://developer.mozilla.org/en-US/docs/Web/API/Window).
External tools have started to make use of this data as well.
For example, the [CSSTree](https://github.com/csstree/csstree/) CSS parser.

[![NPM version](https://img.shields.io/npm/v/mdn-data.svg)](https://www.npmjs.com/package/mdn-data)
[![lint](https://github.com/mdn/data/actions/workflows/lint.yml/badge.svg)](https://github.com/mdn/data/actions/workflows/lint.yml)

## Repository contents

There's a top-level directory for each broad area covered: for example, `api`
and `css`. Inside each of these directories is one or more
JSON files containing the data.

### api

Contains data about Web APIs:

- API inheritance (interface inheritance and mixin implementations)

### css

Contains data about:

- CSS at-rules
- CSS properties
- CSS selectors
- CSS syntaxes
- CSS types
- CSS units

Read more about [CSS data](https://github.com/mdn/data/blob/master/css/readme.md) and the format of the files.

### l10n

The l10n folder contains localization strings that are used in the various
json files throughout this repository.

## Problems?

If you find a problem, please [file an issue](https://github.com/mdn/data/issues/new).

## Contributing

We're very happy to accept contributions to this data. Please familiarize yourself
with the schema for the data you're editing, and send us a pull request. See also the [Contributing file](https://github.com/mdn/data/blob/master/CONTRIBUTING.md) for more information.

## See also

- [https://github.com/mdn/browser-compat-data](https://github.com/mdn/browser-compat-data)
  for compatibility data for Web technologies
