{"name": "@portabletext/block-tools", "version": "2.0.0", "description": "Can format HTML, Slate JSON or Sanity block array into any other format.", "keywords": ["portable-text", "block-tools"], "homepage": "https://www.sanity.io/", "bugs": {"url": "https://github.com/portabletext/editor/issues"}, "repository": {"type": "git", "url": "git+https://github.com/portabletext/editor.git", "directory": "packages/block-tools"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"source": "./src/index.ts", "import": "./lib/index.js", "require": "./lib/index.cjs", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "main": "./lib/index.cjs", "module": "./lib/index.js", "types": "./lib/index.d.ts", "files": ["lib", "src"], "dependencies": {"get-random-values-esm": "1.0.2", "lodash": "^4.17.21"}, "devDependencies": {"@sanity/pkg-utils": "^7.9.6", "@sanity/schema": "^4.0.1", "@sanity/types": "^4.0.1", "@types/jsdom": "^20.0.0", "@types/lodash": "^4.17.16", "@types/react": "^19.1.8", "@vercel/stega": "0.1.2", "@vitest/coverage-v8": "^3.2.4", "jsdom": "^26.0.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "peerDependencies": {"@sanity/types": "^4.0.1", "@types/react": "^18.3 || ^19"}, "publishConfig": {"access": "public"}, "scripts": {"build": "pkg-utils build --strict --check --clean", "check:lint": "biome lint .", "check:types": "tsc", "clean": "del .turbo && del lib && del node_modules", "dev": "pkg-utils watch", "lint:fix": "biome lint --write .", "test": "vitest --run", "test:unit": "vitest --run --project unit", "test:unit:watch": "vitest --project unit", "test:watch": "vitest"}}