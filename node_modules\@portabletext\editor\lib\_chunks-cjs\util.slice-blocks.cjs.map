{"version": 3, "file": "util.slice-blocks.cjs", "sources": ["../../src/utils/util.is-keyed-segment.ts", "../../src/selection/selection-point.ts", "../../src/internal-utils/asserters.ts", "../../src/internal-utils/parse-blocks.ts", "../../src/utils/util.block-offset.ts", "../../src/utils/util.get-block-start-point.ts", "../../src/utils/util.get-selection-end-point.ts", "../../src/utils/util.get-selection-start-point.ts", "../../src/utils/util.get-text-block-text.ts", "../../src/utils/util.is-span.ts", "../../src/utils/util.slice-blocks.ts"], "sourcesContent": ["import type {KeyedSegment} from '@sanity/types'\n\n/**\n * @public\n */\nexport function isKeyedSegment(segment: unknown): segment is KeyedSegment {\n  return typeof segment === 'object' && segment !== null && '_key' in segment\n}\n", "import type {EditorSelectionPoint} from '../types/editor'\nimport {isKeyedSegment} from '../utils/util.is-keyed-segment'\n\nexport function getBlockKeyFromSelectionPoint(point: EditorSelectionPoint) {\n  const blockPathSegment = point.path.at(0)\n\n  if (isKeyedSegment(blockPathSegment)) {\n    return blockPathSegment._key\n  }\n\n  return undefined\n}\n\nexport function getChildKeyFromSelectionPoint(point: EditorSelectionPoint) {\n  const childPathSegment = point.path.at(2)\n\n  if (isKeyedSegment(childPathSegment)) {\n    return childPathSegment._key\n  }\n\n  return undefined\n}\n", "import type {TypedObject} from '@sanity/types'\n\nexport function isTypedObject(object: unknown): object is TypedObject {\n  return isRecord(object) && typeof object._type === 'string'\n}\n\nfunction isRecord(value: unknown): value is Record<string, unknown> {\n  return !!value && (typeof value === 'object' || typeof value === 'function')\n}\n", "import type {\n  PortableTextBlock,\n  PortableTextListBlock,\n  PortableTextObject,\n  PortableTextSpan,\n  PortableTextTextBlock,\n  TypedObject,\n} from '@sanity/types'\nimport type {EditorSchema} from '../editor/editor-schema'\nimport type {EditorContext} from '../editor/editor-snapshot'\nimport {isTypedObject} from './asserters'\n\nexport function parseBlocks({\n  context,\n  blocks,\n  options,\n}: {\n  context: Pick<EditorContext, 'keyGenerator' | 'schema'>\n  blocks: unknown\n  options: {\n    refreshKeys: boolean\n    validateFields: boolean\n  }\n}): Array<PortableTextBlock> {\n  if (!Array.isArray(blocks)) {\n    return []\n  }\n\n  return blocks.flatMap((block) => {\n    const parsedBlock = parseBlock({context, block, options})\n\n    return parsedBlock ? [parsedBlock] : []\n  })\n}\n\nexport function parseBlock({\n  context,\n  block,\n  options,\n}: {\n  context: Pick<EditorContext, 'keyGenerator' | 'schema'>\n  block: unknown\n  options: {\n    refreshKeys: boolean\n    validateFields: boolean\n  }\n}): PortableTextBlock | undefined {\n  return (\n    parseTextBlock({block, context, options}) ??\n    parseBlockObject({blockObject: block, context, options})\n  )\n}\n\nexport function parseBlockObject({\n  blockObject,\n  context,\n  options,\n}: {\n  blockObject: unknown\n  context: Pick<EditorContext, 'keyGenerator' | 'schema'>\n  options: {refreshKeys: boolean; validateFields: boolean}\n}): PortableTextObject | undefined {\n  if (!isTypedObject(blockObject)) {\n    return undefined\n  }\n\n  const schemaType = context.schema.blockObjects.find(\n    ({name}) => name === blockObject._type,\n  )\n\n  if (!schemaType) {\n    return undefined\n  }\n\n  return parseObject({\n    object: blockObject,\n    context: {\n      keyGenerator: context.keyGenerator,\n      schemaType,\n    },\n    options,\n  })\n}\n\nexport function isListBlock(\n  context: Pick<EditorContext, 'schema'>,\n  block: unknown,\n): block is PortableTextListBlock {\n  return (\n    isTextBlock(context, block) &&\n    block.level !== undefined &&\n    block.listItem !== undefined\n  )\n}\n\nexport function isTextBlock(\n  context: Pick<EditorContext, 'schema'>,\n  block: unknown,\n): block is PortableTextTextBlock {\n  if (!isTypedObject(block)) {\n    return false\n  }\n\n  if (block._type !== context.schema.block.name) {\n    return false\n  }\n\n  if (!Array.isArray(block.children)) {\n    return false\n  }\n\n  return true\n}\n\nexport function parseTextBlock({\n  block,\n  context,\n  options,\n}: {\n  block: unknown\n  context: Pick<EditorContext, 'keyGenerator' | 'schema'>\n  options: {refreshKeys: boolean; validateFields: boolean}\n}): PortableTextTextBlock | undefined {\n  if (!isTypedObject(block)) {\n    return undefined\n  }\n\n  const customFields: Record<string, unknown> = {}\n\n  for (const key of Object.keys(block)) {\n    if (\n      key !== '_type' &&\n      key !== '_key' &&\n      key !== 'children' &&\n      key !== 'markDefs' &&\n      key !== 'style' &&\n      key !== 'listItem' &&\n      key !== 'level'\n    ) {\n      customFields[key] = block[key]\n    }\n  }\n\n  if (block._type !== context.schema.block.name) {\n    return undefined\n  }\n\n  const _key = options.refreshKeys\n    ? context.keyGenerator()\n    : typeof block._key === 'string'\n      ? block._key\n      : context.keyGenerator()\n\n  const unparsedMarkDefs: Array<unknown> = Array.isArray(block.markDefs)\n    ? block.markDefs\n    : []\n  const markDefKeyMap = new Map<string, string>()\n  const markDefs = unparsedMarkDefs.flatMap((markDef) => {\n    if (!isTypedObject(markDef)) {\n      return []\n    }\n\n    const schemaType = context.schema.annotations.find(\n      ({name}) => name === markDef._type,\n    )\n\n    if (!schemaType) {\n      return []\n    }\n\n    if (typeof markDef._key !== 'string') {\n      // If the `markDef` doesn't have a `_key` then we don't know what spans\n      // it belongs to and therefore we have to discard it.\n      return []\n    }\n\n    const parsedAnnotation = parseObject({\n      object: markDef,\n      context: {\n        schemaType,\n        keyGenerator: context.keyGenerator,\n      },\n      options,\n    })\n\n    if (!parsedAnnotation) {\n      return []\n    }\n\n    markDefKeyMap.set(markDef._key, parsedAnnotation._key)\n\n    return [parsedAnnotation]\n  })\n\n  const unparsedChildren: Array<unknown> = Array.isArray(block.children)\n    ? block.children\n    : []\n\n  const children = unparsedChildren\n    .map(\n      (child) =>\n        parseSpan({span: child, context, markDefKeyMap, options}) ??\n        parseInlineObject({inlineObject: child, context, options}),\n    )\n    .filter((child) => child !== undefined)\n\n  const parsedBlock: PortableTextTextBlock = {\n    _type: context.schema.block.name,\n    _key,\n    children:\n      children.length > 0\n        ? children\n        : [\n            {\n              _key: context.keyGenerator(),\n              _type: context.schema.span.name,\n              text: '',\n              marks: [],\n            },\n          ],\n    markDefs,\n    ...(options.validateFields ? {} : customFields),\n  }\n\n  if (\n    typeof block.style === 'string' &&\n    context.schema.styles.find((style) => style.name === block.style)\n  ) {\n    parsedBlock.style = block.style\n  } else {\n    const defaultStyle = context.schema.styles.at(0)?.name\n\n    if (defaultStyle !== undefined) {\n      parsedBlock.style = defaultStyle\n    } else {\n      console.error('Expected default style')\n    }\n  }\n\n  if (\n    typeof block.listItem === 'string' &&\n    context.schema.lists.find((list) => list.name === block.listItem)\n  ) {\n    parsedBlock.listItem = block.listItem\n  }\n\n  if (typeof block.level === 'number') {\n    parsedBlock.level = block.level\n  }\n\n  return parsedBlock\n}\n\nexport function isSpan(\n  context: Pick<EditorContext, 'schema'>,\n  child: unknown,\n): child is PortableTextSpan {\n  if (!isTypedObject(child)) {\n    return false\n  }\n\n  if (child._type !== context.schema.span.name) {\n    return false\n  }\n\n  if (typeof child.text !== 'string') {\n    return false\n  }\n\n  return true\n}\n\nexport function parseSpan({\n  span,\n  context,\n  markDefKeyMap,\n  options,\n}: {\n  span: unknown\n  context: Pick<EditorContext, 'keyGenerator' | 'schema'>\n  markDefKeyMap: Map<string, string>\n  options: {refreshKeys: boolean; validateFields: boolean}\n}): PortableTextSpan | undefined {\n  if (!isTypedObject(span)) {\n    return undefined\n  }\n\n  const customFields: Record<string, unknown> = {}\n\n  for (const key of Object.keys(span)) {\n    if (\n      key !== '_type' &&\n      key !== '_key' &&\n      key !== 'text' &&\n      key !== 'marks'\n    ) {\n      customFields[key] = span[key]\n    }\n  }\n\n  // In reality, the span schema name is always 'span', but we only the check here anyway\n  if (span._type !== context.schema.span.name || span._type !== 'span') {\n    return undefined\n  }\n\n  const unparsedMarks: Array<unknown> = Array.isArray(span.marks)\n    ? span.marks\n    : []\n  const marks = unparsedMarks.flatMap((mark) => {\n    if (typeof mark !== 'string') {\n      return []\n    }\n\n    const markDefKey = markDefKeyMap.get(mark)\n\n    if (markDefKey !== undefined) {\n      return [markDefKey]\n    }\n\n    if (\n      context.schema.decorators.some((decorator) => decorator.name === mark)\n    ) {\n      return [mark]\n    }\n\n    return []\n  })\n\n  return {\n    _type: 'span',\n    _key: options.refreshKeys\n      ? context.keyGenerator()\n      : typeof span._key === 'string'\n        ? span._key\n        : context.keyGenerator(),\n    text: typeof span.text === 'string' ? span.text : '',\n    marks,\n    ...(options.validateFields ? {} : customFields),\n  }\n}\n\nexport function parseInlineObject({\n  inlineObject,\n  context,\n  options,\n}: {\n  inlineObject: unknown\n  context: Pick<EditorContext, 'keyGenerator' | 'schema'>\n  options: {refreshKeys: boolean; validateFields: boolean}\n}): PortableTextObject | undefined {\n  if (!isTypedObject(inlineObject)) {\n    return undefined\n  }\n\n  const schemaType = context.schema.inlineObjects.find(\n    ({name}) => name === inlineObject._type,\n  )\n\n  if (!schemaType) {\n    return undefined\n  }\n\n  return parseObject({\n    object: inlineObject,\n    context: {\n      keyGenerator: context.keyGenerator,\n      schemaType,\n    },\n    options,\n  })\n}\n\nexport function parseAnnotation({\n  annotation,\n  context,\n  options,\n}: {\n  annotation: TypedObject\n  context: Pick<EditorContext, 'keyGenerator' | 'schema'>\n  options: {refreshKeys: boolean; validateFields: boolean}\n}): PortableTextObject | undefined {\n  if (!isTypedObject(annotation)) {\n    return undefined\n  }\n\n  const schemaType = context.schema.annotations.find(\n    ({name}) => name === annotation._type,\n  )\n\n  if (!schemaType) {\n    return undefined\n  }\n\n  return parseObject({\n    object: annotation,\n    context: {\n      keyGenerator: context.keyGenerator,\n      schemaType,\n    },\n    options,\n  })\n}\n\nfunction parseObject({\n  object,\n  context,\n  options,\n}: {\n  object: TypedObject\n  context: Pick<EditorContext, 'keyGenerator'> & {\n    schemaType: EditorSchema['blockObjects'][0]\n  }\n  options: {refreshKeys: boolean; validateFields: boolean}\n}): PortableTextObject {\n  const {_type, _key, ...customFields} = object\n\n  // Validates all props on the object and only takes those that match\n  // the name of a field\n  const values = options.validateFields\n    ? context.schemaType.fields.reduce<Record<string, unknown>>(\n        (fieldValues, field) => {\n          const fieldValue = object[field.name]\n\n          if (fieldValue !== undefined) {\n            fieldValues[field.name] = fieldValue\n          }\n\n          return fieldValues\n        },\n        {},\n      )\n    : customFields\n\n  return {\n    _type: context.schemaType.name,\n    _key: options.refreshKeys\n      ? context.keyGenerator()\n      : typeof object._key === 'string'\n        ? object._key\n        : context.keyGenerator(),\n    ...values,\n  }\n}\n", "import type {EditorContext} from '../editor/editor-snapshot'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {\n  getBlockKeyFromSelectionPoint,\n  getChildKeyFromSelectionPoint,\n} from '../selection/selection-point'\nimport type {BlockOffset} from '../types/block-offset'\nimport type {EditorSelectionPoint} from '../types/editor'\nimport type {ChildPath} from '../types/paths'\n\n/**\n * @public\n */\nexport function blockOffsetToSpanSelectionPoint({\n  context,\n  blockOffset,\n  direction,\n}: {\n  context: Pick<EditorContext, 'schema' | 'value'>\n  blockOffset: BlockOffset\n  direction: 'forward' | 'backward'\n}) {\n  let offsetLeft = blockOffset.offset\n  let selectionPoint: {path: ChildPath; offset: number} | undefined\n  let skippedInlineObject = false\n\n  for (const block of context.value) {\n    if (block._key !== blockOffset.path[0]._key) {\n      continue\n    }\n\n    if (!isTextBlock(context, block)) {\n      continue\n    }\n\n    for (const child of block.children) {\n      if (direction === 'forward') {\n        if (!isSpan(context, child)) {\n          continue\n        }\n\n        if (offsetLeft <= child.text.length) {\n          selectionPoint = {\n            path: [...blockOffset.path, 'children', {_key: child._key}],\n            offset: offsetLeft,\n          }\n          break\n        }\n\n        offsetLeft -= child.text.length\n\n        continue\n      }\n\n      if (!isSpan(context, child)) {\n        skippedInlineObject = true\n        continue\n      }\n\n      if (offsetLeft === 0 && selectionPoint && !skippedInlineObject) {\n        if (skippedInlineObject) {\n          selectionPoint = {\n            path: [...blockOffset.path, 'children', {_key: child._key}],\n            offset: 0,\n          }\n        }\n        break\n      }\n\n      if (offsetLeft > child.text.length) {\n        offsetLeft -= child.text.length\n        continue\n      }\n\n      if (offsetLeft <= child.text.length) {\n        selectionPoint = {\n          path: [...blockOffset.path, 'children', {_key: child._key}],\n          offset: offsetLeft,\n        }\n\n        offsetLeft -= child.text.length\n\n        if (offsetLeft !== 0) {\n          break\n        }\n      }\n    }\n  }\n\n  return selectionPoint\n}\n\n/**\n * @public\n */\nexport function spanSelectionPointToBlockOffset({\n  context,\n  selectionPoint,\n}: {\n  context: Pick<EditorContext, 'schema' | 'value'>\n  selectionPoint: EditorSelectionPoint\n}): BlockOffset | undefined {\n  let offset = 0\n\n  const blockKey = getBlockKeyFromSelectionPoint(selectionPoint)\n  const spanKey = getChildKeyFromSelectionPoint(selectionPoint)\n\n  if (!blockKey || !spanKey) {\n    return undefined\n  }\n\n  for (const block of context.value) {\n    if (block._key !== blockKey) {\n      continue\n    }\n\n    if (!isTextBlock(context, block)) {\n      continue\n    }\n\n    for (const child of block.children) {\n      if (!isSpan(context, child)) {\n        continue\n      }\n\n      if (child._key === spanKey) {\n        return {\n          path: [{_key: block._key}],\n          offset: offset + selectionPoint.offset,\n        }\n      }\n\n      offset += child.text.length\n    }\n  }\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorContext} from '../editor/editor-snapshot'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport type {EditorSelectionPoint} from '../types/editor'\nimport type {BlockPath} from '../types/paths'\n\n/**\n * @public\n */\nexport function getBlockStartPoint({\n  context,\n  block,\n}: {\n  context: Pick<EditorContext, 'schema'>\n  block: {\n    node: PortableTextBlock\n    path: BlockPath\n  }\n}): EditorSelectionPoint {\n  if (isTextBlock(context, block.node)) {\n    return {\n      path: [...block.path, 'children', {_key: block.node.children[0]._key}],\n      offset: 0,\n    }\n  }\n\n  return {\n    path: block.path,\n    offset: 0,\n  }\n}\n", "import type {EditorSelection, EditorSelectionPoint} from '..'\n\n/**\n * @public\n */\nexport function getSelectionEndPoint<\n  TEditorSelection extends NonNullable<EditorSelection> | null,\n  TEditorSelectionPoint extends\n    EditorSelectionPoint | null = TEditorSelection extends NonNullable<EditorSelection>\n    ? EditorSelectionPoint\n    : null,\n>(selection: TEditorSelection): TEditorSelectionPoint {\n  if (!selection) {\n    return null as TEditorSelectionPoint\n  }\n\n  return (\n    selection.backward ? selection.anchor : selection.focus\n  ) as TEditorSelectionPoint\n}\n", "import type {EditorSelection, EditorSelectionPoint} from '..'\n\n/**\n * @public\n */\nexport function getSelectionStartPoint<\n  TEditorSelection extends NonNullable<EditorSelection> | null,\n  TEditorSelectionPoint extends\n    EditorSelectionPoint | null = TEditorSelection extends NonNullable<EditorSelection>\n    ? EditorSelectionPoint\n    : null,\n>(selection: TEditorSelection): TEditorSelectionPoint {\n  if (!selection) {\n    return null as TEditorSelectionPoint\n  }\n\n  return (\n    selection.backward ? selection.focus : selection.anchor\n  ) as TEditorSelectionPoint\n}\n", "import type {PortableTextTextBlock} from '@sanity/types'\n\n/**\n * @public\n */\nexport function getTextBlockText(block: PortableTextTextBlock) {\n  return block.children.map((child) => child.text ?? '').join('')\n}\n", "import type {PortableTextChild, PortableTextSpan} from '@sanity/types'\nimport type {EditorContext} from '..'\n\n/**\n * @public\n */\nexport function isSpan(\n  context: Pick<EditorContext, 'schema'>,\n  child: PortableTextChild,\n): child is PortableTextSpan {\n  return child._type === context.schema.span.name\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorContext} from '..'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {\n  getBlockKeyFromSelectionPoint,\n  getChildKeyFromSelectionPoint,\n} from '../selection/selection-point'\nimport {getSelectionEndPoint} from '../utils/util.get-selection-end-point'\nimport {getSelectionStartPoint} from '../utils/util.get-selection-start-point'\n\n/**\n * @public\n */\nexport function sliceBlocks({\n  context,\n  blocks,\n}: {\n  context: Pick<EditorContext, 'schema' | 'selection'>\n  blocks: Array<PortableTextBlock>\n}): Array<PortableTextBlock> {\n  const slice: Array<PortableTextBlock> = []\n\n  if (!context.selection) {\n    return slice\n  }\n\n  let startBlock: PortableTextBlock | undefined\n  const middleBlocks: PortableTextBlock[] = []\n  let endBlock: PortableTextBlock | undefined\n\n  const startPoint = getSelectionStartPoint(context.selection)\n  const endPoint = getSelectionEndPoint(context.selection)\n  const startBlockKey = getBlockKeyFromSelectionPoint(startPoint)\n  const startChildKey = getChildKeyFromSelectionPoint(startPoint)\n  const endBlockKey = getBlockKeyFromSelectionPoint(endPoint)\n  const endChildKey = getChildKeyFromSelectionPoint(endPoint)\n\n  if (!startBlockKey || !endBlockKey) {\n    return slice\n  }\n\n  for (const block of blocks) {\n    if (!isTextBlock(context, block)) {\n      if (block._key === startBlockKey && block._key === endBlockKey) {\n        startBlock = block\n        break\n      }\n    }\n\n    if (block._key === startBlockKey) {\n      if (!isTextBlock(context, block)) {\n        startBlock = block\n        continue\n      }\n\n      if (startChildKey) {\n        for (const child of block.children) {\n          if (child._key === startChildKey) {\n            if (isSpan(context, child)) {\n              const text =\n                child._key === endChildKey\n                  ? child.text.slice(startPoint.offset, endPoint.offset)\n                  : child.text.slice(startPoint.offset)\n\n              startBlock = {\n                ...block,\n                children: [\n                  {\n                    ...child,\n                    text,\n                  },\n                ],\n              }\n            } else {\n              startBlock = {\n                ...block,\n                children: [child],\n              }\n            }\n\n            if (startChildKey === endChildKey) {\n              break\n            }\n            continue\n          }\n\n          if (startBlock && isTextBlock(context, startBlock)) {\n            if (\n              endChildKey &&\n              child._key === endChildKey &&\n              isSpan(context, child)\n            ) {\n              startBlock.children.push({\n                ...child,\n                text: child.text.slice(0, endPoint.offset),\n              })\n            } else {\n              startBlock.children.push(child)\n            }\n\n            if (\n              block._key === endBlockKey &&\n              endChildKey &&\n              child._key === endChildKey\n            ) {\n              break\n            }\n          }\n        }\n\n        if (startBlockKey === endBlockKey) {\n          break\n        }\n\n        continue\n      }\n\n      startBlock = block\n\n      if (startBlockKey === endBlockKey) {\n        break\n      }\n    }\n\n    if (block._key === endBlockKey) {\n      if (!isTextBlock(context, block)) {\n        endBlock = block\n        break\n      }\n\n      if (endChildKey) {\n        endBlock = {\n          ...block,\n          children: [],\n        }\n\n        for (const child of block.children) {\n          if (endBlock && isTextBlock(context, endBlock)) {\n            if (child._key === endChildKey && isSpan(context, child)) {\n              endBlock.children.push({\n                ...child,\n                text: child.text.slice(0, endPoint.offset),\n              })\n\n              break\n            }\n\n            endBlock.children.push(child)\n\n            if (endChildKey && child._key === endChildKey) {\n              break\n            }\n          }\n        }\n\n        break\n      }\n\n      endBlock = block\n\n      break\n    }\n\n    if (startBlock) {\n      middleBlocks.push(block)\n    }\n  }\n\n  return [\n    ...(startBlock ? [startBlock] : []),\n    ...middleBlocks,\n    ...(endBlock ? [endBlock] : []),\n  ]\n}\n"], "names": ["isKeyedSegment", "segment", "getBlockKeyFromSelectionPoint", "point", "blockPathSegment", "path", "at", "_key", "getChildKeyFromSelectionPoint", "childPathSegment", "isTypedObject", "object", "isRecord", "_type", "value", "parseBlocks", "context", "blocks", "options", "Array", "isArray", "flatMap", "block", "parsedBlock", "parseBlock", "parseTextBlock", "parseBlockObject", "blockObject", "schemaType", "schema", "blockObjects", "find", "name", "parseObject", "keyGenerator", "isListBlock", "isTextBlock", "level", "undefined", "listItem", "children", "customFields", "key", "Object", "keys", "refreshKeys", "unparsedMarkDefs", "markDefs", "markDefKeyMap", "Map", "markDef", "annotations", "parsedAnnotation", "set", "map", "child", "parseSpan", "span", "parseInlineObject", "inlineObject", "filter", "length", "text", "marks", "validateFields", "style", "styles", "defaultStyle", "console", "error", "lists", "list", "isSpan", "mark", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get", "decorators", "some", "decorator", "inlineObjects", "parseAnnotation", "annotation", "values", "fields", "reduce", "field<PERSON><PERSON><PERSON>", "field", "fieldValue", "blockOffsetToSpanSelectionPoint", "blockOffset", "direction", "offsetLeft", "offset", "selectionPoint", "skippedInlineObject", "spanSelectionPointToBlockOffset", "blockKey", "<PERSON><PERSON><PERSON>", "getBlockStartPoint", "node", "getSelectionEndPoint", "selection", "backward", "anchor", "focus", "getSelectionStartPoint", "getTextBlockText", "join", "sliceBlocks", "slice", "startBlock", "middleBlocks", "endBlock", "startPoint", "endPoint", "startBlockKey", "start<PERSON>hild<PERSON>ey", "endBlockKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "push"], "mappings": ";AAKO,SAASA,eAAeC,SAA2C;AACxE,SAAO,OAAOA,WAAY,YAAYA,YAAY,QAAQ,UAAUA;AACtE;ACJO,SAASC,8BAA8BC,OAA6B;AACzE,QAAMC,mBAAmBD,MAAME,KAAKC,GAAG,CAAC;AAExC,MAAIN,eAAeI,gBAAgB;AACjC,WAAOA,iBAAiBG;AAI5B;AAEO,SAASC,8BAA8BL,OAA6B;AACzE,QAAMM,mBAAmBN,MAAME,KAAKC,GAAG,CAAC;AAExC,MAAIN,eAAeS,gBAAgB;AACjC,WAAOA,iBAAiBF;AAI5B;ACnBO,SAASG,cAAcC,QAAwC;AACpE,SAAOC,SAASD,MAAM,KAAK,OAAOA,OAAOE,SAAU;AACrD;AAEA,SAASD,SAASE,OAAkD;AAClE,SAAO,CAAC,CAACA,UAAU,OAAOA,SAAU,YAAY,OAAOA,SAAU;AACnE;ACIO,SAASC,YAAY;AAAA,EAC1BC;AAAAA,EACAC;AAAAA,EACAC;AAQF,GAA6B;AAC3B,SAAKC,MAAMC,QAAQH,MAAM,IAIlBA,OAAOI,QAASC,CAAAA,UAAU;AAC/B,UAAMC,cAAcC,WAAW;AAAA,MAACR;AAAAA,MAASM;AAAAA,MAAOJ;AAAAA,IAAAA,CAAQ;AAExD,WAAOK,cAAc,CAACA,WAAW,IAAI,CAAA;AAAA,EACvC,CAAC,IAPQ,CAAA;AAQX;AAEO,SAASC,WAAW;AAAA,EACzBR;AAAAA,EACAM;AAAAA,EACAJ;AAQF,GAAkC;AAChC,SACEO,eAAe;AAAA,IAACH;AAAAA,IAAON;AAAAA,IAASE;AAAAA,EAAAA,CAAQ,KACxCQ,iBAAiB;AAAA,IAACC,aAAaL;AAAAA,IAAON;AAAAA,IAASE;AAAAA,EAAAA,CAAQ;AAE3D;AAEO,SAASQ,iBAAiB;AAAA,EAC/BC;AAAAA,EACAX;AAAAA,EACAE;AAKF,GAAmC;AACjC,MAAI,CAACR,cAAciB,WAAW;AAC5B;AAGF,QAAMC,aAAaZ,QAAQa,OAAOC,aAAaC,KAC7C,CAAC;AAAA,IAACC;AAAAA,EAAAA,MAAUA,SAASL,YAAYd,KACnC;AAEA,MAAKe;AAIL,WAAOK,YAAY;AAAA,MACjBtB,QAAQgB;AAAAA,MACRX,SAAS;AAAA,QACPkB,cAAclB,QAAQkB;AAAAA,QACtBN;AAAAA,MAAAA;AAAAA,MAEFV;AAAAA,IAAAA,CACD;AACH;AAEO,SAASiB,YACdnB,SACAM,OACgC;AAChC,SACEc,YAAYpB,SAASM,KAAK,KAC1BA,MAAMe,UAAUC,UAChBhB,MAAMiB,aAAaD;AAEvB;AAEO,SAASF,YACdpB,SACAM,OACgC;AAShC,SARI,EAAA,CAACZ,cAAcY,KAAK,KAIpBA,MAAMT,UAAUG,QAAQa,OAAOP,MAAMU,QAIrC,CAACb,MAAMC,QAAQE,MAAMkB,QAAQ;AAKnC;AAEO,SAASf,eAAe;AAAA,EAC7BH;AAAAA,EACAN;AAAAA,EACAE;AAKF,GAAsC;AACpC,MAAI,CAACR,cAAcY,KAAK;AACtB;AAGF,QAAMmB,eAAwC,CAAA;AAE9C,aAAWC,OAAOC,OAAOC,KAAKtB,KAAK;AAE/BoB,YAAQ,WACRA,QAAQ,UACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,WACRA,QAAQ,cACRA,QAAQ,YAERD,aAAaC,GAAG,IAAIpB,MAAMoB,GAAG;AAIjC,MAAIpB,MAAMT,UAAUG,QAAQa,OAAOP,MAAMU;AACvC;AAGF,QAAMzB,OAAOW,QAAQ2B,cACjB7B,QAAQkB,iBACR,OAAOZ,MAAMf,QAAS,WACpBe,MAAMf,OACNS,QAAQkB,gBAERY,mBAAmC3B,MAAMC,QAAQE,MAAMyB,QAAQ,IACjEzB,MAAMyB,WACN,CAAA,GACEC,gBAAgB,oBAAIC,IAAAA,GACpBF,WAAWD,iBAAiBzB,QAAS6B,CAAAA,YAAY;AACrD,QAAI,CAACxC,cAAcwC,OAAO;AACxB,aAAO,CAAA;AAGT,UAAMtB,aAAaZ,QAAQa,OAAOsB,YAAYpB,KAC5C,CAAC;AAAA,MAACC;AAAAA,IAAAA,MAAUA,SAASkB,QAAQrC,KAC/B;AAEA,QAAI,CAACe;AACH,aAAO,CAAA;AAGT,QAAI,OAAOsB,QAAQ3C,QAAS;AAG1B,aAAO,CAAA;AAGT,UAAM6C,mBAAmBnB,YAAY;AAAA,MACnCtB,QAAQuC;AAAAA,MACRlC,SAAS;AAAA,QACPY;AAAAA,QACAM,cAAclB,QAAQkB;AAAAA,MAAAA;AAAAA,MAExBhB;AAAAA,IAAAA,CACD;AAED,WAAKkC,oBAILJ,cAAcK,IAAIH,QAAQ3C,MAAM6C,iBAAiB7C,IAAI,GAE9C,CAAC6C,gBAAgB,KALf,CAAA;AAAA,EAMX,CAAC,GAMKZ,YAJmCrB,MAAMC,QAAQE,MAAMkB,QAAQ,IACjElB,MAAMkB,WACN,CAAA,GAGDc,IACEC,WACCC,UAAU;AAAA,IAACC,MAAMF;AAAAA,IAAOvC;AAAAA,IAASgC;AAAAA,IAAe9B;AAAAA,EAAAA,CAAQ,KACxDwC,kBAAkB;AAAA,IAACC,cAAcJ;AAAAA,IAAOvC;AAAAA,IAASE;AAAAA,EAAAA,CAAQ,CAC7D,EACC0C,OAAQL,WAAUA,UAAUjB,MAAS,GAElCf,cAAqC;AAAA,IACzCV,OAAOG,QAAQa,OAAOP,MAAMU;AAAAA,IAC5BzB;AAAAA,IACAiC,UACEA,SAASqB,SAAS,IACdrB,WACA,CACE;AAAA,MACEjC,MAAMS,QAAQkB,aAAAA;AAAAA,MACdrB,OAAOG,QAAQa,OAAO4B,KAAKzB;AAAAA,MAC3B8B,MAAM;AAAA,MACNC,OAAO,CAAA;AAAA,IAAA,CACR;AAAA,IAEThB;AAAAA,IACA,GAAI7B,QAAQ8C,iBAAiB,KAAKvB;AAAAA,EAAAA;AAGpC,MACE,OAAOnB,MAAM2C,SAAU,YACvBjD,QAAQa,OAAOqC,OAAOnC,KAAMkC,CAAAA,UAAUA,MAAMjC,SAASV,MAAM2C,KAAK;AAEhE1C,gBAAY0C,QAAQ3C,MAAM2C;AAAAA,OACrB;AACL,UAAME,eAAenD,QAAQa,OAAOqC,OAAO5D,GAAG,CAAC,GAAG0B;AAE9CmC,qBAAiB7B,SACnBf,YAAY0C,QAAQE,eAEpBC,QAAQC,MAAM,wBAAwB;AAAA,EAE1C;AAEA,SACE,OAAO/C,MAAMiB,YAAa,YAC1BvB,QAAQa,OAAOyC,MAAMvC,KAAMwC,CAAAA,SAASA,KAAKvC,SAASV,MAAMiB,QAAQ,MAEhEhB,YAAYgB,WAAWjB,MAAMiB,WAG3B,OAAOjB,MAAMe,SAAU,aACzBd,YAAYc,QAAQf,MAAMe,QAGrBd;AACT;AAEO,SAASiD,SACdxD,SACAuC,OAC2B;AAS3B,SARI,EAAA,CAAC7C,cAAc6C,KAAK,KAIpBA,MAAM1C,UAAUG,QAAQa,OAAO4B,KAAKzB,QAIpC,OAAOuB,MAAMO,QAAS;AAK5B;AAEO,SAASN,UAAU;AAAA,EACxBC;AAAAA,EACAzC;AAAAA,EACAgC;AAAAA,EACA9B;AAMF,GAAiC;AAC/B,MAAI,CAACR,cAAc+C,IAAI;AACrB;AAGF,QAAMhB,eAAwC,CAAA;AAE9C,aAAWC,OAAOC,OAAOC,KAAKa,IAAI;AAE9Bf,YAAQ,WACRA,QAAQ,UACRA,QAAQ,UACRA,QAAQ,YAERD,aAAaC,GAAG,IAAIe,KAAKf,GAAG;AAKhC,MAAIe,KAAK5C,UAAUG,QAAQa,OAAO4B,KAAKzB,QAAQyB,KAAK5C,UAAU;AAC5D;AAMF,QAAMkD,SAHgC5C,MAAMC,QAAQqC,KAAKM,KAAK,IAC1DN,KAAKM,QACL,CAAA,GACwB1C,QAASoD,CAAAA,SAAS;AAC5C,QAAI,OAAOA,QAAS;AAClB,aAAO,CAAA;AAGT,UAAMC,aAAa1B,cAAc2B,IAAIF,IAAI;AAEzC,WAAIC,eAAepC,SACV,CAACoC,UAAU,IAIlB1D,QAAQa,OAAO+C,WAAWC,KAAMC,CAAAA,cAAcA,UAAU9C,SAASyC,IAAI,IAE9D,CAACA,IAAI,IAGP,CAAA;AAAA,EACT,CAAC;AAED,SAAO;AAAA,IACL5D,OAAO;AAAA,IACPN,MAAMW,QAAQ2B,cACV7B,QAAQkB,aAAAA,IACR,OAAOuB,KAAKlD,QAAS,WACnBkD,KAAKlD,OACLS,QAAQkB,aAAAA;AAAAA,IACd4B,MAAM,OAAOL,KAAKK,QAAS,WAAWL,KAAKK,OAAO;AAAA,IAClDC;AAAAA,IACA,GAAI7C,QAAQ8C,iBAAiB,KAAKvB;AAAAA,EAAAA;AAEtC;AAEO,SAASiB,kBAAkB;AAAA,EAChCC;AAAAA,EACA3C;AAAAA,EACAE;AAKF,GAAmC;AACjC,MAAI,CAACR,cAAciD,YAAY;AAC7B;AAGF,QAAM/B,aAAaZ,QAAQa,OAAOkD,cAAchD,KAC9C,CAAC;AAAA,IAACC;AAAAA,EAAAA,MAAUA,SAAS2B,aAAa9C,KACpC;AAEA,MAAKe;AAIL,WAAOK,YAAY;AAAA,MACjBtB,QAAQgD;AAAAA,MACR3C,SAAS;AAAA,QACPkB,cAAclB,QAAQkB;AAAAA,QACtBN;AAAAA,MAAAA;AAAAA,MAEFV;AAAAA,IAAAA,CACD;AACH;AAEO,SAAS8D,gBAAgB;AAAA,EAC9BC;AAAAA,EACAjE;AAAAA,EACAE;AAKF,GAAmC;AACjC,MAAI,CAACR,cAAcuE,UAAU;AAC3B;AAGF,QAAMrD,aAAaZ,QAAQa,OAAOsB,YAAYpB,KAC5C,CAAC;AAAA,IAACC;AAAAA,EAAAA,MAAUA,SAASiD,WAAWpE,KAClC;AAEA,MAAKe;AAIL,WAAOK,YAAY;AAAA,MACjBtB,QAAQsE;AAAAA,MACRjE,SAAS;AAAA,QACPkB,cAAclB,QAAQkB;AAAAA,QACtBN;AAAAA,MAAAA;AAAAA,MAEFV;AAAAA,IAAAA,CACD;AACH;AAEA,SAASe,YAAY;AAAA,EACnBtB;AAAAA,EACAK;AAAAA,EACAE;AAOF,GAAuB;AACrB,QAAM;AAAA,IAACL;AAAAA,IAAON;AAAAA,IAAM,GAAGkC;AAAAA,EAAAA,IAAgB9B,QAIjCuE,SAAShE,QAAQ8C,iBACnBhD,QAAQY,WAAWuD,OAAOC,OACxB,CAACC,aAAaC,UAAU;AACtB,UAAMC,aAAa5E,OAAO2E,MAAMtD,IAAI;AAEpC,WAAIuD,eAAejD,WACjB+C,YAAYC,MAAMtD,IAAI,IAAIuD,aAGrBF;AAAAA,EACT,GACA,CAAA,CACF,IACA5C;AAEJ,SAAO;AAAA,IACL5B,OAAOG,QAAQY,WAAWI;AAAAA,IAC1BzB,MAAMW,QAAQ2B,cACV7B,QAAQkB,aAAAA,IACR,OAAOvB,OAAOJ,QAAS,WACrBI,OAAOJ,OACPS,QAAQkB,aAAAA;AAAAA,IACd,GAAGgD;AAAAA,EAAAA;AAEP;AC7aO,SAASM,gCAAgC;AAAA,EAC9CxE;AAAAA,EACAyE;AAAAA,EACAC;AAKF,GAAG;AACD,MAAIC,aAAaF,YAAYG,QACzBC,gBACAC,sBAAsB;AAE1B,aAAWxE,SAASN,QAAQF;AAC1B,QAAIQ,MAAMf,SAASkF,YAAYpF,KAAK,CAAC,EAAEE,QAIlC6B,YAAYpB,SAASM,KAAK;AAI/B,iBAAWiC,SAASjC,MAAMkB,UAAU;AAClC,YAAIkD,cAAc,WAAW;AAC3B,cAAI,CAAClB,SAAOxD,SAASuC,KAAK;AACxB;AAGF,cAAIoC,cAAcpC,MAAMO,KAAKD,QAAQ;AACnCgC,6BAAiB;AAAA,cACfxF,MAAM,CAAC,GAAGoF,YAAYpF,MAAM,YAAY;AAAA,gBAACE,MAAMgD,MAAMhD;AAAAA,cAAAA,CAAK;AAAA,cAC1DqF,QAAQD;AAAAA,YAAAA;AAEV;AAAA,UACF;AAEAA,wBAAcpC,MAAMO,KAAKD;AAEzB;AAAA,QACF;AAEA,YAAI,CAACW,SAAOxD,SAASuC,KAAK,GAAG;AAC3BuC,gCAAsB;AACtB;AAAA,QACF;AAEA,YAAIH,eAAe,KAAKE,kBAAkB,CAACC,qBAAqB;AAC1DA,kCACFD,iBAAiB;AAAA,YACfxF,MAAM,CAAC,GAAGoF,YAAYpF,MAAM,YAAY;AAAA,cAACE,MAAMgD,MAAMhD;AAAAA,YAAAA,CAAK;AAAA,YAC1DqF,QAAQ;AAAA,UAAA;AAGZ;AAAA,QACF;AAEA,YAAID,aAAapC,MAAMO,KAAKD,QAAQ;AAClC8B,wBAAcpC,MAAMO,KAAKD;AACzB;AAAA,QACF;AAEA,YAAI8B,cAAcpC,MAAMO,KAAKD,WAC3BgC,iBAAiB;AAAA,UACfxF,MAAM,CAAC,GAAGoF,YAAYpF,MAAM,YAAY;AAAA,YAACE,MAAMgD,MAAMhD;AAAAA,UAAAA,CAAK;AAAA,UAC1DqF,QAAQD;AAAAA,QAAAA,GAGVA,cAAcpC,MAAMO,KAAKD,QAErB8B,eAAe;AACjB;AAAA,MAGN;AAGF,SAAOE;AACT;AAKO,SAASE,gCAAgC;AAAA,EAC9C/E;AAAAA,EACA6E;AAIF,GAA4B;AAC1B,MAAID,SAAS;AAEb,QAAMI,WAAW9F,8BAA8B2F,cAAc,GACvDI,UAAUzF,8BAA8BqF,cAAc;AAE5D,MAAI,EAAA,CAACG,YAAY,CAACC;AAIlB,eAAW3E,SAASN,QAAQF;AAC1B,UAAIQ,MAAMf,SAASyF,YAId5D,YAAYpB,SAASM,KAAK;AAI/B,mBAAWiC,SAASjC,MAAMkB;AACxB,cAAKgC,SAAOxD,SAASuC,KAAK,GAI1B;AAAA,gBAAIA,MAAMhD,SAAS0F;AACjB,qBAAO;AAAA,gBACL5F,MAAM,CAAC;AAAA,kBAACE,MAAMe,MAAMf;AAAAA,gBAAAA,CAAK;AAAA,gBACzBqF,QAAQA,SAASC,eAAeD;AAAAA,cAAAA;AAIpCA,sBAAUrC,MAAMO,KAAKD;AAAAA,UAAAA;AAAAA;AAAAA;AAG3B;AC9HO,SAASqC,mBAAmB;AAAA,EACjClF;AAAAA,EACAM;AAOF,GAAyB;AACvB,SAAIc,YAAYpB,SAASM,MAAM6E,IAAI,IAC1B;AAAA,IACL9F,MAAM,CAAC,GAAGiB,MAAMjB,MAAM,YAAY;AAAA,MAACE,MAAMe,MAAM6E,KAAK3D,SAAS,CAAC,EAAEjC;AAAAA,IAAAA,CAAK;AAAA,IACrEqF,QAAQ;AAAA,EAAA,IAIL;AAAA,IACLvF,MAAMiB,MAAMjB;AAAAA,IACZuF,QAAQ;AAAA,EAAA;AAEZ;ACzBO,SAASQ,qBAMdC,WAAoD;AACpD,SAAKA,YAKHA,UAAUC,WAAWD,UAAUE,SAASF,UAAUG,QAJ3C;AAMX;ACdO,SAASC,uBAMdJ,WAAoD;AACpD,SAAKA,YAKHA,UAAUC,WAAWD,UAAUG,QAAQH,UAAUE,SAJ1C;AAMX;ACdO,SAASG,iBAAiBpF,OAA8B;AAC7D,SAAOA,MAAMkB,SAASc,IAAKC,CAAAA,UAAUA,MAAMO,QAAQ,EAAE,EAAE6C,KAAK,EAAE;AAChE;ACDO,SAASnC,OACdxD,SACAuC,OAC2B;AAC3B,SAAOA,MAAM1C,UAAUG,QAAQa,OAAO4B,KAAKzB;AAC7C;ACEO,SAAS4E,YAAY;AAAA,EAC1B5F;AAAAA,EACAC;AAIF,GAA6B;AAC3B,QAAM4F,QAAkC,CAAA;AAExC,MAAI,CAAC7F,QAAQqF;AACX,WAAOQ;AAGT,MAAIC;AACJ,QAAMC,eAAoC,CAAA;AAC1C,MAAIC;AAEJ,QAAMC,aAAaR,uBAAuBzF,QAAQqF,SAAS,GACrDa,WAAWd,qBAAqBpF,QAAQqF,SAAS,GACjDc,gBAAgBjH,8BAA8B+G,UAAU,GACxDG,gBAAgB5G,8BAA8ByG,UAAU,GACxDI,cAAcnH,8BAA8BgH,QAAQ,GACpDI,cAAc9G,8BAA8B0G,QAAQ;AAE1D,MAAI,CAACC,iBAAiB,CAACE;AACrB,WAAOR;AAGT,aAAWvF,SAASL,QAAQ;AAC1B,QAAI,CAACmB,YAAYpB,SAASM,KAAK,KACzBA,MAAMf,SAAS4G,iBAAiB7F,MAAMf,SAAS8G,aAAa;AAC9DP,mBAAaxF;AACb;AAAA,IACF;AAGF,QAAIA,MAAMf,SAAS4G,eAAe;AAChC,UAAI,CAAC/E,YAAYpB,SAASM,KAAK,GAAG;AAChCwF,qBAAaxF;AACb;AAAA,MACF;AAEA,UAAI8F,eAAe;AACjB,mBAAW7D,SAASjC,MAAMkB,UAAU;AAClC,cAAIe,MAAMhD,SAAS6G,eAAe;AAChC,gBAAI5C,SAAOxD,SAASuC,KAAK,GAAG;AAC1B,oBAAMO,OACJP,MAAMhD,SAAS+G,cACX/D,MAAMO,KAAK+C,MAAMI,WAAWrB,QAAQsB,SAAStB,MAAM,IACnDrC,MAAMO,KAAK+C,MAAMI,WAAWrB,MAAM;AAExCkB,2BAAa;AAAA,gBACX,GAAGxF;AAAAA,gBACHkB,UAAU,CACR;AAAA,kBACE,GAAGe;AAAAA,kBACHO;AAAAA,gBAAAA,CACD;AAAA,cAAA;AAAA,YAGP;AACEgD,2BAAa;AAAA,gBACX,GAAGxF;AAAAA,gBACHkB,UAAU,CAACe,KAAK;AAAA,cAAA;AAIpB,gBAAI6D,kBAAkBE;AACpB;AAEF;AAAA,UACF;AAEA,cAAIR,cAAc1E,YAAYpB,SAAS8F,UAAU,MAE7CQ,eACA/D,MAAMhD,SAAS+G,eACf9C,SAAOxD,SAASuC,KAAK,IAErBuD,WAAWtE,SAAS+E,KAAK;AAAA,YACvB,GAAGhE;AAAAA,YACHO,MAAMP,MAAMO,KAAK+C,MAAM,GAAGK,SAAStB,MAAM;AAAA,UAAA,CAC1C,IAEDkB,WAAWtE,SAAS+E,KAAKhE,KAAK,GAI9BjC,MAAMf,SAAS8G,eACfC,eACA/D,MAAMhD,SAAS+G;AAEf;AAAA,QAGN;AAEA,YAAIH,kBAAkBE;AACpB;AAGF;AAAA,MACF;AAIA,UAFAP,aAAaxF,OAET6F,kBAAkBE;AACpB;AAAA,IAEJ;AAEA,QAAI/F,MAAMf,SAAS8G,aAAa;AAC9B,UAAI,CAACjF,YAAYpB,SAASM,KAAK,GAAG;AAChC0F,mBAAW1F;AACX;AAAA,MACF;AAEA,UAAIgG,aAAa;AACfN,mBAAW;AAAA,UACT,GAAG1F;AAAAA,UACHkB,UAAU,CAAA;AAAA,QAAA;AAGZ,mBAAWe,SAASjC,MAAMkB;AACxB,cAAIwE,YAAY5E,YAAYpB,SAASgG,QAAQ,GAAG;AAC9C,gBAAIzD,MAAMhD,SAAS+G,eAAe9C,SAAOxD,SAASuC,KAAK,GAAG;AACxDyD,uBAASxE,SAAS+E,KAAK;AAAA,gBACrB,GAAGhE;AAAAA,gBACHO,MAAMP,MAAMO,KAAK+C,MAAM,GAAGK,SAAStB,MAAM;AAAA,cAAA,CAC1C;AAED;AAAA,YACF;AAIA,gBAFAoB,SAASxE,SAAS+E,KAAKhE,KAAK,GAExB+D,eAAe/D,MAAMhD,SAAS+G;AAChC;AAAA,UAEJ;AAGF;AAAA,MACF;AAEAN,iBAAW1F;AAEX;AAAA,IACF;AAEIwF,kBACFC,aAAaQ,KAAKjG,KAAK;AAAA,EAE3B;AAEA,SAAO,CACL,GAAIwF,aAAa,CAACA,UAAU,IAAI,CAAA,GAChC,GAAGC,cACH,GAAIC,WAAW,CAACA,QAAQ,IAAI,CAAA,CAAG;AAEnC;;;;;;;;;;;;;;;;;;;;"}