#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * BAKASANA Asset Minification & Optimization
 * Minifies CSS/JS and optimizes bundle for Lighthouse >95 performance
 */

class AssetMinifier {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.buildDir = path.join(this.projectRoot, '.next');
    this.stylesDir = path.join(this.projectRoot, 'src/styles');
    this.results = {
      css: { before: 0, after: 0, savings: 0 },
      js: { before: 0, after: 0, savings: 0 },
    };
  }

  // Minify CSS files
  async minifyCSS() {
    console.log('🎨 Minifying CSS files...');

    const cssFiles = this.findFiles(this.stylesDir, '.css');
    let totalBefore = 0;
    let totalAfter = 0;

    for (const cssFile of cssFiles) {
      try {
        const originalContent = fs.readFileSync(cssFile, 'utf8');
        const originalSize = originalContent.length;
        totalBefore += originalSize;

        // Advanced CSS minification
        const minifiedContent = this.minifyCSS_Advanced(originalContent);
        const minifiedSize = minifiedContent.length;
        totalAfter += minifiedSize;

        // Create minified version
        const minifiedPath = cssFile.replace('.css', '.min.css');
        fs.writeFileSync(minifiedPath, minifiedContent);

        const savings = Math.round(
          ((originalSize - minifiedSize) / originalSize) * 100
        );
        console.log(
          `  ✅ ${path.basename(cssFile)}: ${originalSize} → ${minifiedSize} bytes (${savings}% saved)`
        );
      } catch (error) {
        console.error(`  ❌ Error minifying ${cssFile}:`, error.message);
      }
    }

    this.results.css = {
      before: totalBefore,
      after: totalAfter,
      savings: Math.round(((totalBefore - totalAfter) / totalBefore) * 100),
    };

    console.log(
      `📊 CSS Total: ${totalBefore} → ${totalAfter} bytes (${this.results.css.savings}% saved)`
    );
  }

  // Advanced CSS minification
  minifyCSS_Advanced(css) {
    return (
      css
        // Remove comments
        .replace(/\/\*[\s\S]*?\*\//g, '')
        // Remove unnecessary whitespace
        .replace(/\s+/g, ' ')
        // Remove whitespace around special characters
        .replace(/\s*([{}:;,>+~])\s*/g, '$1')
        // Remove trailing semicolons
        .replace(/;}/g, '}')
        // Remove empty rules
        .replace(/[^{}]+{\s*}/g, '')
        // Optimize colors
        .replace(/#([a-f0-9])\1([a-f0-9])\2([a-f0-9])\3/gi, '#$1$2$3')
        // Remove unnecessary quotes
        .replace(/url\((['"]?)([^'")]+)\1\)/g, 'url($2)')
        // Optimize zero values
        .replace(/\b0px\b/g, '0')
        .replace(/\b0em\b/g, '0')
        .replace(/\b0rem\b/g, '0')
        .replace(/\b0%\b/g, '0')
        // Remove leading zeros
        .replace(/\b0+(\.\d+)/g, '$1')
        .trim()
    );
  }

  // Optimize JavaScript bundles
  async optimizeJS() {
    console.log('📦 Analyzing JavaScript bundles...');

    const buildManifest = path.join(this.buildDir, 'build-manifest.json');
    if (!fs.existsSync(buildManifest)) {
      console.log('⚠️ Build manifest not found. Run npm run build first.');
      return;
    }

    const manifest = JSON.parse(fs.readFileSync(buildManifest, 'utf8'));
    let totalSize = 0;

    // Analyze bundle sizes
    Object.entries(manifest.pages).forEach(([page, files]) => {
      files.forEach(file => {
        if (file.endsWith('.js')) {
          const filePath = path.join(this.buildDir, 'static', file);
          if (fs.existsSync(filePath)) {
            const size = fs.statSync(filePath).size;
            totalSize += size;

            if (size > 100000) {
              // Files larger than 100KB
              console.log(
                `  ⚠️ Large bundle: ${file} (${Math.round(size / 1024)}KB)`
              );
            }
          }
        }
      });
    });

    this.results.js.before = totalSize;
    console.log(`📊 Total JS Bundle Size: ${Math.round(totalSize / 1024)}KB`);

    // Generate bundle analysis recommendations
    this.generateBundleRecommendations(totalSize);
  }

  // Generate bundle optimization recommendations
  generateBundleRecommendations(totalSize) {
    const recommendations = [];

    if (totalSize > 300000) {
      // > 300KB
      recommendations.push({
        issue: 'Large JavaScript bundle size',
        solution: 'Implement dynamic imports for non-critical components',
        code: `
// Example: Dynamic import for non-critical components
const LazyComponent = dynamic(() => import('./LazyComponent'), {
  loading: () => <div>Loading...</div>
});

// Split vendor libraries
const Chart = dynamic(() => import('react-chartjs-2'), {
  ssr: false
});`,
      });
    }

    if (recommendations.length > 0) {
      console.log('\n💡 Bundle Optimization Recommendations:');
      recommendations.forEach((rec, index) => {
        console.log(`\n${index + 1}. ${rec.issue}`);
        console.log(`   Solution: ${rec.solution}`);
        if (rec.code) {
          console.log(`   Code example:${rec.code}`);
        }
      });
    }
  }

  // Create optimized Tailwind CSS configuration
  createOptimizedTailwindConfig() {
    console.log('🎨 Creating optimized Tailwind configuration...');

    const optimizedConfig = `
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // Only include colors actually used
      colors: {
        'sanctuary': '#FDFCF8',
        'linen': '#F5F1E8',
        'temple-gold': '#C9A575',
        'charcoal': '#2C2C2C',
      },
      // Only include fonts actually used
      fontFamily: {
        'primary': ['Cormorant Garamond', 'serif'],
        'secondary': ['Inter', 'sans-serif'],
      },
      // Optimize animations
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
  // Optimize for production
  corePlugins: {
    // Disable unused core plugins
    preflight: true,
    container: false, // Use custom container
    accessibility: true,
    pointerEvents: true,
    visibility: true,
    position: true,
    inset: true,
    isolation: false, // Rarely used
    zIndex: true,
    order: false, // Use flexbox order sparingly
    gridColumn: true,
    gridColumnStart: false,
    gridColumnEnd: false,
    gridRow: true,
    gridRowStart: false,
    gridRowEnd: false,
    float: false, // Modern layouts don't need float
    clear: false,
    boxSizing: true,
    display: true,
    aspectRatio: true,
    size: true,
    height: true,
    maxHeight: true,
    minHeight: true,
    width: true,
    minWidth: true,
    maxWidth: true,
    flex: true,
    flexShrink: true,
    flexGrow: true,
    flexBasis: true,
    tableLayout: false, // Rarely used
    captionSide: false,
    borderCollapse: false,
    borderSpacing: false,
    transformOrigin: true,
    translate: true,
    rotate: true,
    skew: false, // Rarely used
    scale: true,
    transform: true,
    animation: true,
    cursor: true,
    touchAction: true,
    userSelect: true,
    resize: false, // Rarely used
    scrollSnapType: false,
    scrollSnapAlign: false,
    scrollSnapStop: false,
    scrollMargin: false,
    scrollPadding: false,
    listStylePosition: false,
    listStyleType: false,
    appearance: true,
    columns: false, // CSS columns rarely used
    breakBefore: false,
    breakInside: false,
    breakAfter: false,
    gridAutoColumns: false,
    gridAutoFlow: false,
    gridAutoRows: false,
    gridTemplateColumns: true,
    gridTemplateRows: true,
    flexDirection: true,
    flexWrap: true,
    placeContent: true,
    placeItems: true,
    alignContent: true,
    alignItems: true,
    justifyContent: true,
    justifyItems: true,
    gap: true,
    space: true,
    divideWidth: false, // Use borders instead
    divideColor: false,
    divideStyle: false,
    divideOpacity: false,
    placeSelf: false,
    alignSelf: true,
    justifySelf: false,
    overflow: true,
    overscrollBehavior: false,
    scrollBehavior: true,
    textOverflow: true,
    hyphens: false,
    whitespace: true,
    wordBreak: false,
    borderRadius: true,
    borderWidth: true,
    borderColor: true,
    borderStyle: true,
    borderOpacity: true,
    backgroundColor: true,
    backgroundOpacity: true,
    backgroundImage: true,
    gradientColorStops: true,
    backgroundSize: true,
    backgroundAttachment: false,
    backgroundClip: true,
    backgroundPosition: true,
    backgroundRepeat: true,
    backgroundOrigin: false,
    fill: true,
    stroke: true,
    strokeWidth: true,
    objectFit: true,
    objectPosition: true,
    padding: true,
    margin: true,
    fontFamily: true,
    fontSize: true,
    fontWeight: true,
    fontVariantNumeric: false,
    letterSpacing: true,
    lineHeight: true,
    listStyleImage: false,
    textAlign: true,
    textColor: true,
    textOpacity: true,
    textDecoration: true,
    textDecorationColor: false,
    textDecorationStyle: false,
    textDecorationThickness: false,
    textUnderlineOffset: false,
    fontStyle: true,
    fontVariant: false,
    fontSmoothing: true,
    tabSize: false,
    textIndent: false,
    verticalAlign: false,
    opacity: true,
    boxShadow: true,
    boxShadowColor: false,
    outlineWidth: true,
    outlineColor: true,
    outlineStyle: true,
    outlineOffset: true,
    ringWidth: true,
    ringColor: true,
    ringOpacity: true,
    ringOffsetWidth: true,
    ringOffsetColor: true,
    blur: true,
    brightness: false,
    contrast: false,
    dropShadow: false,
    grayscale: false,
    hueRotate: false,
    invert: false,
    saturate: false,
    sepia: false,
    filter: true,
    backdropBlur: true,
    backdropBrightness: false,
    backdropContrast: false,
    backdropGrayscale: false,
    backdropHueRotate: false,
    backdropInvert: false,
    backdropOpacity: true,
    backdropSaturate: false,
    backdropSepia: false,
    backdropFilter: true,
    transitionProperty: true,
    transitionDelay: true,
    transitionDuration: true,
    transitionTimingFunction: true,
    willChange: false,
    content: true,
  },
};`;

    const configPath = path.join(
      this.projectRoot,
      'tailwind.config.optimized.js'
    );
    fs.writeFileSync(configPath, optimizedConfig);
    console.log(`✅ Optimized Tailwind config saved to: ${configPath}`);
  }

  // Helper methods
  findFiles(dir, extension) {
    const files = [];
    if (!fs.existsSync(dir)) return files;

    const items = fs.readdirSync(dir);
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        files.push(...this.findFiles(itemPath, extension));
      } else if (item.endsWith(extension)) {
        files.push(itemPath);
      }
    });

    return files;
  }

  // Generate performance report
  generateReport() {
    console.log('\n📊 ASSET OPTIMIZATION REPORT');
    console.log('==============================');

    if (this.results.css.before > 0) {
      console.log(`\n🎨 CSS Optimization:`);
      console.log(`   Before: ${Math.round(this.results.css.before / 1024)}KB`);
      console.log(`   After: ${Math.round(this.results.css.after / 1024)}KB`);
      console.log(`   Savings: ${this.results.css.savings}%`);
    }

    if (this.results.js.before > 0) {
      console.log(`\n📦 JavaScript Analysis:`);
      console.log(
        `   Total Bundle: ${Math.round(this.results.js.before / 1024)}KB`
      );
      console.log(
        `   Status: ${this.results.js.before > 300000 ? 'NEEDS OPTIMIZATION' : 'GOOD'}`
      );
    }

    console.log('\n🎯 Next Steps:');
    console.log('   1. Run image optimization: npm run optimize-images');
    console.log('   2. Extract critical CSS: node scripts/critical-css.js');
    console.log('   3. Test performance: npm run build && npm start');
    console.log('   4. Run Lighthouse audit');
  }

  // Run complete asset optimization
  async run() {
    console.log('🚀 BAKASANA Asset Optimization Started\n');

    await this.minifyCSS();
    await this.optimizeJS();
    this.createOptimizedTailwindConfig();
    this.generateReport();

    console.log('\n✅ Asset optimization complete!');
  }
}

// Run the asset minifier
if (require.main === module) {
  const minifier = new AssetMinifier();
  minifier.run().catch(console.error);
}

module.exports = AssetMinifier;
