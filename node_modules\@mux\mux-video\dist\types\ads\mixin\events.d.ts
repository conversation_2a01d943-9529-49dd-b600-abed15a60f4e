export declare class AdEvent extends Event {
}
export declare const Events: {
    readonly AD_REQUEST: "adrequest";
    readonly AD_RESPONSE: "adresponse";
    readonly AD_BREAK_START: "adbreakstart";
    readonly AD_FIRST_QUARTILE: "adfirstquartile";
    readonly AD_MIDPOINT: "admidpoint";
    readonly AD_THIRD_QUARTILE: "adthirdquartile";
    readonly AD_ENDED: "adended";
    readonly AD_BREAK_END: "adbreakend";
    readonly AD_ERROR: "aderror";
    readonly AD_PLAY: "adplay";
    readonly AD_PLAYING: "adplaying";
    readonly AD_PAUSE: "adpause";
    readonly AD_IMPRESSION: "adimpression";
    readonly AD_CLICK: "adclick";
    readonly AD_SKIP: "adskip";
    readonly AD_CLOSE: "adclose";
    readonly PLAY: "play";
    readonly PLAYING: "playing";
    readonly PAUSE: "pause";
    readonly VOLUME_CHANGE: "volumechange";
    readonly TIME_UPDATE: "timeupdate";
    readonly DURATION_CHANGE: "durationchange";
    readonly WAITING: "waiting";
};
