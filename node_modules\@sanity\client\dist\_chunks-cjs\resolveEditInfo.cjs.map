{"version": 3, "file": "resolveEditInfo.cjs", "sources": ["../../src/csm/studioPath.ts", "../../src/csm/draftUtils.ts", "../../src/csm/jsonPath.ts", "../../src/csm/resolveMapping.ts", "../../src/csm/isArray.ts", "../../src/csm/walkMap.ts", "../../src/csm/createEditUrl.ts", "../../src/csm/resolveEditInfo.ts"], "sourcesContent": ["/** @alpha */\nexport type KeyedSegment = {_key: string}\n\n/** @alpha */\nexport type IndexTuple = [number | '', number | '']\n\n/** @alpha */\nexport type PathSegment = string | number | KeyedSegment | IndexTuple\n\n/** @alpha */\nexport type Path = PathSegment[]\n\nconst rePropName =\n  /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g\n/** @internal */\nexport const reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/\nconst reIndexTuple = /^\\d*:\\d*$/\n\n/** @internal */\nexport function isIndexSegment(segment: PathSegment): segment is number {\n  return typeof segment === 'number' || (typeof segment === 'string' && /^\\[\\d+\\]$/.test(segment))\n}\n\n/** @internal */\nexport function isKeySegment(segment: PathSegment): segment is KeyedSegment {\n  if (typeof segment === 'string') {\n    return reKeySegment.test(segment.trim())\n  }\n\n  return typeof segment === 'object' && '_key' in segment\n}\n\n/** @internal */\nexport function isIndexTuple(segment: PathSegment): segment is IndexTuple {\n  if (typeof segment === 'string' && reIndexTuple.test(segment)) {\n    return true\n  }\n\n  if (!Array.isArray(segment) || segment.length !== 2) {\n    return false\n  }\n\n  const [from, to] = segment\n  return (typeof from === 'number' || from === '') && (typeof to === 'number' || to === '')\n}\n\n/** @internal */\nexport function get<Result = unknown, Fallback = unknown>(\n  obj: unknown,\n  path: Path | string,\n  defaultVal?: Fallback,\n): Result | typeof defaultVal {\n  const select = typeof path === 'string' ? fromString(path) : path\n  if (!Array.isArray(select)) {\n    throw new Error('Path must be an array or a string')\n  }\n\n  let acc: unknown | undefined = obj\n  for (let i = 0; i < select.length; i++) {\n    const segment = select[i]\n    if (isIndexSegment(segment)) {\n      if (!Array.isArray(acc)) {\n        return defaultVal\n      }\n\n      acc = acc[segment]\n    }\n\n    if (isKeySegment(segment)) {\n      if (!Array.isArray(acc)) {\n        return defaultVal\n      }\n\n      acc = acc.find((item) => item._key === segment._key)\n    }\n\n    if (typeof segment === 'string') {\n      acc =\n        typeof acc === 'object' && acc !== null\n          ? ((acc as Record<string, unknown>)[segment] as Result)\n          : undefined\n    }\n\n    if (typeof acc === 'undefined') {\n      return defaultVal\n    }\n  }\n\n  return acc as Result\n}\n\n/** @alpha */\nexport function toString(path: Path): string {\n  if (!Array.isArray(path)) {\n    throw new Error('Path is not an array')\n  }\n\n  return path.reduce<string>((target, segment, i) => {\n    const segmentType = typeof segment\n    if (segmentType === 'number') {\n      return `${target}[${segment}]`\n    }\n\n    if (segmentType === 'string') {\n      const separator = i === 0 ? '' : '.'\n      return `${target}${separator}${segment}`\n    }\n\n    if (isKeySegment(segment) && segment._key) {\n      return `${target}[_key==\"${segment._key}\"]`\n    }\n\n    if (Array.isArray(segment)) {\n      const [from, to] = segment\n      return `${target}[${from}:${to}]`\n    }\n\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``)\n  }, '')\n}\n\n/** @alpha */\nexport function fromString(path: string): Path {\n  if (typeof path !== 'string') {\n    throw new Error('Path is not a string')\n  }\n\n  const segments = path.match(rePropName)\n  if (!segments) {\n    throw new Error('Invalid path string')\n  }\n\n  return segments.map(parsePathSegment)\n}\n\nfunction parsePathSegment(segment: string): PathSegment {\n  if (isIndexSegment(segment)) {\n    return parseIndexSegment(segment)\n  }\n\n  if (isKeySegment(segment)) {\n    return parseKeySegment(segment)\n  }\n\n  if (isIndexTuple(segment)) {\n    return parseIndexTupleSegment(segment)\n  }\n\n  return segment\n}\n\nfunction parseIndexSegment(segment: string): PathSegment {\n  return Number(segment.replace(/[^\\d]/g, ''))\n}\n\nfunction parseKeySegment(segment: string): KeyedSegment {\n  const segments = segment.match(reKeySegment)\n  return {_key: segments![1]}\n}\n\nfunction parseIndexTupleSegment(segment: string): IndexTuple {\n  const [from, to] = segment.split(':').map((seg) => (seg === '' ? seg : Number(seg)))\n  return [from, to]\n}\n", "// nominal/opaque type hack\ntype Opaque<T, K> = T & {__opaqueId__: K}\n\n/** @internal */\nexport type DraftId = Opaque<string, 'draftId'>\n\n/** @internal */\nexport type PublishedId = Opaque<string, 'publishedId'>\n\n/** @internal */\nexport const DRAFTS_FOLDER = 'drafts'\n\n/** @internal */\nexport const VERSION_FOLDER = 'versions'\n\nconst PATH_SEPARATOR = '.'\nconst DRAFTS_PREFIX = `${DRAFTS_FOLDER}${PATH_SEPARATOR}`\nconst VERSION_PREFIX = `${VERSION_FOLDER}${PATH_SEPARATOR}`\n\n/** @internal */\nexport function isDraftId(id: string): id is DraftId {\n  return id.startsWith(DRAFTS_PREFIX)\n}\n\n/** @internal */\nexport function isVersionId(id: string): boolean {\n  return id.startsWith(VERSION_PREFIX)\n}\n\n/** @internal */\nexport function isPublishedId(id: string): id is PublishedId {\n  return !isDraftId(id) && !isVersionId(id)\n}\n\n/** @internal */\nexport function getDraftId(id: string): DraftId {\n  if (isVersionId(id)) {\n    const publishedId = getPublishedId(id)\n    return (DRAFTS_PREFIX + publishedId) as DraftId\n  }\n\n  return isDraftId(id) ? id : ((DRAFTS_PREFIX + id) as DraftId)\n}\n\n/**  @internal */\nexport function getVersionId(id: string, version: string): string {\n  if (version === 'drafts' || version === 'published') {\n    throw new Error('Version can not be \"published\" or \"drafts\"')\n  }\n\n  return `${VERSION_PREFIX}${version}${PATH_SEPARATOR}${getPublishedId(id)}`\n}\n\n/**\n *  @internal\n *  Given an id, returns the versionId if it exists.\n *  e.g. `versions.summer-drop.foo` = `summer-drop`\n *  e.g. `drafts.foo` = `undefined`\n *  e.g. `foo` = `undefined`\n */\nexport function getVersionFromId(id: string): string | undefined {\n  if (!isVersionId(id)) return undefined\n  // eslint-disable-next-line unused-imports/no-unused-vars\n  const [_versionPrefix, versionId, ..._publishedId] = id.split(PATH_SEPARATOR)\n\n  return versionId\n}\n\n/** @internal */\nexport function getPublishedId(id: string): PublishedId {\n  if (isVersionId(id)) {\n    // make sure to only remove the versions prefix and the bundle name\n    return id.split(PATH_SEPARATOR).slice(2).join(PATH_SEPARATOR) as PublishedId as PublishedId\n  }\n\n  if (isDraftId(id)) {\n    return id.slice(DRAFTS_PREFIX.length) as PublishedId\n  }\n\n  return id as PublishedId\n}\n", "import * as studioPath from './studioPath'\nimport type {\n  ContentSourceMapParsedPath,\n  ContentSourceMapParsedPathKeyedSegment,\n  ContentSourceMapPaths,\n  Path,\n} from './types'\n\nconst ESCAPE: Record<string, string> = {\n  '\\f': '\\\\f',\n  '\\n': '\\\\n',\n  '\\r': '\\\\r',\n  '\\t': '\\\\t',\n  \"'\": \"\\\\'\",\n  '\\\\': '\\\\\\\\',\n}\n\nconst UNESCAPE: Record<string, string> = {\n  '\\\\f': '\\f',\n  '\\\\n': '\\n',\n  '\\\\r': '\\r',\n  '\\\\t': '\\t',\n  \"\\\\'\": \"'\",\n  '\\\\\\\\': '\\\\',\n}\n\n/**\n * @internal\n */\nexport function jsonPath(path: ContentSourceMapParsedPath): ContentSourceMapPaths[number] {\n  return `$${path\n    .map((segment) => {\n      if (typeof segment === 'string') {\n        const escapedKey = segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => {\n          return ESCAPE[match]\n        })\n        return `['${escapedKey}']`\n      }\n\n      if (typeof segment === 'number') {\n        return `[${segment}]`\n      }\n\n      if (segment._key !== '') {\n        const escapedKey = segment._key.replace(/['\\\\]/g, (match) => {\n          return ESCAPE[match]\n        })\n        return `[?(@._key=='${escapedKey}')]`\n      }\n\n      return `[${segment._index}]`\n    })\n    .join('')}`\n}\n\n/**\n * @internal\n */\nexport function parseJsonPath(path: ContentSourceMapPaths[number]): ContentSourceMapParsedPath {\n  const parsed: ContentSourceMapParsedPath = []\n\n  const parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g\n  let match: RegExpExecArray | null\n\n  while ((match = parseRe.exec(path)) !== null) {\n    if (match[1] !== undefined) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => {\n        return UNESCAPE[m]\n      })\n\n      parsed.push(key)\n      continue\n    }\n\n    if (match[2] !== undefined) {\n      parsed.push(parseInt(match[2], 10))\n      continue\n    }\n\n    if (match[3] !== undefined) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => {\n        return UNESCAPE[m]\n      })\n\n      parsed.push({\n        _key,\n        _index: -1,\n      })\n      continue\n    }\n  }\n\n  return parsed\n}\n\n/**\n * @internal\n */\nexport function jsonPathToStudioPath(path: ContentSourceMapParsedPath): Path {\n  return path.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (segment._key !== '') {\n      return {_key: segment._key}\n    }\n\n    if (segment._index !== -1) {\n      return segment._index\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n\n/**\n * @internal\n */\nexport function studioPathToJsonPath(path: Path | string): ContentSourceMapParsedPath {\n  const parsedPath = typeof path === 'string' ? studioPath.fromString(path) : path\n\n  return parsedPath.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (Array.isArray(segment)) {\n      throw new Error(`IndexTuple segments aren't supported:${JSON.stringify(segment)}`)\n    }\n\n    if (isContentSourceMapParsedPathKeyedSegment(segment)) {\n      return segment\n    }\n\n    if (segment._key) {\n      return {_key: segment._key, _index: -1}\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n\nfunction isContentSourceMapParsedPathKeyedSegment(\n  segment: studioPath.PathSegment | ContentSourceMapParsedPath[number],\n): segment is ContentSourceMapParsedPathKeyedSegment {\n  return typeof segment === 'object' && '_key' in segment && '_index' in segment\n}\n\n/**\n * @internal\n */\nexport function jsonPathToMappingPath(path: ContentSourceMapParsedPath): (string | number)[] {\n  return path.map((segment) => {\n    if (typeof segment === 'string') {\n      return segment\n    }\n\n    if (typeof segment === 'number') {\n      return segment\n    }\n\n    if (segment._index !== -1) {\n      return segment._index\n    }\n\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`)\n  })\n}\n", "import {jsonPath, jsonPathToMappingPath} from './jsonPath'\nimport type {ContentSourceMap, ContentSourceMapMapping, ContentSourceMapParsedPath} from './types'\n\n/**\n * @internal\n */\nexport function resolveMapping(\n  resultPath: ContentSourceMapParsedPath,\n  csm?: ContentSourceMap,\n):\n  | {\n      mapping: ContentSourceMapMapping\n      matchedPath: string\n      pathSuffix: string\n    }\n  | undefined {\n  if (!csm?.mappings) {\n    return undefined\n  }\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath))\n\n  if (csm.mappings[resultMappingPath] !== undefined) {\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: '',\n    }\n  }\n\n  const mappings = Object.entries(csm.mappings)\n    .filter(([key]) => resultMappingPath.startsWith(key))\n    .sort(([key1], [key2]) => key2.length - key1.length)\n\n  if (mappings.length == 0) {\n    return undefined\n  }\n\n  const [matchedPath, mapping] = mappings[0]\n  const pathSuffix = resultMappingPath.substring(matchedPath.length)\n  return {mapping, matchedPath, pathSuffix}\n}\n", "/** @internal */\nexport function isArray(value: unknown): value is Array<unknown> {\n  return value !== null && Array.isArray(value)\n}\n", "import {isRecord} from '../util/isRecord'\nimport {isArray} from './isArray'\nimport type {ContentSourceMapParsedPath, WalkMapFn} from './types'\n\n/**\n * generic way to walk a nested object or array and apply a mapping function to each value\n * @internal\n */\nexport function walkMap(\n  value: unknown,\n  mappingFn: WalkMapFn,\n  path: ContentSourceMapParsedPath = [],\n): unknown {\n  if (isArray(value)) {\n    return value.map((v, idx) => {\n      if (isRecord(v)) {\n        const _key = v['_key']\n        if (typeof _key === 'string') {\n          return walkMap(v, mappingFn, path.concat({_key, _index: idx}))\n        }\n      }\n\n      return walkMap(v, mappingFn, path.concat(idx))\n    })\n  }\n\n  if (isRecord(value)) {\n    // Handle Portable Text in a faster way\n    if (value._type === 'block' || value._type === 'span') {\n      const result = {...value}\n      if (value._type === 'block') {\n        result.children = walkMap(value.children, mappingFn, path.concat('children'))\n      } else if (value._type === 'span') {\n        result.text = walkMap(value.text, mappingFn, path.concat('text'))\n      }\n      return result\n    }\n\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))]),\n    )\n  }\n\n  return mappingFn(value, path)\n}\n", "import {getPublishedId, getVersionFromId, isPublishedId, isVersionId} from './draftUtils'\nimport {jsonPathToStudioPath} from './jsonPath'\nimport * as studioPath from './studioPath'\nimport type {CreateEditUrlOptions, EditIntentUrl, StudioBaseUrl} from './types'\n\n/** @internal */\nexport function createEditUrl(options: CreateEditUrlOptions): `${StudioBaseUrl}${EditIntentUrl}` {\n  const {\n    baseUrl,\n    workspace: _workspace = 'default',\n    tool: _tool = 'default',\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset,\n  } = options\n\n  if (!baseUrl) {\n    throw new Error('baseUrl is required')\n  }\n  if (!path) {\n    throw new Error('path is required')\n  }\n  if (!_id) {\n    throw new Error('id is required')\n  }\n  if (baseUrl !== '/' && baseUrl.endsWith('/')) {\n    throw new Error('baseUrl must not end with a slash')\n  }\n\n  const workspace = _workspace === 'default' ? undefined : _workspace\n  const tool = _tool === 'default' ? undefined : _tool\n  const id = getPublishedId(_id)\n  const stringifiedPath = Array.isArray(path)\n    ? studioPath.toString(jsonPathToStudioPath(path))\n    : path\n\n  // eslint-disable-next-line no-warning-comments\n  // @TODO Using searchParams as a temporary workaround until `@sanity/overlays` can decode state from the path reliably\n  const searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath,\n  })\n  if (workspace) {\n    searchParams.set('workspace', workspace)\n  }\n  if (tool) {\n    searchParams.set('tool', tool)\n  }\n  if (projectId) {\n    searchParams.set('projectId', projectId)\n  }\n  if (dataset) {\n    searchParams.set('dataset', dataset)\n  }\n  if (isPublishedId(_id)) {\n    searchParams.set('perspective', 'published')\n  } else if (isVersionId(_id)) {\n    const versionId = getVersionFromId(_id)!\n    searchParams.set('perspective', versionId)\n  }\n\n  const segments = [baseUrl === '/' ? '' : baseUrl]\n  if (workspace) {\n    segments.push(workspace)\n  }\n  const routerParams = [\n    'mode=presentation',\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`,\n  ]\n  if (tool) {\n    routerParams.push(`tool=${tool}`)\n  }\n  segments.push('intent', 'edit', `${routerParams.join(';')}?${searchParams}`)\n  return segments.join('/') as unknown as `${StudioBaseUrl}${EditIntentUrl}`\n}\n", "import {parseJsonPath} from './jsonPath'\nimport {resolveMapping} from './resolveMapping'\nimport type {\n  CreateEditUrlOptions,\n  ResolveEditInfoOptions,\n  StudioBaseRoute,\n  StudioBaseUrl,\n  StudioUrl,\n} from './types'\n\n/** @internal */\nexport function resolveEditInfo(options: ResolveEditInfoOptions): CreateEditUrlOptions | undefined {\n  const {resultSourceMap: csm, resultPath} = options\n  const {mapping, pathSuffix} = resolveMapping(resultPath, csm) || {}\n\n  if (!mapping) {\n    // console.warn('no mapping for path', { path: resultPath, sourceMap: csm })\n    return undefined\n  }\n\n  if (mapping.source.type === 'literal') {\n    return undefined\n  }\n\n  if (mapping.source.type === 'unknown') {\n    return undefined\n  }\n\n  const sourceDoc = csm.documents[mapping.source.document]\n  const sourcePath = csm.paths[mapping.source.path]\n\n  if (sourceDoc && sourcePath) {\n    const {baseUrl, workspace, tool} = resolveStudioBaseRoute(\n      typeof options.studioUrl === 'function' ? options.studioUrl(sourceDoc) : options.studioUrl,\n    )\n    if (!baseUrl) return undefined\n    const {_id, _type, _projectId, _dataset} = sourceDoc\n    return {\n      baseUrl,\n      workspace,\n      tool,\n      id: _id,\n      type: _type,\n      path: parseJsonPath(sourcePath + pathSuffix),\n      projectId: _projectId,\n      dataset: _dataset,\n    } satisfies CreateEditUrlOptions\n  }\n\n  return undefined\n}\n\n/** @internal */\nexport function resolveStudioBaseRoute(studioUrl: StudioUrl): StudioBaseRoute {\n  let baseUrl: StudioBaseUrl = typeof studioUrl === 'string' ? studioUrl : studioUrl.baseUrl\n  if (baseUrl !== '/') {\n    baseUrl = baseUrl.replace(/\\/$/, '')\n  }\n  if (typeof studioUrl === 'string') {\n    return {baseUrl}\n  }\n  return {...studioUrl, baseUrl}\n}\n"], "names": ["studioPath.fromString", "isRecord", "studioPath.toString"], "mappings": ";;AAYA,MAAM,aACJ,oGAEW,eAAe,4BACtB,eAAe;AAGd,SAAS,eAAe,SAAyC;AAC/D,SAAA,OAAO,WAAY,YAAa,OAAO,WAAY,YAAY,YAAY,KAAK,OAAO;AAChG;AAGO,SAAS,aAAa,SAA+C;AAC1E,SAAI,OAAO,WAAY,WACd,aAAa,KAAK,QAAQ,KAAK,CAAC,IAGlC,OAAO,WAAY,YAAY,UAAU;AAClD;AAGO,SAAS,aAAa,SAA6C;AACxE,MAAI,OAAO,WAAY,YAAY,aAAa,KAAK,OAAO;AACnD,WAAA;AAGT,MAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,QAAQ,WAAW;AACzC,WAAA;AAGH,QAAA,CAAC,MAAM,EAAE,IAAI;AACX,UAAA,OAAO,QAAS,YAAY,SAAS,QAAQ,OAAO,MAAO,YAAY,OAAO;AACxF;AAGgB,SAAA,IACd,KACA,MACA,YAC4B;AAC5B,QAAM,SAAS,OAAO,QAAS,WAAW,WAAW,IAAI,IAAI;AACzD,MAAA,CAAC,MAAM,QAAQ,MAAM;AACjB,UAAA,IAAI,MAAM,mCAAmC;AAGrD,MAAI,MAA2B;AAC/B,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAChC,UAAA,UAAU,OAAO,CAAC;AACpB,QAAA,eAAe,OAAO,GAAG;AACvB,UAAA,CAAC,MAAM,QAAQ,GAAG;AACb,eAAA;AAGT,YAAM,IAAI,OAAO;AAAA,IAAA;AAGf,QAAA,aAAa,OAAO,GAAG;AACrB,UAAA,CAAC,MAAM,QAAQ,GAAG;AACb,eAAA;AAGT,YAAM,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,QAAQ,IAAI;AAAA,IAAA;AAUrD,QAPI,OAAO,WAAY,aACrB,MACE,OAAO,OAAQ,YAAY,QAAQ,OAC7B,IAAgC,OAAO,IACzC,SAGJ,OAAO,MAAQ;AACV,aAAA;AAAA,EAAA;AAIJ,SAAA;AACT;AAGO,SAAS,SAAS,MAAoB;AACvC,MAAA,CAAC,MAAM,QAAQ,IAAI;AACf,UAAA,IAAI,MAAM,sBAAsB;AAGxC,SAAO,KAAK,OAAe,CAAC,QAAQ,SAAS,MAAM;AACjD,UAAM,cAAc,OAAO;AAC3B,QAAI,gBAAgB;AACX,aAAA,GAAG,MAAM,IAAI,OAAO;AAG7B,QAAI,gBAAgB;AAEX,aAAA,GAAG,MAAM,GADE,MAAM,IAAI,KAAK,GACL,GAAG,OAAO;AAGpC,QAAA,aAAa,OAAO,KAAK,QAAQ;AACnC,aAAO,GAAG,MAAM,WAAW,QAAQ,IAAI;AAGrC,QAAA,MAAM,QAAQ,OAAO,GAAG;AACpB,YAAA,CAAC,MAAM,EAAE,IAAI;AACnB,aAAO,GAAG,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,IAAA;AAGhC,UAAM,IAAI,MAAM,8BAA8B,KAAK,UAAU,OAAO,CAAC,IAAI;AAAA,KACxE,EAAE;AACP;AAGO,SAAS,WAAW,MAAoB;AAC7C,MAAI,OAAO,QAAS;AACZ,UAAA,IAAI,MAAM,sBAAsB;AAGlC,QAAA,WAAW,KAAK,MAAM,UAAU;AACtC,MAAI,CAAC;AACG,UAAA,IAAI,MAAM,qBAAqB;AAGhC,SAAA,SAAS,IAAI,gBAAgB;AACtC;AAEA,SAAS,iBAAiB,SAA8B;AACtD,SAAI,eAAe,OAAO,IACjB,kBAAkB,OAAO,IAG9B,aAAa,OAAO,IACf,gBAAgB,OAAO,IAG5B,aAAa,OAAO,IACf,uBAAuB,OAAO,IAGhC;AACT;AAEA,SAAS,kBAAkB,SAA8B;AACvD,SAAO,OAAO,QAAQ,QAAQ,UAAU,EAAE,CAAC;AAC7C;AAEA,SAAS,gBAAgB,SAA+B;AAEtD,SAAO,EAAC,MADS,QAAQ,MAAM,YAAY,EACnB,CAAC,EAAC;AAC5B;AAEA,SAAS,uBAAuB,SAA6B;AAC3D,QAAM,CAAC,MAAM,EAAE,IAAI,QAAQ,MAAM,GAAG,EAAE,IAAI,CAAC,QAAS,QAAQ,KAAK,MAAM,OAAO,GAAG,CAAE;AAC5E,SAAA,CAAC,MAAM,EAAE;AAClB;;;;;;;;;;;ACzJa,MAAA,gBAAgB,UAGhB,iBAAiB,YAExB,iBAAiB,KACjB,gBAAgB,GAAG,aAAa,GAAG,cAAc,IACjD,iBAAiB,GAAG,cAAc,GAAG,cAAc;AAGlD,SAAS,UAAU,IAA2B;AAC5C,SAAA,GAAG,WAAW,aAAa;AACpC;AAGO,SAAS,YAAY,IAAqB;AACxC,SAAA,GAAG,WAAW,cAAc;AACrC;AAGO,SAAS,cAAc,IAA+B;AAC3D,SAAO,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,EAAE;AAC1C;AAGO,SAAS,WAAW,IAAqB;AAC1C,MAAA,YAAY,EAAE,GAAG;AACb,UAAA,cAAc,eAAe,EAAE;AACrC,WAAQ,gBAAgB;AAAA,EAAA;AAG1B,SAAO,UAAU,EAAE,IAAI,KAAO,gBAAgB;AAChD;AAGgB,SAAA,aAAa,IAAY,SAAyB;AAC5D,MAAA,YAAY,YAAY,YAAY;AAChC,UAAA,IAAI,MAAM,4CAA4C;AAGvD,SAAA,GAAG,cAAc,GAAG,OAAO,GAAG,cAAc,GAAG,eAAe,EAAE,CAAC;AAC1E;AASO,SAAS,iBAAiB,IAAgC;AAC3D,MAAA,CAAC,YAAY,EAAE,EAAG;AAEhB,QAAA,CAAC,gBAAgB,WAAW,GAAG,YAAY,IAAI,GAAG,MAAM,cAAc;AAErE,SAAA;AACT;AAGO,SAAS,eAAe,IAAyB;AAClD,SAAA,YAAY,EAAE,IAET,GAAG,MAAM,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,cAAc,IAG1D,UAAU,EAAE,IACP,GAAG,MAAM,cAAc,MAAM,IAG/B;AACT;ACxEA,MAAM,SAAiC;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AACR,GAEM,WAAmC;AAAA,EACvC,OAAO;AAAA,EACP,OAAO;AAAA;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AACV;AAKO,SAAS,SAAS,MAAiE;AACjF,SAAA,IAAI,KACR,IAAI,CAAC,YACA,OAAO,WAAY,WAId,KAHY,QAAQ,QAAQ,kBAAkB,CAAC,UAC7C,OAAO,KAAK,CACpB,CACqB,OAGpB,OAAO,WAAY,WACd,IAAI,OAAO,MAGhB,QAAQ,SAAS,KAIZ,eAHY,QAAQ,KAAK,QAAQ,UAAU,CAAC,UAC1C,OAAO,KAAK,CACpB,CAC+B,QAG3B,IAAI,QAAQ,MAAM,GAC1B,EACA,KAAK,EAAE,CAAC;AACb;AAKO,SAAS,cAAc,MAAiE;AACvF,QAAA,SAAqC,IAErC,UAAU;AACZ,MAAA;AAEJ,UAAQ,QAAQ,QAAQ,KAAK,IAAI,OAAO,QAAM;AACxC,QAAA,MAAM,CAAC,MAAM,QAAW;AACpB,YAAA,MAAM,MAAM,CAAC,EAAE,QAAQ,qBAAqB,CAAC,MAC1C,SAAS,CAAC,CAClB;AAED,aAAO,KAAK,GAAG;AACf;AAAA,IAAA;AAGE,QAAA,MAAM,CAAC,MAAM,QAAW;AAC1B,aAAO,KAAK,SAAS,MAAM,CAAC,GAAG,EAAE,CAAC;AAClC;AAAA,IAAA;AAGE,QAAA,MAAM,CAAC,MAAM,QAAW;AACpB,YAAA,OAAO,MAAM,CAAC,EAAE,QAAQ,YAAY,CAAC,MAClC,SAAS,CAAC,CAClB;AAED,aAAO,KAAK;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,MAAA,CACT;AACD;AAAA,IAAA;AAAA,EACF;AAGK,SAAA;AACT;AAKO,SAAS,qBAAqB,MAAwC;AACpE,SAAA,KAAK,IAAI,CAAC,YAAY;AAK3B,QAJI,OAAO,WAAY,YAInB,OAAO,WAAY;AACd,aAAA;AAGT,QAAI,QAAQ,SAAS;AACZ,aAAA,EAAC,MAAM,QAAQ,KAAI;AAG5B,QAAI,QAAQ,WAAW;AACrB,aAAO,QAAQ;AAGjB,UAAM,IAAI,MAAM,mBAAmB,KAAK,UAAU,OAAO,CAAC,EAAE;AAAA,EAAA,CAC7D;AACH;AAKO,SAAS,qBAAqB,MAAiD;AACjE,UAAA,OAAO,QAAS,WAAWA,WAAsB,IAAI,IAAI,MAE1D,IAAI,CAAC,YAAY;AAKjC,QAJI,OAAO,WAAY,YAInB,OAAO,WAAY;AACd,aAAA;AAGL,QAAA,MAAM,QAAQ,OAAO;AACvB,YAAM,IAAI,MAAM,wCAAwC,KAAK,UAAU,OAAO,CAAC,EAAE;AAGnF,QAAI,yCAAyC,OAAO;AAC3C,aAAA;AAGT,QAAI,QAAQ;AACV,aAAO,EAAC,MAAM,QAAQ,MAAM,QAAQ,GAAE;AAGxC,UAAM,IAAI,MAAM,mBAAmB,KAAK,UAAU,OAAO,CAAC,EAAE;AAAA,EAAA,CAC7D;AACH;AAEA,SAAS,yCACP,SACmD;AACnD,SAAO,OAAO,WAAY,YAAY,UAAU,WAAW,YAAY;AACzE;AAKO,SAAS,sBAAsB,MAAuD;AACpF,SAAA,KAAK,IAAI,CAAC,YAAY;AAK3B,QAJI,OAAO,WAAY,YAInB,OAAO,WAAY;AACd,aAAA;AAGT,QAAI,QAAQ,WAAW;AACrB,aAAO,QAAQ;AAGjB,UAAM,IAAI,MAAM,mBAAmB,KAAK,UAAU,OAAO,CAAC,EAAE;AAAA,EAAA,CAC7D;AACH;AC1KgB,SAAA,eACd,YACA,KAOY;AACZ,MAAI,CAAC,KAAK;AACR;AAEF,QAAM,oBAAoB,SAAS,sBAAsB,UAAU,CAAC;AAEhE,MAAA,IAAI,SAAS,iBAAiB,MAAM;AAC/B,WAAA;AAAA,MACL,SAAS,IAAI,SAAS,iBAAiB;AAAA,MACvC,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAGI,QAAA,WAAW,OAAO,QAAQ,IAAI,QAAQ,EACzC,OAAO,CAAC,CAAC,GAAG,MAAM,kBAAkB,WAAW,GAAG,CAAC,EACnD,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,MAAM,KAAK,SAAS,KAAK,MAAM;AAErD,MAAI,SAAS,UAAU;AACrB;AAGI,QAAA,CAAC,aAAa,OAAO,IAAI,SAAS,CAAC,GACnC,aAAa,kBAAkB,UAAU,YAAY,MAAM;AAC1D,SAAA,EAAC,SAAS,aAAa,WAAU;AAC1C;ACvCO,SAAS,QAAQ,OAAyC;AAC/D,SAAO,UAAU,QAAQ,MAAM,QAAQ,KAAK;AAC9C;ACKO,SAAS,QACd,OACA,WACA,OAAmC,CAAA,GAC1B;AACT,MAAI,QAAQ,KAAK;AACf,WAAO,MAAM,IAAI,CAAC,GAAG,QAAQ;AACvB,UAAAC,SAAAA,SAAS,CAAC,GAAG;AACf,cAAM,OAAO,EAAE;AACf,YAAI,OAAO,QAAS;AACX,iBAAA,QAAQ,GAAG,WAAW,KAAK,OAAO,EAAC,MAAM,QAAQ,IAAG,CAAC,CAAC;AAAA,MAAA;AAIjE,aAAO,QAAQ,GAAG,WAAW,KAAK,OAAO,GAAG,CAAC;AAAA,IAAA,CAC9C;AAGC,MAAAA,SAAAA,SAAS,KAAK,GAAG;AAEnB,QAAI,MAAM,UAAU,WAAW,MAAM,UAAU,QAAQ;AAC/C,YAAA,SAAS,EAAC,GAAG,MAAK;AACpB,aAAA,MAAM,UAAU,UAClB,OAAO,WAAW,QAAQ,MAAM,UAAU,WAAW,KAAK,OAAO,UAAU,CAAC,IACnE,MAAM,UAAU,WACzB,OAAO,OAAO,QAAQ,MAAM,MAAM,WAAW,KAAK,OAAO,MAAM,CAAC,IAE3D;AAAA,IAAA;AAGT,WAAO,OAAO;AAAA,MACZ,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,QAAQ,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,IAClF;AAAA,EAAA;AAGK,SAAA,UAAU,OAAO,IAAI;AAC9B;ACtCO,SAAS,cAAc,SAAmE;AACzF,QAAA;AAAA,IACJ;AAAA,IACA,WAAW,aAAa;AAAA,IACxB,MAAM,QAAQ;AAAA,IACd,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AAEJ,MAAI,CAAC;AACG,UAAA,IAAI,MAAM,qBAAqB;AAEvC,MAAI,CAAC;AACG,UAAA,IAAI,MAAM,kBAAkB;AAEpC,MAAI,CAAC;AACG,UAAA,IAAI,MAAM,gBAAgB;AAElC,MAAI,YAAY,OAAO,QAAQ,SAAS,GAAG;AACnC,UAAA,IAAI,MAAM,mCAAmC;AAGrD,QAAM,YAAY,eAAe,YAAY,SAAY,YACnD,OAAO,UAAU,YAAY,SAAY,OACzC,KAAK,eAAe,GAAG,GACvB,kBAAkB,MAAM,QAAQ,IAAI,IACtCC,SAAoB,qBAAqB,IAAI,CAAC,IAC9C,MAIE,eAAe,IAAI,gBAAgB;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EAAA,CACP;AACG,MAAA,aACF,aAAa,IAAI,aAAa,SAAS,GAErC,QACF,aAAa,IAAI,QAAQ,IAAI,GAE3B,aACF,aAAa,IAAI,aAAa,SAAS,GAErC,WACF,aAAa,IAAI,WAAW,OAAO,GAEjC,cAAc,GAAG;AACN,iBAAA,IAAI,eAAe,WAAW;AAAA,WAClC,YAAY,GAAG,GAAG;AACrB,UAAA,YAAY,iBAAiB,GAAG;AACzB,iBAAA,IAAI,eAAe,SAAS;AAAA,EAAA;AAG3C,QAAM,WAAW,CAAC,YAAY,MAAM,KAAK,OAAO;AAC5C,eACF,SAAS,KAAK,SAAS;AAEzB,QAAM,eAAe;AAAA,IACnB;AAAA,IACA,MAAM,EAAE;AAAA,IACR,QAAQ,IAAI;AAAA,IACZ,QAAQ,mBAAmB,eAAe,CAAC;AAAA,EAC7C;AACI,SAAA,QACF,aAAa,KAAK,QAAQ,IAAI,EAAE,GAElC,SAAS,KAAK,UAAU,QAAQ,GAAG,aAAa,KAAK,GAAG,CAAC,IAAI,YAAY,EAAE,GACpE,SAAS,KAAK,GAAG;AAC1B;ACrEO,SAAS,gBAAgB,SAAmE;AACjG,QAAM,EAAC,iBAAiB,KAAK,WAAA,IAAc,SACrC,EAAC,SAAS,WAAc,IAAA,eAAe,YAAY,GAAG,KAAK,CAAC;AAE9D,MAAA,CAAC,WAKD,QAAQ,OAAO,SAAS,aAIxB,QAAQ,OAAO,SAAS;AAC1B;AAGF,QAAM,YAAY,IAAI,UAAU,QAAQ,OAAO,QAAQ,GACjD,aAAa,IAAI,MAAM,QAAQ,OAAO,IAAI;AAEhD,MAAI,aAAa,YAAY;AAC3B,UAAM,EAAC,SAAS,WAAW,KAAQ,IAAA;AAAA,MACjC,OAAO,QAAQ,aAAc,aAAa,QAAQ,UAAU,SAAS,IAAI,QAAQ;AAAA,IACnF;AACA,QAAI,CAAC,QAAS;AACd,UAAM,EAAC,KAAK,OAAO,YAAY,SAAY,IAAA;AACpC,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM,cAAc,aAAa,UAAU;AAAA,MAC3C,WAAW;AAAA,MACX,SAAS;AAAA,IACX;AAAA,EAAA;AAIJ;AAGO,SAAS,uBAAuB,WAAuC;AAC5E,MAAI,UAAyB,OAAO,aAAc,WAAW,YAAY,UAAU;AAInF,SAHI,YAAY,QACd,UAAU,QAAQ,QAAQ,OAAO,EAAE,IAEjC,OAAO,aAAc,WAChB,EAAC,YAEH,EAAC,GAAG,WAAW,QAAO;AAC/B;;;;;;;;;;;;;;;;;;;;;;;"}