{"name": "@architect/asap", "version": "7.0.10", "description": "Architect Static Asset Proxy (ASAP) - a helpful library for Lambda-based static asset delivery", "main": "src/asap.js", "scripts": {"lint": "eslint . --fix", "rc": "npm version prerelease --preid RC", "test:nolint": "npm run coverage", "test": "npm run lint && npm run coverage", "test:unit": "cross-env tape 'test/unit/**/*-test.js' | tap-arc", "coverage": "nyc --reporter=lcov --reporter=text npm run test:unit", "vendor": "node scripts/generate-mime-files.js"}, "repository": {"type": "git", "url": "git+https://github.com/architect/asap.git"}, "engines": {"node": ">=16"}, "license": "Apache-2.0", "bugs": "https://github.com/architect/asap/issues", "homepage": "https://github.com/architect/asap#readme", "files": ["src/*"], "dependencies": {"@aws-lite/client": "~0.21.1", "@aws-lite/s3": "^0.1.21"}, "devDependencies": {"@architect/eslint-config": "~3.0.0", "@architect/req-res-fixtures": "git+https://github.com/architect/req-res-fixtures.git", "cross-env": "7.0.3", "eslint": "~9.1.1", "mime-types": "~2.1.35", "mock-tmp": "~0.0.4", "nyc": "~15.1.0", "proxyquire": "~2.1.3", "tap-arc": "~1.2.2", "tape": "~5.7.5"}, "keywords": ["aws", "lambda", "amazon", "serverless", "cloud", "cloudfunctions", "apigateway", "dynamodb", "s3", "sns", "api gateway", "framework"], "eslintConfig": {"extends": "@architect/eslint-config"}}