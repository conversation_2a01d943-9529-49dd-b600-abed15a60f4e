#!/usr/bin/env node

/**
 * Framer Motion Usage Verifier - Bakasana Studio
 * 
 * This script verifies that all Framer Motion usage follows current best practices
 * and checks for any deprecated patterns
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

const log = (color, message) => console.log(`${colors[color]}${message}${colors.reset}`);

class FramerMotionVerifier {
  constructor() {
    this.deprecatedPatterns = [
      {
        pattern: /motion\(\s*[^.]/g,
        description: 'Deprecated motion() usage - use motion.create() instead',
        severity: 'error'
      },
      {
        pattern: /import.*\bmotive\b/g,
        description: 'Possible typo: "motive" instead of "motion"',
        severity: 'warning'
      }
    ];
    
    this.goodPatterns = [
      /motion\.\w+/g,          // motion.div, motion.section, etc.
      /motion\[\w+/g,          // motion[tagName]
      /motion\.create\(/g,     // motion.create()
    ];
    
    this.files = [];
    this.issues = [];
    this.stats = {
      filesScanned: 0,
      motionUsages: 0,
      deprecatedUsages: 0,
      goodUsages: 0
    };
  }

  // Find all relevant files recursively
  findFiles(dir = path.join(process.cwd(), 'src')) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.findFiles(fullPath);
      } else if (stat.isFile() && /\.(js|jsx|ts|tsx)$/.test(item)) {
        this.files.push(fullPath);
      }
    });
    
    this.stats.filesScanned = this.files.length;
  }

  // Check a single file for issues
  checkFile(filePath) {
    if (!fs.existsSync(filePath)) return;
    
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    // Check if file uses framer-motion at all
    if (!content.includes('framer-motion')) {
      return;
    }
    
    let fileHasMotion = false;
    let fileIssues = [];
    
    // Check for deprecated patterns
    this.deprecatedPatterns.forEach(({ pattern, description, severity }) => {
      const matches = [...content.matchAll(pattern)];
      
      matches.forEach(match => {
        fileHasMotion = true;
        this.stats.motionUsages++;
        
        if (severity === 'error') {
          this.stats.deprecatedUsages++;
        }
        
        const lines = content.substring(0, match.index).split('\n');
        const lineNumber = lines.length;
        const line = lines[lineNumber - 1];
        
        fileIssues.push({
          file: relativePath,
          line: lineNumber,
          column: match.index - lines.slice(0, -1).join('\n').length - 1,
          match: match[0],
          description,
          severity,
          context: line.trim()
        });
      });
    });
    
    // Check for good patterns
    this.goodPatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)];
      matches.forEach(match => {
        fileHasMotion = true;
        this.stats.motionUsages++;
        this.stats.goodUsages++;
      });
    });
    
    if (fileIssues.length > 0) {
      this.issues.push(...fileIssues);
    }
    
    return { hasMotion: fileHasMotion, issues: fileIssues };
  }

  // Generate a comprehensive report
  generateReport() {
    log('cyan', '\n' + '='.repeat(60));
    log('cyan', '🎬 FRAMER MOTION USAGE VERIFIER - BAKASANA STUDIO');
    log('cyan', '='.repeat(60));
    
    // Summary stats
    log('magenta', '\n📊 Scan Summary:');
    log('white', `Files scanned: ${this.stats.filesScanned}`);
    log('white', `Motion usages found: ${this.stats.motionUsages}`);
    log(this.stats.deprecatedUsages > 0 ? 'red' : 'green', 
        `Deprecated usages: ${this.stats.deprecatedUsages}`);
    log('green', `Good usages: ${this.stats.goodUsages}`);
    
    // Show issues
    if (this.issues.length > 0) {
      log('red', '\n❌ Issues Found:');
      log('white', '-'.repeat(40));
      
      this.issues.forEach(issue => {
        const icon = issue.severity === 'error' ? '❌' : '⚠️';
        log(issue.severity === 'error' ? 'red' : 'yellow', 
            `${icon} ${issue.file}:${issue.line}:${issue.column}`);
        log('white', `   ${issue.description}`);
        log('white', `   Match: "${issue.match}"`);
        log('white', `   Context: ${issue.context}`);
        log('white', '');
      });
    } else {
      log('green', '\n✅ No issues found!');
      log('green', 'All Framer Motion usage follows current best practices.');
    }
    
    // Show recommendations
    this.showRecommendations();
    
    // Show current version
    this.showVersionInfo();
    
    log('cyan', '\n' + '='.repeat(60));
    log(this.issues.length === 0 ? 'green' : 'yellow', 
        `Status: ${this.issues.length === 0 ? '✅ CLEAN' : '⚠️ NEEDS ATTENTION'}`);
    log('cyan', '='.repeat(60) + '\n');
  }

  showRecommendations() {
    log('magenta', '\n💡 Best Practices:');
    log('white', '• Use motion.div, motion.section, etc. for HTML elements');
    log('white', '• Use motion[tagName] for dynamic HTML elements');
    log('white', '• Use motion.create(Component) for custom React components');
    log('white', '• Avoid deprecated motion(Component) syntax');
    log('white', '• Keep Framer Motion updated to latest stable version');
  }

  showVersionInfo() {
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const version = packageJson.dependencies?.['framer-motion'] || 
                     packageJson.devDependencies?.['framer-motion'] || 
                     'Not found';
      
      log('magenta', '\n📦 Current Version:');
      log('white', `Framer Motion: ${version}`);
      
      if (version.includes('^12.') || version.includes('^11.')) {
        log('green', '✅ Version supports motion.create() API');
      } else if (version !== 'Not found') {
        log('yellow', '⚠️ Consider updating to latest version for best practices');
      }
    }
  }

  // Find common motion usage patterns
  analyzePatterns() {
    const patterns = {};
    
    this.files.forEach(filePath => {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Count different motion usage patterns
      const motionDivs = (content.match(/motion\.div/g) || []).length;
      const motionSections = (content.match(/motion\.section/g) || []).length;
      const motionButtons = (content.match(/motion\.button/g) || []).length;
      const motionSpans = (content.match(/motion\.span/g) || []).length;
      const motionCreate = (content.match(/motion\.create/g) || []).length;
      const dynamicMotion = (content.match(/motion\[/g) || []).length;
      
      if (motionDivs) patterns.div = (patterns.div || 0) + motionDivs;
      if (motionSections) patterns.section = (patterns.section || 0) + motionSections;
      if (motionButtons) patterns.button = (patterns.button || 0) + motionButtons;
      if (motionSpans) patterns.span = (patterns.span || 0) + motionSpans;
      if (motionCreate) patterns.create = (patterns.create || 0) + motionCreate;
      if (dynamicMotion) patterns.dynamic = (patterns.dynamic || 0) + dynamicMotion;
    });
    
    if (Object.keys(patterns).length > 0) {
      log('magenta', '\n📈 Usage Patterns:');
      Object.entries(patterns)
        .sort(([,a], [,b]) => b - a)
        .forEach(([pattern, count]) => {
          log('white', `motion.${pattern}: ${count} usages`);
        });
    }
  }

  run() {
    this.findFiles();
    
    log('cyan', '\n🔍 Scanning files for Framer Motion usage...');
    
    this.files.forEach(filePath => {
      this.checkFile(filePath);
    });
    
    this.analyzePatterns();
    this.generateReport();
    
    return this.issues.length === 0;
  }
}

// Run the verifier
if (require.main === module) {
  const verifier = new FramerMotionVerifier();
  const success = verifier.run();
  process.exit(success ? 0 : 1);
}

module.exports = FramerMotionVerifier;