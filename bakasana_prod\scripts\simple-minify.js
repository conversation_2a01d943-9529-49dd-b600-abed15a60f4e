#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const postcss = require('postcss');
const cssnano = require('cssnano');

console.log('🚀 Starting simple minification...');

// Create dist directory if it doesn't exist
const distDir = path.join(process.cwd(), 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Minify CSS files
async function minifyCSS() {
  console.log('🎨 Minifying CSS files...');
  
  const cssFiles = [
    'src/app/globals.css',
    'src/styles/main.css',
    'src/styles/hero.css',
    'src/styles/sections.css'
  ];
  
  let totalOriginalSize = 0;
  let totalMinifiedSize = 0;
  let combinedCSS = '';
  
  for (const file of cssFiles) {
    if (fs.existsSync(file)) {
      try {
        console.log(`  ⚡ Processing ${path.basename(file)}...`);
        
        const css = fs.readFileSync(file, 'utf8');
        totalOriginalSize += css.length;
        
        // Process with cssnano
        const result = await postcss([cssnano({ preset: 'default' })])
          .process(css, { from: file, to: undefined });
        
        const minifiedCSS = result.css;
        totalMinifiedSize += minifiedCSS.length;
        
        // Save individual minified file
        const outputFile = path.join(distDir, path.basename(file, '.css') + '.min.css');
        fs.writeFileSync(outputFile, minifiedCSS);
        
        // Add to combined CSS
        combinedCSS += `\n/* ${path.basename(file)} */\n${minifiedCSS}`;
        
        const savings = Math.round(((css.length - minifiedCSS.length) / css.length) * 100);
        console.log(`    ✅ ${path.basename(file)}: ${css.length} → ${minifiedCSS.length} bytes (${savings}% saved)`);
        
      } catch (error) {
        console.error(`    ❌ Error processing ${file}:`, error.message);
      }
    } else {
      console.log(`    ⚠️ File not found: ${file}`);
    }
  }
  
  // Save combined CSS
  const combinedFile = path.join(distDir, 'styles.min.css');
  fs.writeFileSync(combinedFile, combinedCSS);
  
  const totalSavings = totalOriginalSize > 0 ? Math.round(((totalOriginalSize - totalMinifiedSize) / totalOriginalSize) * 100) : 0;
  
  console.log(`\n📊 CSS Minification Results:`);
  console.log(`   Original size: ${Math.round(totalOriginalSize / 1024)} KB`);
  console.log(`   Minified size: ${Math.round(totalMinifiedSize / 1024)} KB`);
  console.log(`   Space saved: ${totalSavings}%`);
  console.log(`   Combined output: ${combinedFile}`);
}

// Simple CSS minification (fallback)
function simpleMinifyCSS(css) {
  return css
    // Remove comments
    .replace(/\/\*[\s\S]*?\*\//g, '')
    // Remove unnecessary whitespace
    .replace(/\s+/g, ' ')
    // Remove whitespace around special characters
    .replace(/\s*([{}:;,>+~])\s*/g, '$1')
    // Remove trailing semicolons
    .replace(/;}/g, '}')
    // Remove empty rules
    .replace(/[^{}]+{\s*}/g, '')
    // Optimize colors
    .replace(/#([a-f0-9])\1([a-f0-9])\2([a-f0-9])\3/gi, '#$1$2$3')
    // Optimize zero values
    .replace(/\b0px\b/g, '0')
    .replace(/\b0em\b/g, '0')
    .replace(/\b0rem\b/g, '0')
    .replace(/\b0%\b/g, '0')
    // Remove leading zeros
    .replace(/\b0+(\.\d+)/g, '$1')
    .trim();
}

// Fallback CSS minification
function fallbackMinifyCSS() {
  console.log('🎨 Using fallback CSS minification...');
  
  const cssFiles = [
    'src/app/globals.css',
    'src/styles/main.css',
    'src/styles/hero.css',
    'src/styles/sections.css'
  ];
  
  let totalOriginalSize = 0;
  let totalMinifiedSize = 0;
  let combinedCSS = '';
  
  cssFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`  ⚡ Processing ${path.basename(file)}...`);
      
      const css = fs.readFileSync(file, 'utf8');
      totalOriginalSize += css.length;
      
      const minifiedCSS = simpleMinifyCSS(css);
      totalMinifiedSize += minifiedCSS.length;
      
      // Save individual minified file
      const outputFile = path.join(distDir, path.basename(file, '.css') + '.min.css');
      fs.writeFileSync(outputFile, minifiedCSS);
      
      // Add to combined CSS
      combinedCSS += `\n/* ${path.basename(file)} */\n${minifiedCSS}`;
      
      const savings = Math.round(((css.length - minifiedCSS.length) / css.length) * 100);
      console.log(`    ✅ ${path.basename(file)}: ${css.length} → ${minifiedCSS.length} bytes (${savings}% saved)`);
    }
  });
  
  // Save combined CSS
  const combinedFile = path.join(distDir, 'styles.min.css');
  fs.writeFileSync(combinedFile, combinedCSS);
  
  const totalSavings = totalOriginalSize > 0 ? Math.round(((totalOriginalSize - totalMinifiedSize) / totalOriginalSize) * 100) : 0;
  
  console.log(`\n📊 CSS Minification Results:`);
  console.log(`   Original size: ${Math.round(totalOriginalSize / 1024)} KB`);
  console.log(`   Minified size: ${Math.round(totalMinifiedSize / 1024)} KB`);
  console.log(`   Space saved: ${totalSavings}%`);
  console.log(`   Combined output: ${combinedFile}`);
}

// Main execution
async function main() {
  try {
    await minifyCSS();
  } catch (error) {
    console.log('⚠️ PostCSS/cssnano failed, using fallback minification...');
    fallbackMinifyCSS();
  }
  
  console.log('\n✅ Minification completed!');
}

if (require.main === module) {
  main().catch(console.error);
}
