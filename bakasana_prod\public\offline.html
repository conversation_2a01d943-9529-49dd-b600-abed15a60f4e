<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - BAKASANA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #FDFCF8 0%, #F5F1E8 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2C2C2C;
            line-height: 1.6;
        }

        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 1rem;
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            opacity: 0.8;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            color: #B8956F;
            margin-bottom: 1rem;
        }

        .offline-message {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
        }

        .offline-features {
            background: #F8F6F0;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }

        .offline-features h3 {
            color: #B8956F;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .offline-features ul {
            list-style: none;
            padding: 0;
        }

        .offline-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #E5E5E5;
        }

        .offline-features li:last-child {
            border-bottom: none;
        }

        .offline-features li::before {
            content: "🧘‍♀️";
            margin-right: 0.5rem;
        }

        .retry-button {
            background: linear-gradient(135deg, #B8956F 0%, #C9A575 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 1rem 0.5rem;
        }

        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(184, 149, 111, 0.3);
        }

        .home-button {
            background: transparent;
            color: #B8956F;
            border: 2px solid #B8956F;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 1rem 0.5rem;
            text-decoration: none;
            display: inline-block;
        }

        .home-button:hover {
            background: #B8956F;
            color: white;
            transform: translateY(-2px);
        }

        .connection-status {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 600;
        }

        .connection-status.offline {
            background: #FEF2F2;
            color: #DC2626;
            border: 1px solid #FECACA;
        }

        .connection-status.online {
            background: #F0FDF4;
            color: #16A34A;
            border: 1px solid #BBF7D0;
        }

        .cached-content {
            margin-top: 2rem;
            text-align: left;
        }

        .cached-content h4 {
            color: #B8956F;
            margin-bottom: 1rem;
        }

        .cached-links {
            display: grid;
            gap: 0.5rem;
        }

        .cached-links a {
            color: #666;
            text-decoration: none;
            padding: 0.5rem;
            border-radius: 6px;
            transition: background 0.2s ease;
        }

        .cached-links a:hover {
            background: #F8F6F0;
            color: #B8956F;
        }

        @media (max-width: 768px) {
            .offline-container {
                margin: 0.5rem;
                padding: 1.5rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .retry-button, .home-button {
                display: block;
                width: 100%;
                margin: 0.5rem 0;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #B8956F;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">🧘‍♀️</div>
        <h1 class="offline-title">Jesteś offline</h1>
        <p class="offline-message">
            Nie martw się! Możesz nadal przeglądać niektóre treści BAKASANA, 
            które zostały zapisane na Twoim urządzeniu.
        </p>

        <div class="offline-features">
            <h3>Dostępne offline:</h3>
            <ul>
                <li>Informacje o retreatach</li>
                <li>Galeria zdjęć</li>
                <li>Blog o jodze</li>
                <li>Dane kontaktowe</li>
            </ul>
        </div>

        <div class="connection-status offline" id="connectionStatus">
            ❌ Brak połączenia z internetem
        </div>

        <div>
            <button class="retry-button" onclick="retryConnection()">
                Spróbuj ponownie
                <span class="loading" id="loadingSpinner" style="display: none;"></span>
            </button>
            <a href="/" class="home-button">Strona główna</a>
        </div>

        <div class="cached-content">
            <h4>Zapisane strony:</h4>
            <div class="cached-links">
                <a href="/">🏠 Strona główna</a>
                <a href="/retreaty">🌴 Retreaty</a>
                <a href="/o-mnie">👩‍🏫 O mnie</a>
                <a href="/galeria">📸 Galeria</a>
                <a href="/blog">📝 Blog</a>
                <a href="/kontakt">📞 Kontakt</a>
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status online';
                statusElement.innerHTML = '✅ Połączenie przywrócone';
                
                // Auto-redirect after 2 seconds
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusElement.className = 'connection-status offline';
                statusElement.innerHTML = '❌ Brak połączenia z internetem';
            }
        }

        // Retry connection
        function retryConnection() {
            const spinner = document.getElementById('loadingSpinner');
            spinner.style.display = 'inline-block';
            
            // Check connection after a short delay
            setTimeout(() => {
                spinner.style.display = 'none';
                
                if (navigator.onLine) {
                    window.location.reload();
                } else {
                    alert('Nadal brak połączenia. Sprawdź ustawienia sieci.');
                }
            }, 1500);
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Check for cached pages
        if ('caches' in window) {
            caches.keys().then(cacheNames => {
                console.log('Available caches:', cacheNames);
            });
        }

        // Register service worker if not already registered
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered from offline page');
                })
                .catch(error => {
                    console.log('SW registration failed from offline page');
                });
        }

        // Handle back button
        window.addEventListener('popstate', function(event) {
            if (navigator.onLine) {
                window.location.reload();
            }
        });
    </script>
</body>
</html>
