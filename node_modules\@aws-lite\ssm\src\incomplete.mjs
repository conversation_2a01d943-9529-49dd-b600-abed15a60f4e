const disabled = true
export default {
  AddTagsToResource:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_AddTagsToResource' },
  AssociateOpsItemRelatedItem:                       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_AssociateOpsItemRelatedItem' },
  CancelCommand:                                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CancelCommand' },
  CancelMaintenanceWindowExecution:                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CancelMaintenanceWindowExecution' },
  CreateActivation:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreateActivation' },
  CreateAssociation:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreateAssociation' },
  CreateAssociationBatch:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreateAssociationBatch' },
  CreateDocument:                                    { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreateDocument' },
  CreateMaintenanceWindow:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreateMaintenanceWindow' },
  CreateOpsItem:                                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreateOpsItem' },
  CreateOpsMetadata:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreateOpsMetadata' },
  CreatePatchBaseline:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreatePatchBaseline' },
  CreateResourceDataSync:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_CreateResourceDataSync' },
  DeleteActivation:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeleteActivation' },
  DeleteAssociation:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeleteAssociation' },
  DeleteDocument:                                    { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeleteDocument' },
  DeleteInventory:                                   { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeleteInventory' },
  DeleteMaintenanceWindow:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeleteMaintenanceWindow' },
  DeleteOpsMetadata:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeleteOpsMetadata' },
  DeletePatchBaseline:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeletePatchBaseline' },
  DeleteResourceDataSync:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeleteResourceDataSync' },
  DeleteResourcePolicy:                              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeleteResourcePolicy' },
  DeregisterManagedInstance:                         { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeregisterManagedInstance' },
  DeregisterPatchBaselineForPatchGroup:              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeregisterPatchBaselineForPatchGroup' },
  DeregisterTargetFromMaintenanceWindow:             { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeregisterTargetFromMaintenanceWindow' },
  DeregisterTaskFromMaintenanceWindow:               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DeregisterTaskFromMaintenanceWindow' },
  DescribeActivations:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeActivations' },
  DescribeAssociation:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeAssociation' },
  DescribeAssociationExecutions:                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeAssociationExecutions' },
  DescribeAssociationExecutionTargets:               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeAssociationExecutionTargets' },
  DescribeAutomationExecutions:                      { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeAutomationExecutions' },
  DescribeAutomationStepExecutions:                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeAutomationStepExecutions' },
  DescribeAvailablePatches:                          { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeAvailablePatches' },
  DescribeDocument:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeDocument' },
  DescribeDocumentPermission:                        { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeDocumentPermission' },
  DescribeEffectiveInstanceAssociations:             { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeEffectiveInstanceAssociations' },
  DescribeEffectivePatchesForPatchBaseline:          { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeEffectivePatchesForPatchBaseline' },
  DescribeInstanceAssociationsStatus:                { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeInstanceAssociationsStatus' },
  DescribeInstanceInformation:                       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeInstanceInformation' },
  DescribeInstancePatches:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeInstancePatches' },
  DescribeInstancePatchStates:                       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeInstancePatchStates' },
  DescribeInstancePatchStatesForPatchGroup:          { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeInstancePatchStatesForPatchGroup' },
  DescribeInventoryDeletions:                        { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeInventoryDeletions' },
  DescribeMaintenanceWindowExecutions:               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeMaintenanceWindowExecutions' },
  DescribeMaintenanceWindowExecutionTaskInvocations: { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeMaintenanceWindowExecutionTaskInvocations' },
  DescribeMaintenanceWindowExecutionTasks:           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeMaintenanceWindowExecutionTasks' },
  DescribeMaintenanceWindows:                        { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeMaintenanceWindows' },
  DescribeMaintenanceWindowSchedule:                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeMaintenanceWindowSchedule' },
  DescribeMaintenanceWindowsForTarget:               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeMaintenanceWindowsForTarget' },
  DescribeMaintenanceWindowTargets:                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeMaintenanceWindowTargets' },
  DescribeMaintenanceWindowTasks:                    { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeMaintenanceWindowTasks' },
  DescribeOpsItems:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeOpsItems' },
  DescribeParameters:                                { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeParameters' },
  DescribePatchBaselines:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribePatchBaselines' },
  DescribePatchGroups:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribePatchGroups' },
  DescribePatchGroupState:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribePatchGroupState' },
  DescribePatchProperties:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribePatchProperties' },
  DescribeSessions:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DescribeSessions' },
  DisassociateOpsItemRelatedItem:                    { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_DisassociateOpsItemRelatedItem' },
  GetAutomationExecution:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetAutomationExecution' },
  GetCalendarState:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetCalendarState' },
  GetCommandInvocation:                              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetCommandInvocation' },
  GetConnectionStatus:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetConnectionStatus' },
  GetDefaultPatchBaseline:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetDefaultPatchBaseline' },
  GetDeployablePatchSnapshotForInstance:             { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetDeployablePatchSnapshotForInstance' },
  GetDocument:                                       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetDocument' },
  GetInventory:                                      { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetInventory' },
  GetInventorySchema:                                { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetInventorySchema' },
  GetMaintenanceWindow:                              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetMaintenanceWindow' },
  GetMaintenanceWindowExecution:                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetMaintenanceWindowExecution' },
  GetMaintenanceWindowExecutionTask:                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetMaintenanceWindowExecutionTask' },
  GetMaintenanceWindowExecutionTaskInvocation:       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetMaintenanceWindowExecutionTaskInvocation' },
  GetMaintenanceWindowTask:                          { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetMaintenanceWindowTask' },
  GetOpsItem:                                        { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetOpsItem' },
  GetOpsMetadata:                                    { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetOpsMetadata' },
  GetOpsSummary:                                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetOpsSummary' },
  GetParameterHistory:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetParameterHistory' },
  GetPatchBaseline:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetPatchBaseline' },
  GetPatchBaselineForPatchGroup:                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetPatchBaselineForPatchGroup' },
  GetResourcePolicies:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetResourcePolicies' },
  GetServiceSetting:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetServiceSetting' },
  LabelParameterVersion:                             { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_LabelParameterVersion' },
  ListAssociations:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListAssociations' },
  ListAssociationVersions:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListAssociationVersions' },
  ListCommandInvocations:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListCommandInvocations' },
  ListCommands:                                      { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListCommands' },
  ListComplianceItems:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListComplianceItems' },
  ListComplianceSummaries:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListComplianceSummaries' },
  ListDocumentMetadataHistory:                       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListDocumentMetadataHistory' },
  ListDocuments:                                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListDocuments' },
  ListDocumentVersions:                              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListDocumentVersions' },
  ListInventoryEntries:                              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListInventoryEntries' },
  ListOpsItemEvents:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListOpsItemEvents' },
  ListOpsItemRelatedItems:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListOpsItemRelatedItems' },
  ListOpsMetadata:                                   { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListOpsMetadata' },
  ListResourceComplianceSummaries:                   { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListResourceComplianceSummaries' },
  ListResourceDataSync:                              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListResourceDataSync' },
  ListTagsForResource:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ListTagsForResource' },
  ModifyDocumentPermission:                          { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ModifyDocumentPermission' },
  PutComplianceItems:                                { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_PutComplianceItems' },
  PutInventory:                                      { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_PutInventory' },
  PutResourcePolicy:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_PutResourcePolicy' },
  RegisterDefaultPatchBaseline:                      { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_RegisterDefaultPatchBaseline' },
  RegisterPatchBaselineForPatchGroup:                { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_RegisterPatchBaselineForPatchGroup' },
  RegisterTargetWithMaintenanceWindow:               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_RegisterTargetWithMaintenanceWindow' },
  RegisterTaskWithMaintenanceWindow:                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_RegisterTaskWithMaintenanceWindow' },
  RemoveTagsFromResource:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_RemoveTagsFromResource' },
  ResetServiceSetting:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ResetServiceSetting' },
  ResumeSession:                                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_ResumeSession' },
  SendAutomationSignal:                              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_SendAutomationSignal' },
  SendCommand:                                       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_SendCommand' },
  StartAssociationsOnce:                             { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_StartAssociationsOnce' },
  StartAutomationExecution:                          { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_StartAutomationExecution' },
  StartChangeRequestExecution:                       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_StartChangeRequestExecution' },
  StartSession:                                      { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_StartSession' },
  StopAutomationExecution:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_StopAutomationExecution' },
  TerminateSession:                                  { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_TerminateSession' },
  UnlabelParameterVersion:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UnlabelParameterVersion' },
  UpdateAssociation:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateAssociation' },
  UpdateAssociationStatus:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateAssociationStatus' },
  UpdateDocument:                                    { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateDocument' },
  UpdateDocumentDefaultVersion:                      { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateDocumentDefaultVersion' },
  UpdateDocumentMetadata:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateDocumentMetadata' },
  UpdateMaintenanceWindow:                           { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateMaintenanceWindow' },
  UpdateMaintenanceWindowTarget:                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateMaintenanceWindowTarget' },
  UpdateMaintenanceWindowTask:                       { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateMaintenanceWindowTask' },
  UpdateManagedInstanceRole:                         { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateManagedInstanceRole' },
  UpdateOpsItem:                                     { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateOpsItem' },
  UpdateOpsMetadata:                                 { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateOpsMetadata' },
  UpdatePatchBaseline:                               { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdatePatchBaseline' },
  UpdateResourceDataSync:                            { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateResourceDataSync' },
  UpdateServiceSetting:                              { disabled, awsDoc: 'https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateServiceSetting' },
}
