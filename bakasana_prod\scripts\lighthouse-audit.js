#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * BAKASANA Lighthouse Performance Audit
 * Automated Lighthouse testing for >95 scores across all metrics
 */

class LighthouseAuditor {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.reportsDir = path.join(this.projectRoot, 'lighthouse-reports');
    this.targetScores = {
      performance: 95,
      accessibility: 95,
      bestPractices: 95,
      seo: 95,
    };
  }

  // Ensure reports directory exists
  ensureReportsDir() {
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  // Check if Lighthouse CLI is installed
  checkLighthouseCLI() {
    try {
      execSync('lighthouse --version', { stdio: 'pipe' });
      return true;
    } catch (error) {
      console.log('📦 Installing Lighthouse CLI...');
      try {
        execSync('npm install -g lighthouse', { stdio: 'inherit' });
        return true;
      } catch (installError) {
        console.error(
          '❌ Failed to install Lighthouse CLI:',
          installError.message
        );
        return false;
      }
    }
  }

  // Run Lighthouse audit
  async runAudit(url, outputName) {
    console.log(`🔍 Running Lighthouse audit for: ${url}`);

    const outputPath = path.join(this.reportsDir, `${outputName}.json`);
    const htmlOutputPath = path.join(this.reportsDir, `${outputName}.html`);

    const lighthouseCommand = [
      'lighthouse',
      url,
      '--output=json,html',
      `--output-path=${outputPath.replace('.json', '')}`,
      '--chrome-flags="--headless --no-sandbox --disable-dev-shm-usage"',
      '--throttling-method=simulate',
      '--form-factor=desktop',
      '--screenEmulation.disabled=false',
      '--emulatedUserAgent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"',
    ].join(' ');

    try {
      execSync(lighthouseCommand, {
        stdio: 'pipe',
        timeout: 120000, // 2 minutes timeout
      });

      console.log(`✅ Audit completed: ${outputName}`);
      return this.parseResults(outputPath);
    } catch (error) {
      console.error(`❌ Audit failed for ${url}:`, error.message);
      return null;
    }
  }

  // Parse Lighthouse results
  parseResults(jsonPath) {
    try {
      const results = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

      return {
        url: results.finalUrl,
        timestamp: results.fetchTime,
        scores: {
          performance: Math.round(results.categories.performance.score * 100),
          accessibility: Math.round(
            results.categories.accessibility.score * 100
          ),
          bestPractices: Math.round(
            results.categories['best-practices'].score * 100
          ),
          seo: Math.round(results.categories.seo.score * 100),
        },
        metrics: {
          firstContentfulPaint:
            results.audits['first-contentful-paint'].numericValue,
          largestContentfulPaint:
            results.audits['largest-contentful-paint'].numericValue,
          cumulativeLayoutShift:
            results.audits['cumulative-layout-shift'].numericValue,
          totalBlockingTime: results.audits['total-blocking-time'].numericValue,
          speedIndex: results.audits['speed-index'].numericValue,
        },
        opportunities: results.audits['opportunities'] || [],
        diagnostics: results.audits['diagnostics'] || [],
      };
    } catch (error) {
      console.error('❌ Failed to parse results:', error.message);
      return null;
    }
  }

  // Analyze results and provide recommendations
  analyzeResults(results) {
    if (!results) return;

    console.log('\n📊 LIGHTHOUSE AUDIT RESULTS');
    console.log('============================');
    console.log(`🌐 URL: ${results.url}`);
    console.log(
      `⏰ Timestamp: ${new Date(results.timestamp).toLocaleString()}`
    );

    console.log('\n🎯 SCORES:');
    Object.entries(results.scores).forEach(([category, score]) => {
      const target = this.targetScores[category];
      const status = score >= target ? '✅' : '❌';
      const categoryName =
        category.charAt(0).toUpperCase() +
        category.slice(1).replace(/([A-Z])/g, ' $1');
      console.log(
        `   ${status} ${categoryName}: ${score}/100 (Target: ${target})`
      );
    });

    console.log('\n⚡ CORE WEB VITALS:');
    console.log(
      `   FCP: ${Math.round(results.metrics.firstContentfulPaint)}ms`
    );
    console.log(
      `   LCP: ${Math.round(results.metrics.largestContentfulPaint)}ms`
    );
    console.log(`   CLS: ${results.metrics.cumulativeLayoutShift.toFixed(3)}`);
    console.log(`   TBT: ${Math.round(results.metrics.totalBlockingTime)}ms`);
    console.log(`   SI: ${Math.round(results.metrics.speedIndex)}`);

    // Generate recommendations
    this.generateRecommendations(results);
  }

  // Generate performance recommendations
  generateRecommendations(results) {
    const recommendations = [];

    // Performance recommendations
    if (results.scores.performance < this.targetScores.performance) {
      if (results.metrics.largestContentfulPaint > 2500) {
        recommendations.push({
          category: 'Performance',
          issue: 'Slow Largest Contentful Paint (LCP)',
          solution:
            'Optimize images, preload critical resources, improve server response time',
          priority: 'HIGH',
        });
      }

      if (results.metrics.cumulativeLayoutShift > 0.1) {
        recommendations.push({
          category: 'Performance',
          issue: 'High Cumulative Layout Shift (CLS)',
          solution:
            'Add size attributes to images, reserve space for dynamic content',
          priority: 'HIGH',
        });
      }

      if (results.metrics.totalBlockingTime > 300) {
        recommendations.push({
          category: 'Performance',
          issue: 'High Total Blocking Time (TBT)',
          solution:
            'Reduce JavaScript execution time, code splitting, defer non-critical JS',
          priority: 'MEDIUM',
        });
      }
    }

    // Accessibility recommendations
    if (results.scores.accessibility < this.targetScores.accessibility) {
      recommendations.push({
        category: 'Accessibility',
        issue: 'Accessibility score below target',
        solution:
          'Add alt text to images, improve color contrast, add ARIA labels',
        priority: 'HIGH',
      });
    }

    // SEO recommendations
    if (results.scores.seo < this.targetScores.seo) {
      recommendations.push({
        category: 'SEO',
        issue: 'SEO score below target',
        solution:
          'Add meta descriptions, improve heading structure, optimize for mobile',
        priority: 'MEDIUM',
      });
    }

    if (recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      recommendations.forEach((rec, index) => {
        console.log(`\n${index + 1}. [${rec.priority}] ${rec.category}`);
        console.log(`   Issue: ${rec.issue}`);
        console.log(`   Solution: ${rec.solution}`);
      });
    } else {
      console.log('\n🎉 All targets achieved! Great job!');
    }
  }

  // Generate performance optimization checklist
  generateChecklist(results) {
    const checklist = {
      performance: {
        '✅ Images optimized + lazy loaded': results.scores.performance >= 90,
        '✅ CSS/JS minified': results.metrics.totalBlockingTime < 300,
        '✅ Critical CSS inlined': results.metrics.firstContentfulPaint < 1800,
        '✅ Resource hints implemented': results.scores.performance >= 95,
        '✅ Bundle size optimized': results.scores.performance >= 95,
      },
      accessibility: {
        '✅ Alt text for images': results.scores.accessibility >= 95,
        '✅ Color contrast': results.scores.accessibility >= 95,
        '✅ Keyboard navigation': results.scores.accessibility >= 95,
        '✅ ARIA labels': results.scores.accessibility >= 95,
      },
      seo: {
        '✅ Meta tags optimized': results.scores.seo >= 95,
        '✅ Structured data': results.scores.seo >= 95,
        '✅ Mobile friendly': results.scores.seo >= 95,
        '✅ Page speed': results.scores.performance >= 95,
      },
    };

    console.log('\n📋 OPTIMIZATION CHECKLIST:');
    Object.entries(checklist).forEach(([category, items]) => {
      console.log(`\n${category.toUpperCase()}:`);
      Object.entries(items).forEach(([item, completed]) => {
        const status = completed ? '✅' : '❌';
        console.log(`   ${status} ${item.replace('✅ ', '')}`);
      });
    });

    return checklist;
  }

  // Run comprehensive audit
  async runComprehensiveAudit() {
    console.log('🚀 BAKASANA Lighthouse Performance Audit Started\n');

    this.ensureReportsDir();

    if (!this.checkLighthouseCLI()) {
      console.error('❌ Cannot proceed without Lighthouse CLI');
      return;
    }

    // Test URLs
    const testUrls = [
      { url: 'http://localhost:3000', name: 'homepage' },
      { url: 'http://localhost:3000/retreaty', name: 'retreaty' },
      { url: 'http://localhost:3000/program', name: 'program' },
      { url: 'http://localhost:3000/kontakt', name: 'kontakt' },
    ];

    const allResults = [];

    for (const { url, name } of testUrls) {
      const results = await this.runAudit(url, name);
      if (results) {
        allResults.push({ name, results });
        this.analyzeResults(results);
        console.log('\n' + '='.repeat(50) + '\n');
      }
    }

    // Generate summary report
    this.generateSummaryReport(allResults);
  }

  // Generate summary report
  generateSummaryReport(allResults) {
    console.log('📊 SUMMARY REPORT');
    console.log('=================');

    const summary = {
      totalPages: allResults.length,
      averageScores: {
        performance: 0,
        accessibility: 0,
        bestPractices: 0,
        seo: 0,
      },
      pagesAboveTarget: 0,
    };

    // Calculate averages
    allResults.forEach(({ results }) => {
      Object.keys(summary.averageScores).forEach(category => {
        summary.averageScores[category] += results.scores[category];
      });

      // Check if page meets all targets
      const meetsAllTargets = Object.entries(this.targetScores).every(
        ([category, target]) => results.scores[category] >= target
      );
      if (meetsAllTargets) summary.pagesAboveTarget++;
    });

    Object.keys(summary.averageScores).forEach(category => {
      summary.averageScores[category] = Math.round(
        summary.averageScores[category] / allResults.length
      );
    });

    console.log(`\n📈 Average Scores:`);
    Object.entries(summary.averageScores).forEach(([category, score]) => {
      const target = this.targetScores[category];
      const status = score >= target ? '✅' : '❌';
      console.log(`   ${status} ${category}: ${score}/100`);
    });

    console.log(
      `\n🎯 Pages meeting all targets: ${summary.pagesAboveTarget}/${summary.totalPages}`
    );

    // Save summary to file
    const summaryPath = path.join(this.reportsDir, 'summary.json');
    fs.writeFileSync(
      summaryPath,
      JSON.stringify({ summary, allResults }, null, 2)
    );
    console.log(`\n📄 Summary saved to: ${summaryPath}`);

    if (summary.pagesAboveTarget === summary.totalPages) {
      console.log(
        '\n🎉 CONGRATULATIONS! All pages meet Lighthouse >95 targets!'
      );
    } else {
      console.log(
        '\n🔧 Some pages need optimization. Check individual reports for details.'
      );
    }
  }
}

// Run the auditor
if (require.main === module) {
  const auditor = new LighthouseAuditor();
  auditor.runComprehensiveAudit().catch(console.error);
}

module.exports = LighthouseAuditor;
