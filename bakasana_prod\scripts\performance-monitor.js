#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * BAKASANA PERFORMANCE MONITOR
 * Comprehensive performance analysis and optimization tool
 */

class PerformanceMonitor {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.buildDir = path.join(this.projectRoot, '.next');
    this.publicDir = path.join(this.projectRoot, 'public');
    this.results = {
      timestamp: new Date().toISOString(),
      bundleAnalysis: {},
      imageOptimization: {},
      cssOptimization: {},
      recommendations: [],
    };
  }

  // Analyze bundle sizes
  analyzeBundleSize() {
    console.log('🔍 Analyzing bundle sizes...');

    try {
      const buildManifest = path.join(this.buildDir, 'build-manifest.json');
      if (fs.existsSync(buildManifest)) {
        const manifest = JSON.parse(fs.readFileSync(buildManifest, 'utf8'));

        // Calculate total JS size
        let totalJSSize = 0;
        Object.values(manifest.pages).forEach(files => {
          files.forEach(file => {
            if (file.endsWith('.js')) {
              const filePath = path.join(this.buildDir, 'static', file);
              if (fs.existsSync(filePath)) {
                totalJSSize += fs.statSync(filePath).size;
              }
            }
          });
        });

        this.results.bundleAnalysis = {
          totalJSSize: Math.round(totalJSSize / 1024),
          status: totalJSSize > 300000 ? 'NEEDS_OPTIMIZATION' : 'GOOD',
          recommendation:
            totalJSSize > 300000
              ? 'Consider code splitting and tree shaking'
              : 'Bundle size is optimal',
        };

        console.log(
          `📦 Total JS Bundle Size: ${Math.round(totalJSSize / 1024)} KB`
        );
      }
    } catch (error) {
      console.error('Error analyzing bundle:', error.message);
    }
  }

  // Analyze image optimization
  analyzeImages() {
    console.log('🖼️ Analyzing image optimization...');

    const imageDir = path.join(this.publicDir, 'images');
    const images = this.getAllImages(imageDir);

    let totalSize = 0;
    let unoptimizedCount = 0;
    let missingWebP = 0;

    images.forEach(imagePath => {
      const stats = fs.statSync(imagePath);
      totalSize += stats.size;

      const ext = path.extname(imagePath).toLowerCase();
      if (['.jpg', '.jpeg', '.png'].includes(ext)) {
        const webpPath = imagePath.replace(/\.(jpg|jpeg|png)$/i, '.webp');
        if (!fs.existsSync(webpPath)) {
          missingWebP++;
        }

        // Check if image is too large (>100KB)
        if (stats.size > 100000) {
          unoptimizedCount++;
        }
      }
    });

    this.results.imageOptimization = {
      totalImages: images.length,
      totalSize: Math.round(totalSize / 1024),
      unoptimizedCount,
      missingWebP,
      status:
        unoptimizedCount > 0 || missingWebP > 0 ? 'NEEDS_OPTIMIZATION' : 'GOOD',
    };

    console.log(
      `📸 Images: ${images.length} total, ${Math.round(totalSize / 1024)} KB`
    );
    console.log(
      `⚠️ Unoptimized: ${unoptimizedCount}, Missing WebP: ${missingWebP}`
    );
  }

  // Analyze CSS optimization
  analyzeCSS() {
    console.log('🎨 Analyzing CSS optimization...');

    const cssFiles = this.getAllCSS(this.projectRoot);
    let totalSize = 0;
    let hasUnusedCSS = false;

    cssFiles.forEach(cssPath => {
      const stats = fs.statSync(cssPath);
      totalSize += stats.size;

      // Simple check for potentially unused CSS
      const content = fs.readFileSync(cssPath, 'utf8');
      if (content.includes('@import') && content.length > 50000) {
        hasUnusedCSS = true;
      }
    });

    this.results.cssOptimization = {
      totalFiles: cssFiles.length,
      totalSize: Math.round(totalSize / 1024),
      hasUnusedCSS,
      status:
        totalSize > 100000 || hasUnusedCSS ? 'NEEDS_OPTIMIZATION' : 'GOOD',
    };

    console.log(
      `🎨 CSS: ${cssFiles.length} files, ${Math.round(totalSize / 1024)} KB`
    );
  }

  // Generate recommendations
  generateRecommendations() {
    console.log('💡 Generating recommendations...');

    const recommendations = [];

    // Bundle size recommendations
    if (this.results.bundleAnalysis.status === 'NEEDS_OPTIMIZATION') {
      recommendations.push({
        category: 'Bundle Size',
        priority: 'HIGH',
        issue: 'Large JavaScript bundle size',
        solution: 'Implement dynamic imports and code splitting',
        impact: 'Reduce First Load JS by 30-50%',
      });
    }

    // Image optimization recommendations
    if (this.results.imageOptimization.unoptimizedCount > 0) {
      recommendations.push({
        category: 'Images',
        priority: 'HIGH',
        issue: `${this.results.imageOptimization.unoptimizedCount} unoptimized images`,
        solution: 'Run image optimization script and implement lazy loading',
        impact: 'Improve LCP by 20-40%',
      });
    }

    if (this.results.imageOptimization.missingWebP > 0) {
      recommendations.push({
        category: 'Images',
        priority: 'MEDIUM',
        issue: `${this.results.imageOptimization.missingWebP} images missing WebP format`,
        solution: 'Generate WebP versions for all images',
        impact: 'Reduce image payload by 25-35%',
      });
    }

    // CSS optimization recommendations
    if (this.results.cssOptimization.status === 'NEEDS_OPTIMIZATION') {
      recommendations.push({
        category: 'CSS',
        priority: 'MEDIUM',
        issue: 'Large CSS bundle or unused styles',
        solution: 'Implement critical CSS inlining and purge unused styles',
        impact: 'Improve FCP by 15-25%',
      });
    }

    // General performance recommendations
    recommendations.push({
      category: 'Performance',
      priority: 'HIGH',
      issue: 'Missing critical resource hints',
      solution: 'Add preload/prefetch for critical resources',
      impact: 'Improve all Core Web Vitals',
    });

    this.results.recommendations = recommendations;
  }

  // Helper methods
  getAllImages(dir) {
    const images = [];
    if (!fs.existsSync(dir)) return images;

    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        images.push(...this.getAllImages(filePath));
      } else if (/\.(jpg|jpeg|png|webp|avif|svg)$/i.test(file)) {
        images.push(filePath);
      }
    });

    return images;
  }

  getAllCSS(dir) {
    const cssFiles = [];
    const searchDirs = [
      path.join(dir, 'src/app'),
      path.join(dir, 'src/styles'),
      path.join(dir, 'src/components'),
    ];

    searchDirs.forEach(searchDir => {
      if (fs.existsSync(searchDir)) {
        this.findCSSFiles(searchDir, cssFiles);
      }
    });

    return cssFiles;
  }

  findCSSFiles(dir, cssFiles) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        this.findCSSFiles(filePath, cssFiles);
      } else if (file.endsWith('.css')) {
        cssFiles.push(filePath);
      }
    });
  }

  // Generate performance report
  generateReport() {
    console.log('\n📊 PERFORMANCE ANALYSIS REPORT');
    console.log('================================');

    console.log('\n📦 BUNDLE ANALYSIS:');
    console.log(`Status: ${this.results.bundleAnalysis.status}`);
    console.log(`Total JS Size: ${this.results.bundleAnalysis.totalJSSize} KB`);

    console.log('\n🖼️ IMAGE OPTIMIZATION:');
    console.log(`Status: ${this.results.imageOptimization.status}`);
    console.log(`Total Images: ${this.results.imageOptimization.totalImages}`);
    console.log(
      `Unoptimized: ${this.results.imageOptimization.unoptimizedCount}`
    );
    console.log(`Missing WebP: ${this.results.imageOptimization.missingWebP}`);

    console.log('\n🎨 CSS OPTIMIZATION:');
    console.log(`Status: ${this.results.cssOptimization.status}`);
    console.log(`Total CSS Size: ${this.results.cssOptimization.totalSize} KB`);

    console.log('\n💡 RECOMMENDATIONS:');
    this.results.recommendations.forEach((rec, index) => {
      console.log(`\n${index + 1}. [${rec.priority}] ${rec.category}`);
      console.log(`   Issue: ${rec.issue}`);
      console.log(`   Solution: ${rec.solution}`);
      console.log(`   Impact: ${rec.impact}`);
    });

    // Save report to file
    const reportPath = path.join(this.projectRoot, 'performance-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Report saved to: ${reportPath}`);
  }

  // Run complete analysis
  async run() {
    console.log('🚀 Starting BAKASANA Performance Analysis...\n');

    this.analyzeBundleSize();
    this.analyzeImages();
    this.analyzeCSS();
    this.generateRecommendations();
    this.generateReport();

    console.log('\n✅ Performance analysis complete!');
  }
}

// Run the performance monitor
if (require.main === module) {
  const monitor = new PerformanceMonitor();
  monitor.run().catch(console.error);
}

module.exports = PerformanceMonitor;
