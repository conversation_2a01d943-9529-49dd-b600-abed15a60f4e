import crypto from 'crypto';

import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { botDetector, adaptiveRateLimiter, TimingProtection } from '@/lib/advancedSecurity';
import { securityLogger } from '@/lib/securityLogger';

// Redis client for production rate limiting
let redisClient = null;
let redisInitialized = false;

async function initRedis() {
  if (redisInitialized) return redisClient;
  redisInitialized = true;

  if (process.env.NODE_ENV === 'production' && process.env.REDIS_URL) {
    try {
      const { createClient } = await import('redis');
      redisClient = createClient({
        url: process.env.REDIS_URL,
        socket: {
          reconnectStrategy: (retries) => {
            if (retries > 3) {
              console.warn('Redis max retries reached, falling back to memory');
              return false;
            }
            return Math.min(retries * 100, 3000);
          }
        }
      });

      redisClient.on('error', (err) => {
        console.warn('Redis error, falling back to memory:', err.message);
        redisClient = null;
      });

      redisClient.on('connect', () => {
        console.log('✅ Redis connected for rate limiting');
      });

      await redisClient.connect();
      return redisClient;
    } catch (error) {
      console.warn('Redis initialization failed, using memory fallback:', error.message);
      redisClient = null;
    }
  }

  return redisClient;
}

export async function POST(request) {
  const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD;
  const JWT_SECRET = process.env.JWT_SECRET;

  // Validate required environment variables
  if (!ADMIN_PASSWORD || !JWT_SECRET) {
    throw new Error(
      'Missing required environment variables: ADMIN_PASSWORD and JWT_SECRET must be set'
    );
  }

  // Rate limiting configuration
  const loginAttempts = new Map(); // Fallback for memory-based rate limiting
  const MAX_ATTEMPTS = 5;
  const LOCKOUT_TIME = 15 * 60 * 1000; // 15 minut

  function getClientIP(request) {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');

    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }

    if (realIP) {
      return realIP;
    }

    return 'unknown';
  }

  async function isRateLimited(ip) {
    const now = Date.now();

    // Try Redis first
    const redis = await initRedis();
    if (redis && redis.isOpen) {
      try {
        const key = `login_attempts:${ip}`;
        const count = await redis.zCount(key, now - LOCKOUT_TIME, now);
        return count >= MAX_ATTEMPTS;
      } catch (error) {
        console.warn('Redis error in isRateLimited, using memory fallback:', error.message);
      }
    }

    // Memory fallback
    const attempts = loginAttempts.get(ip);
    if (!attempts) {
      return false;
    }

    // Sprawdź czy lockout wygasł
    if (now - attempts.lastAttempt > LOCKOUT_TIME) {
      loginAttempts.delete(ip);
      return false;
    }

    return attempts.count >= MAX_ATTEMPTS;
  }

  async function recordFailedAttempt(ip) {
    const now = Date.now();

    // Try Redis first
    const redis = await initRedis();
    if (redis && redis.isOpen) {
      try {
        const key = `login_attempts:${ip}`;
        await redis.zAdd(key, { score: now, value: now.toString() });
        await redis.zRemRangeByScore(key, 0, now - LOCKOUT_TIME);
        await redis.expire(key, Math.ceil(LOCKOUT_TIME / 1000));
        return;
      } catch (error) {
        console.warn('Redis error in recordFailedAttempt, using memory fallback:', error.message);
      }
    }

    // Memory fallback
    const attempts = loginAttempts.get(ip) || { count: 0, lastAttempt: 0 };
    attempts.count += 1;
    attempts.lastAttempt = now;
    loginAttempts.set(ip, attempts);
  }

  async function clearFailedAttempts(ip) {
    // Try Redis first
    const redis = await initRedis();
    if (redis && redis.isOpen) {
      try {
        await redis.del(`login_attempts:${ip}`);
        return;
      } catch (error) {
        console.warn('Redis error in clearFailedAttempts, using memory fallback:', error.message);
      }
    }

    // Memory fallback
    loginAttempts.delete(ip);
  }

  try {
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';

    // Zaawansowana analiza ryzyka
    const requestData = {
      path: '/api/admin/login',
      method: 'POST',
      headers: Object.fromEntries(request.headers.entries())
    };

    const riskAnalysis = botDetector.analyzeBehavior(clientIP, userAgent, requestData);

    // Sprawdź adaptacyjny rate limiting
    const rateLimitResult = adaptiveRateLimiter.checkLimit(clientIP, '/api/admin/login', riskAnalysis.riskScore);

    if (!rateLimitResult.allowed) {
      securityLogger.logSecurityEvent('ADMIN_LOGIN_RATE_LIMITED', {
        clientIP,
        userAgent,
        riskScore: riskAnalysis.riskScore,
        violations: rateLimitResult.violations,
        path: '/api/admin/login',
        message: `Rate limit exceeded with ${rateLimitResult.violations} violations`
      }, 'high', clientIP);

      return NextResponse.json(
        {
          success: false,
          error: 'Zbyt wiele nieudanych prób logowania. Spróbuj ponownie za 15 minut.',
          lockoutTime: LOCKOUT_TIME / 1000 / 60,
          riskScore: riskAnalysis.riskScore > 50 ? 'HIGH' : 'MEDIUM'
        },
        { status: 429 }
      );
    }

    // Sprawdź tradycyjny rate limiting
    if (await isRateLimited(clientIP)) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Zbyt wiele nieudanych prób logowania. Spróbuj ponownie za 15 minut.',
          lockoutTime: LOCKOUT_TIME / 1000 / 60, // w minutach
        },
        { status: 429 }
      );
    }

    const { password, jsFingerprint, formValidation } = await request.json();

    // Dodatkowa walidacja dla wysokiego ryzyka
    if (riskAnalysis.riskScore > 70) {
      securityLogger.logSecurityEvent('HIGH_RISK_ADMIN_LOGIN_ATTEMPT', {
        clientIP,
        userAgent,
        riskScore: riskAnalysis.riskScore,
        path: '/api/admin/login',
        patterns: riskAnalysis.patterns,
        message: `High risk admin login attempt detected`
      }, 'high', clientIP);

      // Wymagaj dodatkowych danych dla wysokiego ryzyka
      if (!jsFingerprint || !formValidation) {
        await recordFailedAttempt(clientIP);

        securityLogger.logSecurityEvent('ADMIN_LOGIN_ADDITIONAL_VERIFICATION_REQUIRED', {
          clientIP,
          userAgent,
          riskScore: riskAnalysis.riskScore,
          path: '/api/admin/login',
          message: 'Additional verification required for high risk login'
        }, 'medium', clientIP);

        return NextResponse.json(
          { success: false, error: 'Dodatkowa weryfikacja wymagana' },
          { status: 403 }
        );
      }
    }

    // Walidacja hasła
    if (!password) {
      await recordFailedAttempt(clientIP);
      await TimingProtection.addRandomDelay(200, 500); // Dodaj opóźnienie
      return NextResponse.json(
        { success: false, error: 'Hasło jest wymagane' },
        { status: 400 }
      );
    }

    // Verify password using constant-time comparison
    const isValidPassword = await TimingProtection.verifyPasswordConstantTime(password, ADMIN_PASSWORD, bcrypt);

    if (!isValidPassword) {
      await recordFailedAttempt(clientIP);

      // Log nieudanej próby logowania
      console.warn(
        `Failed admin login attempt from IP: ${clientIP} at ${new Date().toISOString()}`
      );

      return NextResponse.json(
        { success: false, error: 'Nieprawidłowe hasło' },
        { status: 401 }
      );
    }

    // Wyczyść nieudane próby po udanym logowaniu
    await clearFailedAttempts(clientIP);

    // Generuj JWT token
    const token = jwt.sign(
      {
        role: 'admin',
        ip: clientIP,
        loginTime: Date.now(),
      },
      JWT_SECRET,
      {
        expiresIn: '24h',
        issuer: 'bakasana-travel-admin',
        audience: 'bakasana-travel-app',
      }
    );

    // Log udanego logowania

    // Create response with secure cookie
    const response = NextResponse.json({
      success: true,
      token,
      expiresIn: 24 * 60 * 60, // 24 godziny w sekundach
      message: 'Zalogowano pomyślnie',
    });

    // Set secure cookie for admin token
    const isProduction = process.env.NODE_ENV === 'production';
    response.cookies.set('admin-token', token, {
      httpOnly: true,
      secure: isProduction,
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('Admin login error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Błąd serwera podczas logowania',
        details:
          process.env.NODE_ENV === 'development' ? error.message : undefined,
      },
      { status: 500 }
    );
  }
}

// Secure password verification using bcrypt
async function verifyPassword(inputPassword, hashedPassword) {
  try {
    return await bcrypt.compare(inputPassword, hashedPassword);
  } catch (error) {
    console.error('Password verification error:', error);
    return false;
  }
}

// Hash password utility (for initial setup)
async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

// GET endpoint dla sprawdzenia statusu
export async function GET() {
  return NextResponse.json({
    message: 'Admin login endpoint is working',
    timestamp: new Date().toISOString(),
    rateLimit: {
      maxAttempts: MAX_ATTEMPTS,
      lockoutTimeMinutes: LOCKOUT_TIME / 1000 / 60,
    },
  });
}
