{"version": 3, "sources": ["../src/base.ts", "../src/env.ts", "../src/assets/mux-logo.ts"], "sourcesContent": ["import {\n  initialize,\n  teardown,\n  generatePlayerInitTime,\n  MuxMediaProps,\n  StreamTypes,\n  PlaybackTypes,\n  toMuxVideoURL,\n  Metadata,\n  MediaError,\n  getError,\n  CmcdTypes,\n  CmcdTypeValues,\n  addCuePoints,\n  getCuePoints,\n  getActiveCuePoint,\n  addChapters,\n  getActiveChapter,\n  getMetadata,\n  getStartDate,\n  getCurrentPdt,\n  getStreamType,\n  getTargetLiveWindow,\n  getLiveEdgeStart,\n  getSeekable,\n  getEnded,\n  getChapters,\n  toPlaybackIdFromSrc,\n  toPlaybackIdParts,\n  // isMuxVideoSrc,\n} from '@mux/playback-core';\nimport type {\n  PlaybackCore,\n  PlaybackEngine,\n  ExtensionMimeTypeMap,\n  ValueOf,\n  MaxResolutionValue,\n  MinResolutionValue,\n  RenditionOrderValue,\n  Chapter,\n  CuePoint,\n  Tokens,\n} from '@mux/playback-core';\nimport { getPlayerVersion } from './env';\n// this must be imported after playback-core for the polyfill to be included\nimport { CustomVideoElement, Events } from 'custom-media-element';\nimport type { HlsConfig } from 'hls.js';\nimport { muxLogo } from './assets/mux-logo.js';\nimport type { IMuxVideoBaseElement } from './types.js';\n\nexport * from './types.js';\n\nexport const Attributes = {\n  BEACON_COLLECTION_DOMAIN: 'beacon-collection-domain',\n  CUSTOM_DOMAIN: 'custom-domain',\n  DEBUG: 'debug',\n  DISABLE_TRACKING: 'disable-tracking',\n  DISABLE_COOKIES: 'disable-cookies',\n  DRM_TOKEN: 'drm-token',\n  PLAYBACK_TOKEN: 'playback-token',\n  ENV_KEY: 'env-key',\n  MAX_RESOLUTION: 'max-resolution',\n  MIN_RESOLUTION: 'min-resolution',\n  RENDITION_ORDER: 'rendition-order',\n  PROGRAM_START_TIME: 'program-start-time',\n  PROGRAM_END_TIME: 'program-end-time',\n  ASSET_START_TIME: 'asset-start-time',\n  ASSET_END_TIME: 'asset-end-time',\n  METADATA_URL: 'metadata-url',\n  PLAYBACK_ID: 'playback-id',\n  PLAYER_SOFTWARE_NAME: 'player-software-name',\n  PLAYER_SOFTWARE_VERSION: 'player-software-version',\n  PLAYER_INIT_TIME: 'player-init-time',\n  PREFER_CMCD: 'prefer-cmcd',\n  PREFER_PLAYBACK: 'prefer-playback',\n  START_TIME: 'start-time',\n  STREAM_TYPE: 'stream-type',\n  TARGET_LIVE_WINDOW: 'target-live-window',\n  LIVE_EDGE_OFFSET: 'live-edge-offset',\n  TYPE: 'type',\n  LOGO: 'logo',\n} as const;\n\nconst AttributeNameValues = Object.values(Attributes);\n\nexport const playerSoftwareVersion = getPlayerVersion();\nexport const playerSoftwareName = 'mux-video';\n\nexport class MuxVideoBaseElement extends CustomVideoElement implements IMuxVideoBaseElement {\n  static get NAME() {\n    return playerSoftwareName;\n  }\n\n  static get VERSION() {\n    return playerSoftwareVersion;\n  }\n\n  static get observedAttributes() {\n    return [...AttributeNameValues, ...(CustomVideoElement.observedAttributes ?? [])];\n  }\n\n  #core?: PlaybackCore;\n  #loadRequested?: Promise<void> | null;\n  #defaultPlayerInitTime: number;\n  #metadata: Metadata = {};\n  #tokens: Tokens = {};\n  #_hlsConfig?: Partial<HlsConfig>;\n  #playerSoftwareVersion?: string;\n  #playerSoftwareName?: string;\n  #errorTranslator?: (errorEvent: any) => any;\n  #logo: string = '';\n\n  static getLogoHTML(logoValue: string | null) {\n    if (!logoValue || logoValue === 'false') return '';\n    return logoValue === 'default' ? muxLogo : `<img part=\"logo\" src=\"${logoValue}\" />`;\n  }\n\n  static getTemplateHTML(attrs: Record<string, string> = {}) {\n    return /* html */ `\n      ${CustomVideoElement.getTemplateHTML(attrs)}\n      <style>\n        :host {\n          position: relative;\n        }\n        slot[name=\"logo\"] {\n          display: flex;\n          justify-content: end;\n          position: absolute;\n          top: 1rem;\n          right: 1rem;\n          opacity: 0;\n          transition: opacity 0.25s ease-in-out;\n          z-index: 1;\n        }\n        slot[name=\"logo\"]:has([part=\"logo\"]) {\n          opacity: 1;\n        }\n        slot[name=\"logo\"] [part=\"logo\"] {\n          width: 5rem;\n          pointer-events: none;\n          user-select: none;\n        }\n      </style>\n      <slot name=\"logo\">\n        ${this.getLogoHTML(attrs[Attributes.LOGO] ?? '')}\n      </slot>\n    `;\n  }\n\n  constructor() {\n    super();\n    this.#defaultPlayerInitTime = generatePlayerInitTime();\n\n    this.nativeEl.addEventListener('muxmetadata', (_event: Event) => {\n      const fetchedMetadata = getMetadata(this.nativeEl);\n      const userMetadata = this.metadata ?? {};\n\n      // User metadata takes precedence over fetched metadata...\n      this.metadata = {\n        ...fetchedMetadata,\n        ...userMetadata,\n      };\n\n      // ...except for the free plan branding metadata\n      if ((fetchedMetadata as any)?.['com.mux.video.branding'] === 'mux-free-plan') {\n        this.#logo = 'default';\n        this.updateLogo();\n      }\n    });\n  }\n\n  get preferCmcd() {\n    return (this.getAttribute(Attributes.PREFER_CMCD) as ValueOf<CmcdTypes>) ?? undefined;\n  }\n\n  set preferCmcd(value: ValueOf<CmcdTypes> | undefined) {\n    if (value === this.preferCmcd) return;\n    if (!value) {\n      this.removeAttribute(Attributes.PREFER_CMCD);\n    } else if (CmcdTypeValues.includes(value)) {\n      this.setAttribute(Attributes.PREFER_CMCD, value);\n    } else {\n      console.warn(`Invalid value for preferCmcd. Must be one of ${CmcdTypeValues.join()}`);\n    }\n  }\n\n  get playerInitTime() {\n    if (!this.hasAttribute(Attributes.PLAYER_INIT_TIME)) return this.#defaultPlayerInitTime;\n    return +(this.getAttribute(Attributes.PLAYER_INIT_TIME) as string) as number;\n  }\n\n  set playerInitTime(val) {\n    // don't cause an infinite loop and avoid change event dispatching\n    if (val == this.playerInitTime) return;\n\n    if (val == null) {\n      this.removeAttribute(Attributes.PLAYER_INIT_TIME);\n    } else {\n      this.setAttribute(Attributes.PLAYER_INIT_TIME, `${+val}`);\n    }\n  }\n\n  get playerSoftwareName() {\n    return this.#playerSoftwareName ?? playerSoftwareName;\n  }\n\n  set playerSoftwareName(value: string | undefined) {\n    this.#playerSoftwareName = value;\n  }\n\n  get playerSoftwareVersion() {\n    return this.#playerSoftwareVersion ?? playerSoftwareVersion;\n  }\n\n  set playerSoftwareVersion(value: string | undefined) {\n    this.#playerSoftwareVersion = value;\n  }\n\n  // Keeping this named \"_hls\" since it's exposed for unadvertised \"advanced usage\" via getter that assumes specifically hls.js (CJP)\n  get _hls(): PlaybackEngine | undefined {\n    return this.#core?.engine;\n  }\n\n  get mux(): Readonly<HTMLVideoElement['mux']> | undefined {\n    return this.nativeEl?.mux;\n  }\n\n  get error() {\n    return getError(this.nativeEl) ?? null;\n  }\n\n  get errorTranslator(): ((errorEvent: any) => any) | undefined {\n    return this.#errorTranslator;\n  }\n\n  set errorTranslator(value: ((errorEvent: any) => any) | undefined) {\n    this.#errorTranslator = value;\n  }\n\n  get src() {\n    // Use the attribute value as the source of truth.\n    // No need to store it in two places.\n    // This avoids needing a to read the attribute initially and update the src.\n    return this.getAttribute('src') as string;\n  }\n\n  set src(val: string) {\n    // If being set by attributeChangedCallback,\n    // dont' cause an infinite loop\n    if (val === this.src) return;\n\n    if (val == null) {\n      this.removeAttribute('src');\n    } else {\n      this.setAttribute('src', val);\n    }\n  }\n\n  get type(): ValueOf<ExtensionMimeTypeMap> | undefined {\n    return (this.getAttribute(Attributes.TYPE) as ValueOf<ExtensionMimeTypeMap>) ?? undefined;\n  }\n\n  set type(val: ValueOf<ExtensionMimeTypeMap> | undefined) {\n    // dont' cause an infinite loop\n    if (val === this.type) return;\n\n    if (val) {\n      this.setAttribute(Attributes.TYPE, val);\n    } else {\n      this.removeAttribute(Attributes.TYPE);\n    }\n  }\n\n  get preload() {\n    const val = this.getAttribute('preload') as HTMLMediaElement['preload'];\n    if (val === '') return 'auto';\n    if (['none', 'metadata', 'auto'].includes(val)) return val;\n    return super.preload;\n  }\n\n  set preload(val) {\n    // don't cause an infinite loop\n    // check the attribute because an empty string maps to the `auto` prop\n    if (val == this.getAttribute('preload')) return;\n\n    if (['', 'none', 'metadata', 'auto'].includes(val)) {\n      this.setAttribute('preload', val);\n    } else {\n      this.removeAttribute('preload');\n    }\n  }\n\n  get debug() {\n    return this.getAttribute(Attributes.DEBUG) != null;\n  }\n\n  set debug(val) {\n    // dont' cause an infinite loop\n    if (val === this.debug) return;\n\n    if (val) {\n      this.setAttribute(Attributes.DEBUG, '');\n    } else {\n      this.removeAttribute(Attributes.DEBUG);\n    }\n  }\n\n  get disableTracking() {\n    return this.hasAttribute(Attributes.DISABLE_TRACKING);\n  }\n\n  set disableTracking(val) {\n    // dont' cause an infinite loop\n    if (val === this.disableTracking) return;\n\n    this.toggleAttribute(Attributes.DISABLE_TRACKING, !!val);\n  }\n\n  get disableCookies() {\n    return this.hasAttribute(Attributes.DISABLE_COOKIES);\n  }\n\n  set disableCookies(val) {\n    // dont' cause an infinite loop\n    if (val === this.disableCookies) return;\n\n    if (val) {\n      this.setAttribute(Attributes.DISABLE_COOKIES, '');\n    } else {\n      this.removeAttribute(Attributes.DISABLE_COOKIES);\n    }\n  }\n\n  get startTime(): number | undefined {\n    const val = this.getAttribute(Attributes.START_TIME);\n    if (val == null) return undefined;\n    const num = +val;\n    return !Number.isNaN(num) ? num : undefined;\n  }\n\n  set startTime(val: number | undefined) {\n    // dont' cause an infinite loop\n    if (val === this.startTime) return;\n\n    if (val == null) {\n      this.removeAttribute(Attributes.START_TIME);\n    } else {\n      this.setAttribute(Attributes.START_TIME, `${val}`);\n    }\n  }\n\n  // NOTE: playbackId may contain additional query params (e.g. token= for playback token) (CJP)\n  get playbackId(): string | undefined {\n    if (this.hasAttribute(Attributes.PLAYBACK_ID)) {\n      return this.getAttribute(Attributes.PLAYBACK_ID) as string;\n    }\n\n    return toPlaybackIdFromSrc(this.src) ?? undefined;\n  }\n\n  set playbackId(val: string | undefined) {\n    // dont' cause an infinite loop\n    if (val === this.playbackId) return;\n\n    if (val) {\n      this.setAttribute(Attributes.PLAYBACK_ID, val);\n    } else {\n      this.removeAttribute(Attributes.PLAYBACK_ID);\n    }\n  }\n\n  get maxResolution() {\n    return (this.getAttribute(Attributes.MAX_RESOLUTION) as MaxResolutionValue) ?? undefined;\n  }\n\n  set maxResolution(val: MaxResolutionValue | undefined) {\n    if (val === this.maxResolution) return;\n\n    if (val) {\n      this.setAttribute(Attributes.MAX_RESOLUTION, val);\n    } else {\n      this.removeAttribute(Attributes.MAX_RESOLUTION);\n    }\n  }\n\n  get minResolution() {\n    return (this.getAttribute(Attributes.MIN_RESOLUTION) as MinResolutionValue) ?? undefined;\n  }\n\n  set minResolution(val: MinResolutionValue | undefined) {\n    if (val === this.minResolution) return;\n\n    if (val) {\n      this.setAttribute(Attributes.MIN_RESOLUTION, val);\n    } else {\n      this.removeAttribute(Attributes.MIN_RESOLUTION);\n    }\n  }\n\n  get renditionOrder() {\n    return (this.getAttribute(Attributes.RENDITION_ORDER) as RenditionOrderValue) ?? undefined;\n  }\n\n  set renditionOrder(val: RenditionOrderValue | undefined) {\n    if (val === this.renditionOrder) return;\n\n    if (val) {\n      this.setAttribute(Attributes.RENDITION_ORDER, val);\n    } else {\n      this.removeAttribute(Attributes.RENDITION_ORDER);\n    }\n  }\n\n  get programStartTime() {\n    const val = this.getAttribute(Attributes.PROGRAM_START_TIME);\n    if (val == null) return undefined;\n    const num = +val;\n    return !Number.isNaN(num) ? num : undefined;\n  }\n\n  set programStartTime(val: number | undefined) {\n    if (val == undefined) {\n      this.removeAttribute(Attributes.PROGRAM_START_TIME);\n    } else {\n      this.setAttribute(Attributes.PROGRAM_START_TIME, `${val}`);\n    }\n  }\n\n  get programEndTime() {\n    const val = this.getAttribute(Attributes.PROGRAM_END_TIME);\n    if (val == null) return undefined;\n    const num = +val;\n    return !Number.isNaN(num) ? num : undefined;\n  }\n\n  set programEndTime(val: number | undefined) {\n    if (val == undefined) {\n      this.removeAttribute(Attributes.PROGRAM_END_TIME);\n    } else {\n      this.setAttribute(Attributes.PROGRAM_END_TIME, `${val}`);\n    }\n  }\n\n  get assetStartTime() {\n    const val = this.getAttribute(Attributes.ASSET_START_TIME);\n    if (val == null) return undefined;\n    const num = +val;\n    return !Number.isNaN(num) ? num : undefined;\n  }\n\n  set assetStartTime(val: number | undefined) {\n    if (val == undefined) {\n      this.removeAttribute(Attributes.ASSET_START_TIME);\n    } else {\n      this.setAttribute(Attributes.ASSET_START_TIME, `${val}`);\n    }\n  }\n\n  get assetEndTime() {\n    const val = this.getAttribute(Attributes.ASSET_END_TIME);\n    if (val == null) return undefined;\n    const num = +val;\n    return !Number.isNaN(num) ? num : undefined;\n  }\n\n  set assetEndTime(val: number | undefined) {\n    if (val == undefined) {\n      this.removeAttribute(Attributes.ASSET_END_TIME);\n    } else {\n      this.setAttribute(Attributes.ASSET_END_TIME, `${val}`);\n    }\n  }\n\n  get customDomain() {\n    return this.getAttribute(Attributes.CUSTOM_DOMAIN) ?? undefined;\n  }\n\n  set customDomain(val: string | undefined) {\n    // dont' cause an infinite loop\n    if (val === this.customDomain) return;\n\n    if (val) {\n      this.setAttribute(Attributes.CUSTOM_DOMAIN, val);\n    } else {\n      this.removeAttribute(Attributes.CUSTOM_DOMAIN);\n    }\n  }\n\n  get drmToken() {\n    return this.getAttribute(Attributes.DRM_TOKEN) ?? undefined;\n  }\n\n  set drmToken(val: string | undefined) {\n    // dont' cause an infinite loop\n    if (val === this.drmToken) return;\n\n    if (val) {\n      this.setAttribute(Attributes.DRM_TOKEN, val);\n    } else {\n      this.removeAttribute(Attributes.DRM_TOKEN);\n    }\n  }\n\n  /**\n   * Get the playback token for signing the src URL.\n   */\n  get playbackToken() {\n    if (this.hasAttribute(Attributes.PLAYBACK_TOKEN)) {\n      return this.getAttribute(Attributes.PLAYBACK_TOKEN) ?? undefined;\n    }\n    if (this.hasAttribute(Attributes.PLAYBACK_ID)) {\n      const [, queryParts] = toPlaybackIdParts(this.playbackId ?? '');\n      return new URLSearchParams(queryParts).get('token') ?? undefined;\n    }\n    if (this.src) {\n      return new URLSearchParams(this.src).get('token') ?? undefined;\n    }\n    return undefined;\n  }\n\n  /**\n   * Set the playback token for signing the src URL.\n   */\n  set playbackToken(val: string | undefined) {\n    if (val === this.playbackToken) return;\n\n    if (val) {\n      this.setAttribute(Attributes.PLAYBACK_TOKEN, val);\n    } else {\n      this.removeAttribute(Attributes.PLAYBACK_TOKEN);\n    }\n  }\n\n  get tokens() {\n    const playback = this.getAttribute(Attributes.PLAYBACK_TOKEN);\n    const drm = this.getAttribute(Attributes.DRM_TOKEN);\n    return {\n      ...this.#tokens,\n      ...(playback != null ? { playback } : {}),\n      ...(drm != null ? { drm } : {}),\n    };\n  }\n\n  set tokens(val) {\n    this.#tokens = val ?? {};\n  }\n\n  get ended() {\n    // This ensures that edge case media that doesn't properly end will\n    // still announce itself as \"ended\".\n    return getEnded(this.nativeEl, this._hls);\n  }\n\n  get envKey(): string | undefined {\n    return this.getAttribute(Attributes.ENV_KEY) ?? undefined;\n  }\n\n  set envKey(val: string | undefined) {\n    // dont' cause an infinite loop\n    if (val === this.envKey) return;\n\n    if (val) {\n      this.setAttribute(Attributes.ENV_KEY, val);\n    } else {\n      this.removeAttribute(Attributes.ENV_KEY);\n    }\n  }\n\n  get beaconCollectionDomain(): string | undefined {\n    return this.getAttribute(Attributes.BEACON_COLLECTION_DOMAIN) ?? undefined;\n  }\n\n  set beaconCollectionDomain(val: string | undefined) {\n    // don't cause an infinite loop\n    if (val === this.beaconCollectionDomain) return;\n\n    if (val) {\n      this.setAttribute(Attributes.BEACON_COLLECTION_DOMAIN, val);\n    } else {\n      this.removeAttribute(Attributes.BEACON_COLLECTION_DOMAIN);\n    }\n  }\n\n  get streamType(): ValueOf<StreamTypes> | undefined {\n    // Allow overriding inferred `streamType`\n    return (this.getAttribute(Attributes.STREAM_TYPE) as ValueOf<StreamTypes>) ?? getStreamType(this.nativeEl);\n  }\n\n  set streamType(val: ValueOf<StreamTypes> | undefined) {\n    // don't cause an infinite loop and avoid change event dispatching\n    if (val === this.streamType) return;\n\n    if (val) {\n      this.setAttribute(Attributes.STREAM_TYPE, val);\n    } else {\n      this.removeAttribute(Attributes.STREAM_TYPE);\n    }\n  }\n\n  get targetLiveWindow() {\n    // Allow overriding inferred `targetLiveWindow`\n    if (this.hasAttribute(Attributes.TARGET_LIVE_WINDOW)) {\n      return +(this.getAttribute(Attributes.TARGET_LIVE_WINDOW) as string) as number;\n    }\n    return getTargetLiveWindow(this.nativeEl);\n  }\n\n  set targetLiveWindow(val: number | undefined) {\n    // don't cause an infinite loop and avoid change event dispatching\n    if (val == this.targetLiveWindow) return;\n\n    if (val == null) {\n      this.removeAttribute(Attributes.TARGET_LIVE_WINDOW);\n    } else {\n      this.setAttribute(Attributes.TARGET_LIVE_WINDOW, `${+val}`);\n    }\n  }\n\n  get liveEdgeStart() {\n    if (this.hasAttribute(Attributes.LIVE_EDGE_OFFSET)) {\n      const { liveEdgeOffset } = this;\n      const seekableEnd = this.nativeEl.seekable.end(0) ?? 0;\n      const seekableStart = this.nativeEl.seekable.start(0) ?? 0;\n      return Math.max(seekableStart, seekableEnd - (liveEdgeOffset as number));\n    }\n    return getLiveEdgeStart(this.nativeEl);\n  }\n\n  get liveEdgeOffset() {\n    if (!this.hasAttribute(Attributes.LIVE_EDGE_OFFSET)) return undefined;\n    return +(this.getAttribute(Attributes.LIVE_EDGE_OFFSET) as string) as number;\n  }\n\n  set liveEdgeOffset(val: number | undefined) {\n    // don't cause an infinite loop and avoid change event dispatching\n    if (val == this.liveEdgeOffset) return;\n\n    if (val == null) {\n      this.removeAttribute(Attributes.LIVE_EDGE_OFFSET);\n    } else {\n      this.setAttribute(Attributes.LIVE_EDGE_OFFSET, `${+val}`);\n    }\n  }\n\n  get seekable() {\n    return getSeekable(this.nativeEl);\n  }\n\n  async addCuePoints<T = any>(cuePoints: CuePoint<T>[]) {\n    return addCuePoints(this.nativeEl, cuePoints);\n  }\n\n  get activeCuePoint() {\n    return getActiveCuePoint(this.nativeEl);\n  }\n\n  get cuePoints() {\n    return getCuePoints(this.nativeEl);\n  }\n\n  async addChapters(chapters: Chapter[]) {\n    return addChapters(this.nativeEl, chapters);\n  }\n\n  get activeChapter() {\n    return getActiveChapter(this.nativeEl);\n  }\n\n  get chapters() {\n    return getChapters(this.nativeEl);\n  }\n\n  getStartDate() {\n    return getStartDate(this.nativeEl, this._hls);\n  }\n\n  get currentPdt() {\n    return getCurrentPdt(this.nativeEl, this._hls);\n  }\n\n  get preferPlayback(): ValueOf<PlaybackTypes> | undefined {\n    const val = this.getAttribute(Attributes.PREFER_PLAYBACK);\n    if (val === PlaybackTypes.MSE || val === PlaybackTypes.NATIVE) return val;\n    return undefined;\n  }\n\n  set preferPlayback(val: ValueOf<PlaybackTypes> | undefined) {\n    if (val === this.preferPlayback) return;\n\n    if (val === PlaybackTypes.MSE || val === PlaybackTypes.NATIVE) {\n      this.setAttribute(Attributes.PREFER_PLAYBACK, val);\n    } else {\n      this.removeAttribute(Attributes.PREFER_PLAYBACK);\n    }\n  }\n\n  get metadata() {\n    const inferredMetadataAttrs: { [key: string]: string } = this.getAttributeNames()\n      .filter((attrName) => {\n        return attrName.startsWith('metadata-') && !([Attributes.METADATA_URL] as string[]).includes(attrName);\n      })\n      .reduce(\n        (currAttrs, attrName) => {\n          const value = this.getAttribute(attrName);\n          if (value != null) {\n            currAttrs[attrName.replace(/^metadata-/, '').replace(/-/g, '_') as string] = value;\n          }\n          return currAttrs;\n        },\n        {} as { [key: string]: string }\n      );\n\n    return {\n      ...inferredMetadataAttrs,\n      ...this.#metadata,\n    };\n  }\n\n  set metadata(val: Readonly<Metadata> | undefined) {\n    this.#metadata = val ?? {};\n    if (!!this.mux) {\n      this.mux.emit('hb', this.#metadata);\n    }\n  }\n\n  get _hlsConfig() {\n    return this.#_hlsConfig;\n  }\n\n  set _hlsConfig(val: Readonly<Partial<HlsConfig>> | undefined) {\n    this.#_hlsConfig = val;\n  }\n\n  get logo() {\n    return this.getAttribute(Attributes.LOGO) ?? this.#logo;\n  }\n\n  set logo(val) {\n    if (val) {\n      this.setAttribute(Attributes.LOGO, val);\n    } else {\n      this.removeAttribute(Attributes.LOGO);\n    }\n  }\n\n  async #requestLoad() {\n    if (this.#loadRequested) return;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.load();\n  }\n\n  load() {\n    this.#core = initialize(this as Partial<MuxMediaProps>, this.nativeEl, this.#core);\n  }\n\n  unload() {\n    teardown(this.nativeEl, this.#core, this as Partial<MuxMediaProps>);\n    this.#core = undefined;\n  }\n\n  attributeChangedCallback(attrName: string, oldValue: string | null, newValue: string | null) {\n    // Only forward the attributes to the native media element that are not handled.\n    const isNativeAttr = CustomVideoElement.observedAttributes.includes(attrName);\n    if (isNativeAttr && !['src', 'autoplay', 'preload'].includes(attrName)) {\n      super.attributeChangedCallback(attrName, oldValue, newValue);\n    }\n\n    switch (attrName) {\n      case Attributes.PLAYER_SOFTWARE_NAME:\n        this.playerSoftwareName = newValue ?? undefined;\n        break;\n      case Attributes.PLAYER_SOFTWARE_VERSION:\n        this.playerSoftwareVersion = newValue ?? undefined;\n        break;\n      case 'src': {\n        const hadSrc = !!oldValue;\n        const hasSrc = !!newValue;\n        if (!hadSrc && hasSrc) {\n          this.#requestLoad();\n        } else if (hadSrc && !hasSrc) {\n          this.unload();\n        } else if (hadSrc && hasSrc) {\n          this.unload();\n          this.#requestLoad();\n        }\n        break;\n      }\n      case 'autoplay':\n        if (newValue === oldValue) {\n          break;\n        }\n        /** In case newValue is an empty string or null, use this.autoplay which translates to booleans (WL) */\n        this.#core?.setAutoplay(this.autoplay);\n        break;\n      case 'preload':\n        if (newValue === oldValue) {\n          break;\n        }\n        this.#core?.setPreload(newValue as HTMLMediaElement['preload']);\n        break;\n      case Attributes.PLAYBACK_ID:\n        this.src = toMuxVideoURL(this) as string;\n        break;\n      case Attributes.DEBUG: {\n        const debug = this.debug;\n        if (!!this.mux) {\n          /** @TODO Link to docs for a more detailed discussion (CJP) */\n          console.info(\n            'Cannot toggle debug mode of mux data after initialization. Make sure you set all metadata to override before setting the src.'\n          );\n        }\n        if (!!this._hls) {\n          this._hls.config.debug = debug;\n        }\n        break;\n      }\n      case Attributes.METADATA_URL:\n        if (newValue) {\n          fetch(newValue)\n            .then((resp) => resp.json())\n            .then((json) => (this.metadata = json))\n            .catch(() => console.error(`Unable to load or parse metadata JSON from metadata-url ${newValue}!`));\n        }\n        break;\n      case Attributes.STREAM_TYPE:\n        // If the newValue is unset\n        if (newValue == null || newValue !== oldValue) {\n          this.dispatchEvent(new CustomEvent('streamtypechange', { composed: true, bubbles: true }));\n        }\n        break;\n      case Attributes.TARGET_LIVE_WINDOW:\n        if (newValue == null || newValue !== oldValue) {\n          this.dispatchEvent(\n            new CustomEvent('targetlivewindowchange', { composed: true, bubbles: true, detail: this.targetLiveWindow })\n          );\n        }\n        break;\n      case Attributes.LOGO:\n        if (newValue == null || newValue !== oldValue) {\n          this.updateLogo();\n        }\n        break;\n    }\n  }\n\n  updateLogo() {\n    if (!this.shadowRoot) return;\n\n    const slotLogo = this.shadowRoot.querySelector('slot[name=\"logo\"]');\n    if (!slotLogo) return;\n\n    const logoHTML = (this.constructor as typeof MuxVideoElement).getLogoHTML(this.#logo || this.logo);\n    slotLogo.innerHTML = logoHTML;\n  }\n\n  connectedCallback(): void {\n    super.connectedCallback?.();\n    if (this.nativeEl && this.src && !this.#core) {\n      this.#requestLoad();\n    }\n  }\n\n  disconnectedCallback(): void {\n    this.unload();\n  }\n\n  handleEvent(event: Event): void {\n    if (event.target === this.nativeEl) {\n      // Composed events are forwarded to parent shadow hosts (e.g. mux-player).\n      this.dispatchEvent(\n        new CustomEvent(event.type, {\n          composed: true,\n          detail: (event as CustomEvent).detail,\n        })\n      );\n    }\n  }\n}\n\nexport {\n  PlaybackEngine,\n  PlaybackEngine as Hls,\n  ExtensionMimeTypeMap as MimeTypes,\n  MediaError,\n  Events,\n  generatePlayerInitTime,\n};\n", "export const isMaybeBrowser = () => typeof window != 'undefined';\n// @ts-ignore\nexport const isMaybeServer = () => typeof global != 'undefined';\n\nconst getEnvPlayerVersion = () => {\n  try {\n    // @ts-ignore\n    return PLAYER_VERSION as string;\n  } catch {}\n  return 'UNKNOWN';\n};\n\nconst player_version: string = getEnvPlayerVersion();\n\nexport const getPlayerVersion = () => player_version;\n", "export const muxLogo = `\n<svg xmlns=\"http://www.w3.org/2000/svg\" xml:space=\"preserve\" part=\"logo\" style=\"fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2\" viewBox=\"0 0 1600 500\"><g fill=\"#fff\"><path d=\"M994.287 93.486c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31m0-93.486c-34.509 0-62.484 27.976-62.484 62.486v187.511c0 68.943-56.09 125.033-125.032 125.033s-125.03-56.09-125.03-125.033V62.486C681.741 27.976 653.765 0 619.256 0s-62.484 27.976-62.484 62.486v187.511C556.772 387.85 668.921 500 806.771 500c137.851 0 250.001-112.15 250.001-250.003V62.486c0-34.51-27.976-62.486-62.485-62.486M1537.51 468.511c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31m-275.883-218.509-143.33 143.329c-24.402 24.402-24.402 63.966 0 88.368 24.402 24.402 63.967 24.402 88.369 0l143.33-143.329 143.328 143.329c24.402 24.4 63.967 24.402 88.369 0 24.403-24.402 24.403-63.966.001-88.368l-143.33-143.329.001-.004 143.329-143.329c24.402-24.402 24.402-63.965 0-88.367s-63.967-24.402-88.369 0L1349.996 161.63 1206.667 18.302c-24.402-24.401-63.967-24.402-88.369 0s-24.402 63.965 0 88.367l143.329 143.329v.004ZM437.511 468.521c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31M461.426 4.759C438.078-4.913 411.2.432 393.33 18.303L249.999 161.632 106.669 18.303C88.798.432 61.922-4.913 38.573 4.759 15.224 14.43-.001 37.214-.001 62.488v375.026c0 34.51 27.977 62.486 62.487 62.486 34.51 0 62.486-27.976 62.486-62.486V213.341l80.843 80.844c24.404 24.402 63.965 24.402 88.369 0l80.843-80.844v224.173c0 34.51 27.976 62.486 62.486 62.486s62.486-27.976 62.486-62.486V62.488c0-25.274-15.224-48.058-38.573-57.729\" style=\"fill-rule:nonzero\"/></g></svg>`;\n"], "mappings": "yXAAA,OACE,cAAAA,EACA,YAAAC,EACA,0BAAAC,EAGA,iBAAAC,EACA,iBAAAC,EAEA,cAAAC,GACA,YAAAC,EAEA,kBAAAC,EACA,gBAAAC,EACA,gBAAAC,EACA,qBAAAC,EACA,eAAAC,EACA,oBAAAC,EACA,eAAAC,EACA,gBAAAC,EACA,iBAAAC,EACA,iBAAAC,EACA,uBAAAC,EACA,oBAAAC,GACA,eAAAC,GACA,YAAAC,GACA,eAAAC,GACA,uBAAAC,GACA,qBAAAC,OAEK,qBC1BP,IAAMC,EAAsB,IAAM,CAChC,GAAI,CAEF,MAAO,QACT,MAAQ,CAAC,CACT,MAAO,SACT,EAEMC,EAAyBD,EAAoB,EAEtCE,EAAmB,IAAMD,ED+BtC,OAAS,sBAAAE,EAAoB,UAAAC,OAAc,uBE7CpC,IAAMC,EAAU;kvDFoDhB,IAAMC,EAAa,CACxB,yBAA0B,2BAC1B,cAAe,gBACf,MAAO,QACP,iBAAkB,mBAClB,gBAAiB,kBACjB,UAAW,YACX,eAAgB,iBAChB,QAAS,UACT,eAAgB,iBAChB,eAAgB,iBAChB,gBAAiB,kBACjB,mBAAoB,qBACpB,iBAAkB,mBAClB,iBAAkB,mBAClB,eAAgB,iBAChB,aAAc,eACd,YAAa,cACb,qBAAsB,uBACtB,wBAAyB,0BACzB,iBAAkB,mBAClB,YAAa,cACb,gBAAiB,kBACjB,WAAY,aACZ,YAAa,cACb,mBAAoB,qBACpB,iBAAkB,mBAClB,KAAM,OACN,KAAM,MACR,EAEMC,GAAsB,OAAO,OAAOD,CAAU,EAEvCE,EAAwBC,EAAiB,EACzCC,EAAqB,YAtFlCC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAwFaC,EAAN,cAAkCC,CAAmD,CA6D1F,aAAc,CACZ,MAAM,EA9DHC,EAAA,KAAAJ,GAaLI,EAAA,KAAAd,GACAc,EAAA,KAAAb,GACAa,EAAA,KAAAZ,GACAY,EAAA,KAAAX,EAAsB,CAAC,GACvBW,EAAA,KAAAV,EAAkB,CAAC,GACnBU,EAAA,KAAAT,GACAS,EAAA,KAAAR,GACAQ,EAAA,KAAAP,GACAO,EAAA,KAAAN,GACAM,EAAA,KAAAL,EAAgB,IAyCdM,EAAA,KAAKb,EAAyBc,EAAuB,GAErD,KAAK,SAAS,iBAAiB,cAAgBC,GAAkB,CAzJrE,IAAAC,EA0JM,IAAMC,EAAkBC,EAAY,KAAK,QAAQ,EAC3CC,GAAeH,EAAA,KAAK,WAAL,KAAAA,EAAiB,CAAC,EAGvC,KAAK,SAAW,CACd,GAAGC,EACH,GAAGE,CACL,GAGKF,GAAA,YAAAA,EAA0B,6BAA8B,kBAC3DJ,EAAA,KAAKN,EAAQ,WACb,KAAK,WAAW,EAEpB,CAAC,CACH,CAhFA,WAAW,MAAO,CAChB,OAAOV,CACT,CAEA,WAAW,SAAU,CACnB,OAAOF,CACT,CAEA,WAAW,oBAAqB,CAjGlC,IAAAqB,EAkGI,MAAO,CAAC,GAAGtB,GAAqB,IAAIsB,EAAAL,EAAmB,qBAAnB,KAAAK,EAAyC,CAAC,CAAE,CAClF,CAaA,OAAO,YAAYI,EAA0B,CAC3C,MAAI,CAACA,GAAaA,IAAc,QAAgB,GACzCA,IAAc,UAAYC,EAAU,yBAAyBD,CAAS,MAC/E,CAEA,OAAO,gBAAgBE,EAAgC,CAAC,EAAG,CArH7D,IAAAN,EAsHI,MAAkB;AAAA,QACdL,EAAmB,gBAAgBW,CAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAyBvC,KAAK,aAAYN,EAAAM,EAAM7B,EAAW,IAAI,IAArB,KAAAuB,EAA0B,EAAE,CAAC;AAAA;AAAA,KAGtD,CAwBA,IAAI,YAAa,CA3KnB,IAAAA,EA4KI,OAAQA,EAAA,KAAK,aAAavB,EAAW,WAAW,IAAxC,KAAAuB,EAAoE,MAC9E,CAEA,IAAI,WAAWO,EAAuC,CAChDA,IAAU,KAAK,aACdA,EAEMC,EAAe,SAASD,CAAK,EACtC,KAAK,aAAa9B,EAAW,YAAa8B,CAAK,EAE/C,QAAQ,KAAK,gDAAgDC,EAAe,KAAK,CAAC,EAAE,EAJpF,KAAK,gBAAgB/B,EAAW,WAAW,EAM/C,CAEA,IAAI,gBAAiB,CACnB,OAAK,KAAK,aAAaA,EAAW,gBAAgB,EAC3C,CAAE,KAAK,aAAaA,EAAW,gBAAgB,EADMgC,EAAA,KAAKzB,EAEnE,CAEA,IAAI,eAAe0B,EAAK,CAElBA,GAAO,KAAK,iBAEZA,GAAO,KACT,KAAK,gBAAgBjC,EAAW,gBAAgB,EAEhD,KAAK,aAAaA,EAAW,iBAAkB,GAAG,CAACiC,CAAG,EAAE,EAE5D,CAEA,IAAI,oBAAqB,CA1M3B,IAAAV,EA2MI,OAAOA,EAAAS,EAAA,KAAKpB,KAAL,KAAAW,EAA4BnB,CACrC,CAEA,IAAI,mBAAmB0B,EAA2B,CAChDV,EAAA,KAAKR,EAAsBkB,EAC7B,CAEA,IAAI,uBAAwB,CAlN9B,IAAAP,EAmNI,OAAOA,EAAAS,EAAA,KAAKrB,KAAL,KAAAY,EAA+BrB,CACxC,CAEA,IAAI,sBAAsB4B,EAA2B,CACnDV,EAAA,KAAKT,EAAyBmB,EAChC,CAGA,IAAI,MAAmC,CA3NzC,IAAAP,EA4NI,OAAOA,EAAAS,EAAA,KAAK3B,KAAL,YAAAkB,EAAY,MACrB,CAEA,IAAI,KAAqD,CA/N3D,IAAAA,EAgOI,OAAOA,EAAA,KAAK,WAAL,YAAAA,EAAe,GACxB,CAEA,IAAI,OAAQ,CAnOd,IAAAA,EAoOI,OAAOA,EAAAW,EAAS,KAAK,QAAQ,IAAtB,KAAAX,EAA2B,IACpC,CAEA,IAAI,iBAA0D,CAC5D,OAAOS,EAAA,KAAKnB,EACd,CAEA,IAAI,gBAAgBiB,EAA+C,CACjEV,EAAA,KAAKP,EAAmBiB,EAC1B,CAEA,IAAI,KAAM,CAIR,OAAO,KAAK,aAAa,KAAK,CAChC,CAEA,IAAI,IAAIG,EAAa,CAGfA,IAAQ,KAAK,MAEbA,GAAO,KACT,KAAK,gBAAgB,KAAK,EAE1B,KAAK,aAAa,MAAOA,CAAG,EAEhC,CAEA,IAAI,MAAkD,CAlQxD,IAAAV,EAmQI,OAAQA,EAAA,KAAK,aAAavB,EAAW,IAAI,IAAjC,KAAAuB,EAAwE,MAClF,CAEA,IAAI,KAAKU,EAAgD,CAEnDA,IAAQ,KAAK,OAEbA,EACF,KAAK,aAAajC,EAAW,KAAMiC,CAAG,EAEtC,KAAK,gBAAgBjC,EAAW,IAAI,EAExC,CAEA,IAAI,SAAU,CACZ,IAAMiC,EAAM,KAAK,aAAa,SAAS,EACvC,OAAIA,IAAQ,GAAW,OACnB,CAAC,OAAQ,WAAY,MAAM,EAAE,SAASA,CAAG,EAAUA,EAChD,MAAM,OACf,CAEA,IAAI,QAAQA,EAAK,CAGXA,GAAO,KAAK,aAAa,SAAS,IAElC,CAAC,GAAI,OAAQ,WAAY,MAAM,EAAE,SAASA,CAAG,EAC/C,KAAK,aAAa,UAAWA,CAAG,EAEhC,KAAK,gBAAgB,SAAS,EAElC,CAEA,IAAI,OAAQ,CACV,OAAO,KAAK,aAAajC,EAAW,KAAK,GAAK,IAChD,CAEA,IAAI,MAAMiC,EAAK,CAETA,IAAQ,KAAK,QAEbA,EACF,KAAK,aAAajC,EAAW,MAAO,EAAE,EAEtC,KAAK,gBAAgBA,EAAW,KAAK,EAEzC,CAEA,IAAI,iBAAkB,CACpB,OAAO,KAAK,aAAaA,EAAW,gBAAgB,CACtD,CAEA,IAAI,gBAAgBiC,EAAK,CAEnBA,IAAQ,KAAK,iBAEjB,KAAK,gBAAgBjC,EAAW,iBAAkB,CAAC,CAACiC,CAAG,CACzD,CAEA,IAAI,gBAAiB,CACnB,OAAO,KAAK,aAAajC,EAAW,eAAe,CACrD,CAEA,IAAI,eAAeiC,EAAK,CAElBA,IAAQ,KAAK,iBAEbA,EACF,KAAK,aAAajC,EAAW,gBAAiB,EAAE,EAEhD,KAAK,gBAAgBA,EAAW,eAAe,EAEnD,CAEA,IAAI,WAAgC,CAClC,IAAMiC,EAAM,KAAK,aAAajC,EAAW,UAAU,EACnD,GAAIiC,GAAO,KAAM,OACjB,IAAME,EAAM,CAACF,EACb,OAAQ,OAAO,MAAME,CAAG,EAAU,OAANA,CAC9B,CAEA,IAAI,UAAUF,EAAyB,CAEjCA,IAAQ,KAAK,YAEbA,GAAO,KACT,KAAK,gBAAgBjC,EAAW,UAAU,EAE1C,KAAK,aAAaA,EAAW,WAAY,GAAGiC,CAAG,EAAE,EAErD,CAGA,IAAI,YAAiC,CAhWvC,IAAAV,EAiWI,OAAI,KAAK,aAAavB,EAAW,WAAW,EACnC,KAAK,aAAaA,EAAW,WAAW,GAG1CuB,EAAAa,GAAoB,KAAK,GAAG,IAA5B,KAAAb,EAAiC,MAC1C,CAEA,IAAI,WAAWU,EAAyB,CAElCA,IAAQ,KAAK,aAEbA,EACF,KAAK,aAAajC,EAAW,YAAaiC,CAAG,EAE7C,KAAK,gBAAgBjC,EAAW,WAAW,EAE/C,CAEA,IAAI,eAAgB,CAnXtB,IAAAuB,EAoXI,OAAQA,EAAA,KAAK,aAAavB,EAAW,cAAc,IAA3C,KAAAuB,EAAuE,MACjF,CAEA,IAAI,cAAcU,EAAqC,CACjDA,IAAQ,KAAK,gBAEbA,EACF,KAAK,aAAajC,EAAW,eAAgBiC,CAAG,EAEhD,KAAK,gBAAgBjC,EAAW,cAAc,EAElD,CAEA,IAAI,eAAgB,CAjYtB,IAAAuB,EAkYI,OAAQA,EAAA,KAAK,aAAavB,EAAW,cAAc,IAA3C,KAAAuB,EAAuE,MACjF,CAEA,IAAI,cAAcU,EAAqC,CACjDA,IAAQ,KAAK,gBAEbA,EACF,KAAK,aAAajC,EAAW,eAAgBiC,CAAG,EAEhD,KAAK,gBAAgBjC,EAAW,cAAc,EAElD,CAEA,IAAI,gBAAiB,CA/YvB,IAAAuB,EAgZI,OAAQA,EAAA,KAAK,aAAavB,EAAW,eAAe,IAA5C,KAAAuB,EAAyE,MACnF,CAEA,IAAI,eAAeU,EAAsC,CACnDA,IAAQ,KAAK,iBAEbA,EACF,KAAK,aAAajC,EAAW,gBAAiBiC,CAAG,EAEjD,KAAK,gBAAgBjC,EAAW,eAAe,EAEnD,CAEA,IAAI,kBAAmB,CACrB,IAAMiC,EAAM,KAAK,aAAajC,EAAW,kBAAkB,EAC3D,GAAIiC,GAAO,KAAM,OACjB,IAAME,EAAM,CAACF,EACb,OAAQ,OAAO,MAAME,CAAG,EAAU,OAANA,CAC9B,CAEA,IAAI,iBAAiBF,EAAyB,CACxCA,GAAO,KACT,KAAK,gBAAgBjC,EAAW,kBAAkB,EAElD,KAAK,aAAaA,EAAW,mBAAoB,GAAGiC,CAAG,EAAE,CAE7D,CAEA,IAAI,gBAAiB,CACnB,IAAMA,EAAM,KAAK,aAAajC,EAAW,gBAAgB,EACzD,GAAIiC,GAAO,KAAM,OACjB,IAAME,EAAM,CAACF,EACb,OAAQ,OAAO,MAAME,CAAG,EAAU,OAANA,CAC9B,CAEA,IAAI,eAAeF,EAAyB,CACtCA,GAAO,KACT,KAAK,gBAAgBjC,EAAW,gBAAgB,EAEhD,KAAK,aAAaA,EAAW,iBAAkB,GAAGiC,CAAG,EAAE,CAE3D,CAEA,IAAI,gBAAiB,CACnB,IAAMA,EAAM,KAAK,aAAajC,EAAW,gBAAgB,EACzD,GAAIiC,GAAO,KAAM,OACjB,IAAME,EAAM,CAACF,EACb,OAAQ,OAAO,MAAME,CAAG,EAAU,OAANA,CAC9B,CAEA,IAAI,eAAeF,EAAyB,CACtCA,GAAO,KACT,KAAK,gBAAgBjC,EAAW,gBAAgB,EAEhD,KAAK,aAAaA,EAAW,iBAAkB,GAAGiC,CAAG,EAAE,CAE3D,CAEA,IAAI,cAAe,CACjB,IAAMA,EAAM,KAAK,aAAajC,EAAW,cAAc,EACvD,GAAIiC,GAAO,KAAM,OACjB,IAAME,EAAM,CAACF,EACb,OAAQ,OAAO,MAAME,CAAG,EAAU,OAANA,CAC9B,CAEA,IAAI,aAAaF,EAAyB,CACpCA,GAAO,KACT,KAAK,gBAAgBjC,EAAW,cAAc,EAE9C,KAAK,aAAaA,EAAW,eAAgB,GAAGiC,CAAG,EAAE,CAEzD,CAEA,IAAI,cAAe,CAzdrB,IAAAV,EA0dI,OAAOA,EAAA,KAAK,aAAavB,EAAW,aAAa,IAA1C,KAAAuB,EAA+C,MACxD,CAEA,IAAI,aAAaU,EAAyB,CAEpCA,IAAQ,KAAK,eAEbA,EACF,KAAK,aAAajC,EAAW,cAAeiC,CAAG,EAE/C,KAAK,gBAAgBjC,EAAW,aAAa,EAEjD,CAEA,IAAI,UAAW,CAxejB,IAAAuB,EAyeI,OAAOA,EAAA,KAAK,aAAavB,EAAW,SAAS,IAAtC,KAAAuB,EAA2C,MACpD,CAEA,IAAI,SAASU,EAAyB,CAEhCA,IAAQ,KAAK,WAEbA,EACF,KAAK,aAAajC,EAAW,UAAWiC,CAAG,EAE3C,KAAK,gBAAgBjC,EAAW,SAAS,EAE7C,CAKA,IAAI,eAAgB,CA1ftB,IAAAuB,EAAAc,EAAAC,EAAAC,EA2fI,GAAI,KAAK,aAAavC,EAAW,cAAc,EAC7C,OAAOuB,EAAA,KAAK,aAAavB,EAAW,cAAc,IAA3C,KAAAuB,EAAgD,OAEzD,GAAI,KAAK,aAAavB,EAAW,WAAW,EAAG,CAC7C,GAAM,CAAC,CAAEwC,CAAU,EAAIC,IAAkBJ,EAAA,KAAK,aAAL,KAAAA,EAAmB,EAAE,EAC9D,OAAOC,EAAA,IAAI,gBAAgBE,CAAU,EAAE,IAAI,OAAO,IAA3C,KAAAF,EAAgD,MACzD,CACA,GAAI,KAAK,IACP,OAAOC,EAAA,IAAI,gBAAgB,KAAK,GAAG,EAAE,IAAI,OAAO,IAAzC,KAAAA,EAA8C,MAGzD,CAKA,IAAI,cAAcN,EAAyB,CACrCA,IAAQ,KAAK,gBAEbA,EACF,KAAK,aAAajC,EAAW,eAAgBiC,CAAG,EAEhD,KAAK,gBAAgBjC,EAAW,cAAc,EAElD,CAEA,IAAI,QAAS,CACX,IAAM0C,EAAW,KAAK,aAAa1C,EAAW,cAAc,EACtD2C,EAAM,KAAK,aAAa3C,EAAW,SAAS,EAClD,MAAO,CACL,GAAGgC,EAAA,KAAKvB,GACR,GAAIiC,GAAY,KAAO,CAAE,SAAAA,CAAS,EAAI,CAAC,EACvC,GAAIC,GAAO,KAAO,CAAE,IAAAA,CAAI,EAAI,CAAC,CAC/B,CACF,CAEA,IAAI,OAAOV,EAAK,CACdb,EAAA,KAAKX,EAAUwB,GAAA,KAAAA,EAAO,CAAC,EACzB,CAEA,IAAI,OAAQ,CAGV,OAAOW,GAAS,KAAK,SAAU,KAAK,IAAI,CAC1C,CAEA,IAAI,QAA6B,CAziBnC,IAAArB,EA0iBI,OAAOA,EAAA,KAAK,aAAavB,EAAW,OAAO,IAApC,KAAAuB,EAAyC,MAClD,CAEA,IAAI,OAAOU,EAAyB,CAE9BA,IAAQ,KAAK,SAEbA,EACF,KAAK,aAAajC,EAAW,QAASiC,CAAG,EAEzC,KAAK,gBAAgBjC,EAAW,OAAO,EAE3C,CAEA,IAAI,wBAA6C,CAxjBnD,IAAAuB,EAyjBI,OAAOA,EAAA,KAAK,aAAavB,EAAW,wBAAwB,IAArD,KAAAuB,EAA0D,MACnE,CAEA,IAAI,uBAAuBU,EAAyB,CAE9CA,IAAQ,KAAK,yBAEbA,EACF,KAAK,aAAajC,EAAW,yBAA0BiC,CAAG,EAE1D,KAAK,gBAAgBjC,EAAW,wBAAwB,EAE5D,CAEA,IAAI,YAA+C,CAvkBrD,IAAAuB,EAykBI,OAAQA,EAAA,KAAK,aAAavB,EAAW,WAAW,IAAxC,KAAAuB,EAAsEsB,EAAc,KAAK,QAAQ,CAC3G,CAEA,IAAI,WAAWZ,EAAuC,CAEhDA,IAAQ,KAAK,aAEbA,EACF,KAAK,aAAajC,EAAW,YAAaiC,CAAG,EAE7C,KAAK,gBAAgBjC,EAAW,WAAW,EAE/C,CAEA,IAAI,kBAAmB,CAErB,OAAI,KAAK,aAAaA,EAAW,kBAAkB,EAC1C,CAAE,KAAK,aAAaA,EAAW,kBAAkB,EAEnD8C,EAAoB,KAAK,QAAQ,CAC1C,CAEA,IAAI,iBAAiBb,EAAyB,CAExCA,GAAO,KAAK,mBAEZA,GAAO,KACT,KAAK,gBAAgBjC,EAAW,kBAAkB,EAElD,KAAK,aAAaA,EAAW,mBAAoB,GAAG,CAACiC,CAAG,EAAE,EAE9D,CAEA,IAAI,eAAgB,CA1mBtB,IAAAV,EAAAc,EA2mBI,GAAI,KAAK,aAAarC,EAAW,gBAAgB,EAAG,CAClD,GAAM,CAAE,eAAA+C,CAAe,EAAI,KACrBC,GAAczB,EAAA,KAAK,SAAS,SAAS,IAAI,CAAC,IAA5B,KAAAA,EAAiC,EAC/C0B,GAAgBZ,EAAA,KAAK,SAAS,SAAS,MAAM,CAAC,IAA9B,KAAAA,EAAmC,EACzD,OAAO,KAAK,IAAIY,EAAeD,EAAeD,CAAyB,CACzE,CACA,OAAOG,GAAiB,KAAK,QAAQ,CACvC,CAEA,IAAI,gBAAiB,CACnB,GAAK,KAAK,aAAalD,EAAW,gBAAgB,EAClD,MAAO,CAAE,KAAK,aAAaA,EAAW,gBAAgB,CACxD,CAEA,IAAI,eAAeiC,EAAyB,CAEtCA,GAAO,KAAK,iBAEZA,GAAO,KACT,KAAK,gBAAgBjC,EAAW,gBAAgB,EAEhD,KAAK,aAAaA,EAAW,iBAAkB,GAAG,CAACiC,CAAG,EAAE,EAE5D,CAEA,IAAI,UAAW,CACb,OAAOkB,GAAY,KAAK,QAAQ,CAClC,CAEA,MAAM,aAAsBC,EAA0B,CACpD,OAAOC,EAAa,KAAK,SAAUD,CAAS,CAC9C,CAEA,IAAI,gBAAiB,CACnB,OAAOE,EAAkB,KAAK,QAAQ,CACxC,CAEA,IAAI,WAAY,CACd,OAAOC,EAAa,KAAK,QAAQ,CACnC,CAEA,MAAM,YAAYC,EAAqB,CACrC,OAAOC,EAAY,KAAK,SAAUD,CAAQ,CAC5C,CAEA,IAAI,eAAgB,CAClB,OAAOE,EAAiB,KAAK,QAAQ,CACvC,CAEA,IAAI,UAAW,CACb,OAAOC,GAAY,KAAK,QAAQ,CAClC,CAEA,cAAe,CACb,OAAOC,EAAa,KAAK,SAAU,KAAK,IAAI,CAC9C,CAEA,IAAI,YAAa,CACf,OAAOC,EAAc,KAAK,SAAU,KAAK,IAAI,CAC/C,CAEA,IAAI,gBAAqD,CACvD,IAAM5B,EAAM,KAAK,aAAajC,EAAW,eAAe,EACxD,GAAIiC,IAAQ6B,EAAc,KAAO7B,IAAQ6B,EAAc,OAAQ,OAAO7B,CAExE,CAEA,IAAI,eAAeA,EAAyC,CACtDA,IAAQ,KAAK,iBAEbA,IAAQ6B,EAAc,KAAO7B,IAAQ6B,EAAc,OACrD,KAAK,aAAa9D,EAAW,gBAAiBiC,CAAG,EAEjD,KAAK,gBAAgBjC,EAAW,eAAe,EAEnD,CAEA,IAAI,UAAW,CAgBb,MAAO,CACL,GAhBuD,KAAK,kBAAkB,EAC7E,OAAQ+D,GACAA,EAAS,WAAW,WAAW,GAAK,CAAE,CAAC/D,EAAW,YAAY,EAAe,SAAS+D,CAAQ,CACtG,EACA,OACC,CAACC,EAAWD,IAAa,CACvB,IAAMjC,EAAQ,KAAK,aAAaiC,CAAQ,EACxC,OAAIjC,GAAS,OACXkC,EAAUD,EAAS,QAAQ,aAAc,EAAE,EAAE,QAAQ,KAAM,GAAG,CAAW,EAAIjC,GAExEkC,CACT,EACA,CAAC,CACH,EAIA,GAAGhC,EAAA,KAAKxB,EACV,CACF,CAEA,IAAI,SAASyB,EAAqC,CAChDb,EAAA,KAAKZ,EAAYyB,GAAA,KAAAA,EAAO,CAAC,GACnB,KAAK,KACT,KAAK,IAAI,KAAK,KAAMD,EAAA,KAAKxB,EAAS,CAEtC,CAEA,IAAI,YAAa,CACf,OAAOwB,EAAA,KAAKtB,EACd,CAEA,IAAI,WAAWuB,EAA+C,CAC5Db,EAAA,KAAKV,EAAcuB,EACrB,CAEA,IAAI,MAAO,CA7tBb,IAAAV,EA8tBI,OAAOA,EAAA,KAAK,aAAavB,EAAW,IAAI,IAAjC,KAAAuB,EAAsCS,EAAA,KAAKlB,EACpD,CAEA,IAAI,KAAKmB,EAAK,CACRA,EACF,KAAK,aAAajC,EAAW,KAAMiC,CAAG,EAEtC,KAAK,gBAAgBjC,EAAW,IAAI,CAExC,CASA,MAAO,CACLoB,EAAA,KAAKf,EAAQ4D,EAAW,KAAgC,KAAK,SAAUjC,EAAA,KAAK3B,EAAK,EACnF,CAEA,QAAS,CACP6D,EAAS,KAAK,SAAUlC,EAAA,KAAK3B,GAAO,IAA8B,EAClEe,EAAA,KAAKf,EAAQ,OACf,CAEA,yBAAyB0D,EAAkBI,EAAyBC,EAAyB,CAzvB/F,IAAA7C,EAAAc,EAgwBI,OALqBnB,EAAmB,mBAAmB,SAAS6C,CAAQ,GACxD,CAAC,CAAC,MAAO,WAAY,SAAS,EAAE,SAASA,CAAQ,GACnE,MAAM,yBAAyBA,EAAUI,EAAUC,CAAQ,EAGrDL,EAAU,CAChB,KAAK/D,EAAW,qBACd,KAAK,mBAAqBoE,GAAA,KAAAA,EAAY,OACtC,MACF,KAAKpE,EAAW,wBACd,KAAK,sBAAwBoE,GAAA,KAAAA,EAAY,OACzC,MACF,IAAK,MAAO,CACV,IAAMC,EAAS,CAAC,CAACF,EACXG,EAAS,CAAC,CAACF,EACb,CAACC,GAAUC,EACbC,EAAA,KAAKxD,EAAAC,GAAL,WACSqD,GAAU,CAACC,EACpB,KAAK,OAAO,EACHD,GAAUC,IACnB,KAAK,OAAO,EACZC,EAAA,KAAKxD,EAAAC,GAAL,YAEF,KACF,CACA,IAAK,WACH,GAAIoD,IAAaD,EACf,OAGF5C,EAAAS,EAAA,KAAK3B,KAAL,MAAAkB,EAAY,YAAY,KAAK,UAC7B,MACF,IAAK,UACH,GAAI6C,IAAaD,EACf,OAEF9B,EAAAL,EAAA,KAAK3B,KAAL,MAAAgC,EAAY,WAAW+B,GACvB,MACF,KAAKpE,EAAW,YACd,KAAK,IAAMwE,EAAc,IAAI,EAC7B,MACF,KAAKxE,EAAW,MAAO,CACrB,IAAMyE,EAAQ,KAAK,MACb,KAAK,KAET,QAAQ,KACN,+HACF,EAEI,KAAK,OACT,KAAK,KAAK,OAAO,MAAQA,GAE3B,KACF,CACA,KAAKzE,EAAW,aACVoE,GACF,MAAMA,CAAQ,EACX,KAAMM,GAASA,EAAK,KAAK,CAAC,EAC1B,KAAMC,GAAU,KAAK,SAAWA,CAAK,EACrC,MAAM,IAAM,QAAQ,MAAM,2DAA2DP,CAAQ,GAAG,CAAC,EAEtG,MACF,KAAKpE,EAAW,aAEVoE,GAAY,MAAQA,IAAaD,IACnC,KAAK,cAAc,IAAI,YAAY,mBAAoB,CAAE,SAAU,GAAM,QAAS,EAAK,CAAC,CAAC,EAE3F,MACF,KAAKnE,EAAW,oBACVoE,GAAY,MAAQA,IAAaD,IACnC,KAAK,cACH,IAAI,YAAY,yBAA0B,CAAE,SAAU,GAAM,QAAS,GAAM,OAAQ,KAAK,gBAAiB,CAAC,CAC5G,EAEF,MACF,KAAKnE,EAAW,MACVoE,GAAY,MAAQA,IAAaD,IACnC,KAAK,WAAW,EAElB,KACJ,CACF,CAEA,YAAa,CACX,GAAI,CAAC,KAAK,WAAY,OAEtB,IAAMS,EAAW,KAAK,WAAW,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAU,OAEf,IAAMC,EAAY,KAAK,YAAuC,YAAY7C,EAAA,KAAKlB,IAAS,KAAK,IAAI,EACjG8D,EAAS,UAAYC,CACvB,CAEA,mBAA0B,CAx1B5B,IAAAtD,GAy1BIA,EAAA,MAAM,oBAAN,MAAAA,EAAA,WACI,KAAK,UAAY,KAAK,KAAO,CAACS,EAAA,KAAK3B,IACrCkE,EAAA,KAAKxD,EAAAC,GAAL,UAEJ,CAEA,sBAA6B,CAC3B,KAAK,OAAO,CACd,CAEA,YAAY8D,EAAoB,CAC1BA,EAAM,SAAW,KAAK,UAExB,KAAK,cACH,IAAI,YAAYA,EAAM,KAAM,CAC1B,SAAU,GACV,OAASA,EAAsB,MACjC,CAAC,CACH,CAEJ,CACF,EAzwBEzE,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YAtBKC,EAAA,YAipBCC,EAAY,gBAAG,CACfgB,EAAA,KAAK1B,KACT,MAAOc,EAAA,KAAKd,EAAiB,QAAQ,QAAQ,GAC7Cc,EAAA,KAAKd,EAAiB,MACtB,KAAK,KAAK,EACZ", "names": ["initialize", "teardown", "generatePlayerInitTime", "PlaybackTypes", "toMuxVideoURL", "MediaError", "getError", "CmcdTypeValues", "addCuePoints", "getCuePoints", "getActiveCuePoint", "addChapters", "getActiveChapter", "getMetadata", "getStartDate", "getCurrentPdt", "getStreamType", "getTargetLiveWindow", "getLiveEdgeStart", "getSeekable", "getEnded", "getChapters", "toPlaybackIdFromSrc", "toPlaybackIdParts", "getEnvPlayerVersion", "player_version", "getPlayerVersion", "CustomVideoElement", "Events", "mux<PERSON><PERSON>", "Attributes", "AttributeNameValues", "playerSoftwareVersion", "getPlayerVersion", "playerSoftwareName", "_core", "_loadRequested", "_defaultPlayerInitTime", "_metadata", "_tokens", "__hlsConfig", "_playerSoftwareVersion", "_playerSoftwareName", "_errorTranslator", "_logo", "_MuxVideoBaseElement_instances", "requestLoad_fn", "MuxVideoBaseElement", "CustomVideoElement", "__privateAdd", "__privateSet", "generatePlayerInitTime", "_event", "_a", "fetchedMetadata", "getMetadata", "userMetadata", "logoValue", "mux<PERSON><PERSON>", "attrs", "value", "CmcdTypeValues", "__privateGet", "val", "getError", "num", "toPlaybackIdFromSrc", "_b", "_c", "_d", "queryParts", "toPlaybackIdParts", "playback", "drm", "getEnded", "getStreamType", "getTargetLiveWindow", "liveEdgeOffset", "seekableEnd", "seekableStart", "getLiveEdgeStart", "getSeekable", "cuePoints", "addCuePoints", "getActiveCuePoint", "getCuePoints", "chapters", "addChapters", "getActiveChapter", "getChapters", "getStartDate", "getCurrentPdt", "PlaybackTypes", "attrName", "currAttrs", "initialize", "teardown", "oldValue", "newValue", "hadSrc", "hasSrc", "__privateMethod", "toMuxVideoURL", "debug", "resp", "json", "slotLogo", "logoHTML", "event"]}