{"version": 3, "sources": ["../src/index.tsx", "../src/common/utils.ts", "../src/useComposedRefs.ts", "../src/useObjectPropEffect.ts", "../src/env.ts", "../src/useEventCallbackEffect.ts"], "sourcesContent": ["'use client';\nimport React, { useState, useRef } from 'react';\nimport { MaxResolution, MinResolution, RenditionOrder, generatePlayerInitTime } from '@mux/playback-core';\nimport { MediaError } from '@mux/mux-player';\nimport type MuxPlayerElement from '@mux/mux-player';\nimport type { MuxPlayerElementEventMap } from '@mux/mux-player';\nimport { toNativeProps } from './common/utils';\nimport { useComposedRefs } from './useComposedRefs';\nimport useObjectPropEffect, { defaultHasChanged } from './useObjectPropEffect';\nimport { getPlayerVersion } from './env';\nimport { useEventCallbackEffect } from './useEventCallbackEffect';\nimport type { MuxPlayerProps, MuxPlayerRefAttributes } from './types';\n\nexport { MediaError, MaxResolution, MinResolution, RenditionOrder, generatePlayerInitTime };\nexport * from './types';\n\nconst MuxPlayerInternal = React.forwardRef<MuxPlayerRefAttributes, MuxPlayerProps>(({ children, ...props }, ref) => {\n  return React.createElement(\n    'mux-player',\n    {\n      suppressHydrationWarning: true, // prevent issues with SSR / player-init-time\n      ...toNativeProps(props),\n      ref,\n    },\n    children\n  );\n});\n\nconst usePlayer = (\n  ref: // | ((instance: EventTarget | null) => void)\n  React.MutableRefObject<MuxPlayerElement | null> | null | undefined,\n  props: MuxPlayerProps\n) => {\n  const {\n    onAbort,\n    onCanPlay,\n    onCanPlayThrough,\n    onEmptied,\n    onLoadStart,\n    onLoadedData,\n    onLoadedMetadata,\n    onProgress,\n    onDurationChange,\n    onVolumeChange,\n    onRateChange,\n    onResize,\n    onWaiting,\n    onPlay,\n    onPlaying,\n    onTimeUpdate,\n    onPause,\n    onSeeking,\n    onSeeked,\n    onStalled,\n    onSuspend,\n    onEnded,\n    onError,\n    onCuePointChange,\n    onChapterChange,\n    metadata,\n    tokens,\n    paused,\n    playbackId,\n    playbackRates,\n    currentTime,\n    themeProps,\n    extraSourceParams,\n    castCustomData,\n    _hlsConfig,\n    ...remainingProps\n  } = props;\n  useObjectPropEffect('playbackRates', playbackRates, ref);\n  useObjectPropEffect('metadata', metadata, ref);\n  useObjectPropEffect('extraSourceParams', extraSourceParams, ref);\n  useObjectPropEffect('_hlsConfig', _hlsConfig, ref);\n  useObjectPropEffect('themeProps', themeProps, ref);\n  useObjectPropEffect('tokens', tokens, ref);\n  useObjectPropEffect('playbackId', playbackId, ref);\n  useObjectPropEffect('castCustomData', castCustomData, ref);\n  useObjectPropEffect(\n    'paused',\n    paused,\n    ref,\n    (playerEl: HTMLMediaElement, pausedVal?: boolean) => {\n      if (pausedVal == null) return;\n      if (pausedVal) {\n        playerEl.pause();\n      } else {\n        playerEl.play();\n      }\n    },\n    (playerEl, value, propName) => {\n      if (playerEl.hasAttribute('autoplay') && !playerEl.hasPlayed) {\n        return false;\n      }\n      return defaultHasChanged(playerEl, value, propName);\n    }\n  );\n  useObjectPropEffect('currentTime', currentTime, ref, (playerEl: HTMLMediaElement, currentTimeVal?: number) => {\n    if (currentTimeVal == null) return;\n    playerEl.currentTime = currentTimeVal;\n  });\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('abort', ref, onAbort);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('canplay', ref, onCanPlay);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('canplaythrough', ref, onCanPlayThrough);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('emptied', ref, onEmptied);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('loadstart', ref, onLoadStart);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('loadeddata', ref, onLoadedData);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('loadedmetadata', ref, onLoadedMetadata);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('progress', ref, onProgress);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('durationchange', ref, onDurationChange);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('volumechange', ref, onVolumeChange);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('ratechange', ref, onRateChange);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('resize', ref, onResize);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('waiting', ref, onWaiting);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('play', ref, onPlay);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('playing', ref, onPlaying);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('timeupdate', ref, onTimeUpdate);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('pause', ref, onPause);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('seeking', ref, onSeeking);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('seeked', ref, onSeeked);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('stalled', ref, onStalled);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('suspend', ref, onSuspend);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('ended', ref, onEnded);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('error', ref, onError);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('cuepointchange', ref, onCuePointChange);\n  useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>('chapterchange', ref, onChapterChange);\n  return [remainingProps];\n};\n\nexport const playerSoftwareVersion = getPlayerVersion();\nexport const playerSoftwareName = 'mux-player-react';\n\nconst MuxPlayer = React.forwardRef<\n  MuxPlayerRefAttributes,\n  Omit<MuxPlayerProps, 'playerSoftwareVersion' | 'playerSoftwareName'>\n>((props, ref) => {\n  const innerPlayerRef = useRef<MuxPlayerElement>(null);\n  const playerRef = useComposedRefs(innerPlayerRef, ref);\n  const [remainingProps] = usePlayer(innerPlayerRef, props);\n  const [playerInitTime] = useState(props.playerInitTime ?? generatePlayerInitTime());\n\n  return (\n    <MuxPlayerInternal\n      /** @TODO Fix types relationships (CJP) */\n      ref={playerRef as React.Ref<MuxPlayerElement>}\n      defaultHiddenCaptions={props.defaultHiddenCaptions}\n      playerSoftwareName={playerSoftwareName}\n      playerSoftwareVersion={playerSoftwareVersion}\n      playerInitTime={playerInitTime}\n      {...remainingProps}\n    />\n  );\n});\n\nexport default MuxPlayer;\n", "import React from 'react';\n\n// React 19 supports custom elements and setting properties directly on them,\n// older React versions converted all props to attributes on custom elments.\n// Boolean `true` values should not be converted to empty strings in React 19+\n// because that would result in a `false` value if it was set via a property.\n// React 19+ handles primitive values correctly but we still need to convert\n// the camelCase prop names to kebab-case attribute names for mux-player. (WL)\n\nconst IS_REACT_19_OR_NEWER = parseInt(React.version) >= 19;\n\n// NOTE: As a forward-looking implementation, we may want to assume\n// prop names -> attribute names is always a simple name.toLowerCase()\n// and provide a mechanism for passing in per-component overrides for\n// e.g. kebab cases, as that's the way React/Preact handles these. (CJP)\nconst ReactPropToAttrNameMap = {\n  className: 'class',\n  classname: 'class',\n  htmlFor: 'for',\n  crossOrigin: 'crossorigin',\n  viewBox: 'viewBox',\n  playsInline: 'playsinline',\n  autoPlay: 'autoplay',\n  playbackRate: 'playbackrate',\n};\n\ntype KeyTypes = string | number | symbol;\ntype Maybe<T> = T | null | undefined;\n\nexport const isNil = (x: unknown): x is null | undefined => x == undefined;\n\n// Type Guard to determine if a given key is actually a key of some object of type T\nexport const isKeyOf = <T extends object = any>(k: KeyTypes, o: Maybe<T>): k is keyof T => {\n  if (isNil(o)) return false;\n  return k in o;\n};\n\nconst toKebabCase = (string: string) => string.replace(/[A-Z]/g, (match) => `-${match.toLowerCase()}`);\n\nexport const toNativeAttrName = (propName: string, propValue: any): string | undefined => {\n  if (!IS_REACT_19_OR_NEWER && typeof propValue === 'boolean' && !propValue) return undefined;\n  if (isKeyOf(propName, ReactPropToAttrNameMap)) return ReactPropToAttrNameMap[propName];\n  if (typeof propValue === 'undefined') return undefined;\n  if (/[A-Z]/.test(propName)) return toKebabCase(propName);\n  return propName;\n};\nexport const toStyleAttr = <T>(x: T) => x;\n\nexport const toNativeAttrValue = (propValue: any, _propName: string) => {\n  if (!IS_REACT_19_OR_NEWER && typeof propValue === 'boolean') return '';\n  return propValue;\n};\n\nexport const toNativeProps = (props: { ref?: any; [key: string]: any } = {}) => {\n  const { ref, ...restProps } = props;\n  return Object.entries(restProps).reduce<{ [k: string]: string }>((transformedProps, [propName, propValue]) => {\n    const attrName = toNativeAttrName(propName, propValue);\n\n    // prop was stripped. Don't add.\n    if (!attrName) {\n      return transformedProps;\n    }\n\n    const attrValue = toNativeAttrValue(propValue, propName);\n    transformedProps[attrName] = attrValue;\n    return transformedProps;\n  }, {});\n};\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T): (() => void) | void | undefined {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n", "import { useEffect } from 'react';\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Slightly modified version of React's shallowEqual, with optimizations for Arrays\n * so we may treat them specifically as unequal if they are not a) both arrays\n * or b) don't contain the same (shallowly compared) elements.\n */\nconst shallowEqual = (objA: any, objB: any): boolean => {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n\n  if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {\n    return false;\n  }\n\n  if (Array.isArray(objA)) {\n    // Early \"cheap\" array compares\n    if (!Array.isArray(objB) || objA.length !== objB.length) return false;\n    // Shallow compare for arrays\n    return objA.some((vVal, i) => objB[i] === vVal);\n  }\n\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  // Test for A's keys different from B.\n  for (let i = 0; i < keysA.length; i++) {\n    if (!hasOwnProperty.call(objB, keysA[i]) || !Object.is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\nexport const defaultHasChanged = (obj: any, v: any, k: string) => {\n  return !shallowEqual(v, obj[k]);\n};\n\nconst defaultUpdateValue = (obj: any, v: any, k: string) => {\n  obj[k] = v;\n};\n\nexport const useObjectPropEffect = <T extends { [k: string]: any }, V>(\n  propName: string,\n  propValue: V | null | undefined,\n  ref: React.MutableRefObject<T | null> | null | undefined,\n  updateValue = defaultUpdateValue,\n  hasChanged = defaultHasChanged\n) => {\n  return useEffect(() => {\n    const obj = ref?.current;\n    if (!obj) return;\n    if (!hasChanged(obj, propValue, propName)) return;\n    updateValue(obj, propValue, propName);\n  }, [ref?.current, propValue]);\n};\n\nexport default useObjectPropEffect;\n", "const getEnvPlayerVersion = () => {\n  try {\n    // @ts-ignore\n    return PLAYER_VERSION as string;\n  } catch {}\n  return 'UNKNOWN';\n};\n\nconst player_version: string = getEnvPlayerVersion();\n\nexport const getPlayerVersion = () => player_version;\n", "import React, { useEffect } from 'react';\nimport type { GenericEventListener } from './index';\n\nexport const useEventCallbackEffect = <\n  TElement extends EventTarget = EventTarget,\n  TEventMap extends Record<string, Event> = Record<string, Event>,\n  K extends keyof TEventMap = keyof TEventMap,\n>(\n  type: K,\n  ref: // | ((instance: EventTarget | null) => void)\n  React.MutableRefObject<TElement | null> | null | undefined,\n  callback: GenericEventListener<TEventMap[K]> | undefined\n) => {\n  return useEffect(() => {\n    const eventTarget = ref?.current;\n    if (!eventTarget || !callback) return;\n\n    // Type assertion needed because TypeScript can't infer the exact event type\n    const eventName = type as string;\n    const listener = callback as EventListener;\n\n    eventTarget.addEventListener(eventName, listener);\n    return () => {\n      eventTarget.removeEventListener(eventName, listener);\n    };\n  }, [ref?.current, callback, type]);\n};\n"], "mappings": "aACA,OAAOA,GAAS,YAAAC,GAAU,UAAAC,OAAc,QACxC,OAAS,iBAAAC,GAAe,iBAAAC,GAAe,kBAAAC,GAAgB,0BAAAC,OAA8B,qBACrF,OAAS,cAAAC,OAAkB,kBCH3B,OAAOC,MAAW,QASlB,IAAMC,EAAuB,SAASD,EAAM,OAAO,GAAK,GAMlDE,EAAyB,CAC7B,UAAW,QACX,UAAW,QACX,QAAS,MACT,YAAa,cACb,QAAS,UACT,YAAa,cACb,SAAU,WACV,aAAc,cAChB,EAKaC,EAASC,GAAsCA,GAAK,KAGpDC,GAAU,CAAyBC,EAAaC,IACvDJ,EAAMI,CAAC,EAAU,GACdD,KAAKC,EAGRC,GAAeC,GAAmBA,EAAO,QAAQ,SAAWC,GAAU,IAAIA,EAAM,YAAY,CAAC,EAAE,EAExFC,GAAmB,CAACC,EAAkBC,IAAuC,CACxF,GAAI,GAACZ,GAAwB,OAAOY,GAAc,WAAa,CAACA,GAChE,IAAIR,GAAQO,EAAUV,CAAsB,EAAG,OAAOA,EAAuBU,CAAQ,EACrF,GAAI,OAAOC,GAAc,YACzB,MAAI,QAAQ,KAAKD,CAAQ,EAAUJ,GAAYI,CAAQ,EAChDA,EACT,EAGO,IAAME,GAAoB,CAACC,EAAgBC,IAC5C,CAACC,GAAwB,OAAOF,GAAc,UAAkB,GAC7DA,EAGIG,EAAgB,CAACC,EAA2C,CAAC,IAAM,CAC9E,GAAM,CAAE,IAAAC,EAAK,GAAGC,CAAU,EAAIF,EAC9B,OAAO,OAAO,QAAQE,CAAS,EAAE,OAAgC,CAACC,EAAkB,CAACC,EAAUR,CAAS,IAAM,CAC5G,IAAMS,EAAWC,GAAiBF,EAAUR,CAAS,EAGrD,GAAI,CAACS,EACH,OAAOF,EAGT,IAAMI,EAAYZ,GAAkBC,EAAWQ,CAAQ,EACvD,OAAAD,EAAiBE,CAAQ,EAAIE,EACtBJ,CACT,EAAG,CAAC,CAAC,CACP,ECnEA,UAAYK,MAAW,QAQvB,SAASC,EAAUC,EAAqBC,EAA2C,CACjF,GAAI,OAAOD,GAAQ,WACjB,OAAOA,EAAIC,CAAK,EACPD,GAAQ,OAChBA,EAAkC,QAAUC,EAEjD,CAMA,SAASC,MAAkBC,EAA8C,CACvE,OAAQC,GAAS,CACf,IAAIC,EAAa,GACXC,EAAWH,EAAK,IAAKH,GAAQ,CACjC,IAAMO,EAAUR,EAAOC,EAAKI,CAAI,EAChC,MAAI,CAACC,GAAc,OAAOE,GAAW,aACnCF,EAAa,IAERE,CACT,CAAC,EAMD,GAAIF,EACF,MAAO,IAAM,CACX,QAASG,EAAI,EAAGA,EAAIF,EAAS,OAAQE,IAAK,CACxC,IAAMD,EAAUD,EAASE,CAAC,EACtB,OAAOD,GAAW,WACpBA,EAAQ,EAERR,EAAOI,EAAKK,CAAC,EAAG,IAAI,CAExB,CACF,CAEJ,CACF,CAMA,SAASC,KAAsBN,EAA8C,CAE3E,OAAa,cAAYD,GAAY,GAAGC,CAAI,EAAGA,CAAI,CACrD,CCzDA,OAAS,aAAAO,OAAiB,QAE1B,IAAMC,GAAiB,OAAO,UAAU,eAOlCC,GAAe,CAACC,EAAWC,IAAuB,CACtD,GAAI,OAAO,GAAGD,EAAMC,CAAI,EACtB,MAAO,GAGT,GAAI,OAAOD,GAAS,UAAYA,IAAS,MAAQ,OAAOC,GAAS,UAAYA,IAAS,KACpF,MAAO,GAGT,GAAI,MAAM,QAAQD,CAAI,EAEpB,MAAI,CAAC,MAAM,QAAQC,CAAI,GAAKD,EAAK,SAAWC,EAAK,OAAe,GAEzDD,EAAK,KAAK,CAACE,EAAMC,IAAMF,EAAKE,CAAC,IAAMD,CAAI,EAGhD,IAAME,EAAQ,OAAO,KAAKJ,CAAI,EACxBK,EAAQ,OAAO,KAAKJ,CAAI,EAE9B,GAAIG,EAAM,SAAWC,EAAM,OACzB,MAAO,GAIT,QAASF,EAAI,EAAGA,EAAIC,EAAM,OAAQD,IAChC,GAAI,CAACL,GAAe,KAAKG,EAAMG,EAAMD,CAAC,CAAC,GAAK,CAAC,OAAO,GAAGH,EAAKI,EAAMD,CAAC,CAAC,EAAGF,EAAKG,EAAMD,CAAC,CAAC,CAAC,EACnF,MAAO,GAIX,MAAO,EACT,EAEaG,EAAoB,CAACC,EAAUC,EAAQC,IAC3C,CAACV,GAAaS,EAAGD,EAAIE,CAAC,CAAC,EAG1BC,GAAqB,CAACH,EAAUC,EAAQC,IAAc,CAC1DF,EAAIE,CAAC,EAAID,CACX,EAEaG,GAAsB,CACjCC,EACAC,EACAC,EACAC,EAAcL,GACdM,EAAaV,IAENT,GAAU,IAAM,CACrB,IAAMU,EAAMO,GAAA,YAAAA,EAAK,QACZP,GACAS,EAAWT,EAAKM,EAAWD,CAAQ,GACxCG,EAAYR,EAAKM,EAAWD,CAAQ,CACtC,EAAG,CAACE,GAAA,YAAAA,EAAK,QAASD,CAAS,CAAC,EAGvBI,EAAQN,GCjEf,IAAMO,GAAsB,IAAM,CAChC,GAAI,CAEF,MAAO,OACT,MAAQ,CAAC,CACT,MAAO,SACT,EAEMC,GAAyBD,GAAoB,EAEtCE,EAAmB,IAAMD,GCVtC,OAAgB,aAAAE,OAAiB,QAG1B,IAAMC,EAAyB,CAKpCC,EACAC,EAEAC,IAEOJ,GAAU,IAAM,CACrB,IAAMK,EAAcF,GAAA,YAAAA,EAAK,QACzB,GAAI,CAACE,GAAe,CAACD,EAAU,OAG/B,IAAME,EAAYJ,EACZK,EAAWH,EAEjB,OAAAC,EAAY,iBAAiBC,EAAWC,CAAQ,EACzC,IAAM,CACXF,EAAY,oBAAoBC,EAAWC,CAAQ,CACrD,CACF,EAAG,CAACJ,GAAA,YAAAA,EAAK,QAASC,EAAUF,CAAI,CAAC,ELTnC,IAAMM,GAAoBC,EAAM,WAAmD,CAAC,CAAE,SAAAC,EAAU,GAAGC,CAAM,EAAGC,IACnGH,EAAM,cACX,aACA,CACE,yBAA0B,GAC1B,GAAGI,EAAcF,CAAK,EACtB,IAAAC,CACF,EACAF,CACF,CACD,EAEKI,GAAY,CAChBF,EAEAD,IACG,CACH,GAAM,CACJ,QAAAI,EACA,UAAAC,EACA,iBAAAC,EACA,UAAAC,EACA,YAAAC,EACA,aAAAC,EACA,iBAAAC,EACA,WAAAC,EACA,iBAAAC,EACA,eAAAC,EACA,aAAAC,EACA,SAAAC,EACA,UAAAC,EACA,OAAAC,EACA,UAAAC,EACA,aAAAC,EACA,QAAAC,EACA,UAAAC,EACA,SAAAC,EACA,UAAAC,EACA,UAAAC,EACA,QAAAC,EACA,QAAAC,EACA,iBAAAC,EACA,gBAAAC,EACA,SAAAC,EACA,OAAAC,EACA,OAAAC,EACA,WAAAC,EACA,cAAAC,EACA,YAAAC,EACA,WAAAC,EACA,kBAAAC,EACA,eAAAC,EACA,WAAAC,EACA,GAAGC,CACL,EAAIvC,EACJ,OAAAwC,EAAoB,gBAAiBP,EAAehC,CAAG,EACvDuC,EAAoB,WAAYX,EAAU5B,CAAG,EAC7CuC,EAAoB,oBAAqBJ,EAAmBnC,CAAG,EAC/DuC,EAAoB,aAAcF,EAAYrC,CAAG,EACjDuC,EAAoB,aAAcL,EAAYlC,CAAG,EACjDuC,EAAoB,SAAUV,EAAQ7B,CAAG,EACzCuC,EAAoB,aAAcR,EAAY/B,CAAG,EACjDuC,EAAoB,iBAAkBH,EAAgBpC,CAAG,EACzDuC,EACE,SACAT,EACA9B,EACA,CAACwC,EAA4BC,IAAwB,CAC/CA,GAAa,OACbA,EACFD,EAAS,MAAM,EAEfA,EAAS,KAAK,EAElB,EACA,CAACA,EAAUE,EAAOC,IACZH,EAAS,aAAa,UAAU,GAAK,CAACA,EAAS,UAC1C,GAEFI,EAAkBJ,EAAUE,EAAOC,CAAQ,CAEtD,EACAJ,EAAoB,cAAeN,EAAajC,EAAK,CAACwC,EAA4BK,IAA4B,CACxGA,GAAkB,OACtBL,EAAS,YAAcK,EACzB,CAAC,EACDC,EAAmE,QAAS9C,EAAKG,CAAO,EACxF2C,EAAmE,UAAW9C,EAAKI,CAAS,EAC5F0C,EAAmE,iBAAkB9C,EAAKK,CAAgB,EAC1GyC,EAAmE,UAAW9C,EAAKM,CAAS,EAC5FwC,EAAmE,YAAa9C,EAAKO,CAAW,EAChGuC,EAAmE,aAAc9C,EAAKQ,CAAY,EAClGsC,EAAmE,iBAAkB9C,EAAKS,CAAgB,EAC1GqC,EAAmE,WAAY9C,EAAKU,CAAU,EAC9FoC,EAAmE,iBAAkB9C,EAAKW,CAAgB,EAC1GmC,EAAmE,eAAgB9C,EAAKY,CAAc,EACtGkC,EAAmE,aAAc9C,EAAKa,CAAY,EAClGiC,EAAmE,SAAU9C,EAAKc,CAAQ,EAC1FgC,EAAmE,UAAW9C,EAAKe,CAAS,EAC5F+B,EAAmE,OAAQ9C,EAAKgB,CAAM,EACtF8B,EAAmE,UAAW9C,EAAKiB,CAAS,EAC5F6B,EAAmE,aAAc9C,EAAKkB,CAAY,EAClG4B,EAAmE,QAAS9C,EAAKmB,CAAO,EACxF2B,EAAmE,UAAW9C,EAAKoB,CAAS,EAC5F0B,EAAmE,SAAU9C,EAAKqB,CAAQ,EAC1FyB,EAAmE,UAAW9C,EAAKsB,CAAS,EAC5FwB,EAAmE,UAAW9C,EAAKuB,CAAS,EAC5FuB,EAAmE,QAAS9C,EAAKwB,CAAO,EACxFsB,EAAmE,QAAS9C,EAAKyB,CAAO,EACxFqB,EAAmE,iBAAkB9C,EAAK0B,CAAgB,EAC1GoB,EAAmE,gBAAiB9C,EAAK2B,CAAe,EACjG,CAACW,CAAc,CACxB,EAEaS,GAAwBC,EAAiB,EACzCC,GAAqB,mBAE5BC,GAAYrD,EAAM,WAGtB,CAACE,EAAOC,IAAQ,CAxIlB,IAAAmD,EAyIE,IAAMC,EAAiBC,GAAyB,IAAI,EAC9CC,EAAYC,EAAgBH,EAAgBpD,CAAG,EAC/C,CAACsC,CAAc,EAAIpC,GAAUkD,EAAgBrD,CAAK,EAClD,CAACyD,CAAc,EAAIC,IAASN,EAAApD,EAAM,iBAAN,KAAAoD,EAAwBO,GAAuB,CAAC,EAElF,OACE7D,EAAA,cAACD,GAAA,CAEC,IAAK0D,EACL,sBAAuBvD,EAAM,sBAC7B,mBAAoBkD,GACpB,sBAAuBF,GACvB,eAAgBS,EACf,GAAGlB,EACN,CAEJ,CAAC,EAEMqB,GAAQT", "names": ["React", "useState", "useRef", "MaxResolution", "MinResolution", "RenditionOrder", "generatePlayerInitTime", "MediaError", "React", "IS_REACT_19_OR_NEWER", "ReactPropToAttrNameMap", "isNil", "x", "isKeyOf", "k", "o", "toKebabCase", "string", "match", "toNativeAttrName", "propName", "propValue", "toNativeAttrValue", "propValue", "_propName", "IS_REACT_19_OR_NEWER", "toNativeProps", "props", "ref", "restProps", "transformedProps", "propName", "attrName", "toNativeAttrName", "attrValue", "React", "setRef", "ref", "value", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "cleanup", "i", "useComposedRefs", "useEffect", "hasOwnProperty", "shallowEqual", "objA", "objB", "vVal", "i", "keysA", "keysB", "defaultHasChanged", "obj", "v", "k", "defaultUpdateValue", "useObjectPropEffect", "propName", "propValue", "ref", "updateValue", "has<PERSON><PERSON>ed", "useObjectPropEffect_default", "getEnvPlayerVersion", "player_version", "getPlayerVersion", "useEffect", "useEventCallbackEffect", "type", "ref", "callback", "eventTarget", "eventName", "listener", "MuxPlayerInternal", "React", "children", "props", "ref", "toNativeProps", "usePlayer", "onAbort", "onCanPlay", "onCanPlayThrough", "onEmptied", "onLoadStart", "onLoadedData", "onLoadedMetadata", "onProgress", "onDurationChange", "onVolumeChange", "onRateChange", "onResize", "onWaiting", "onPlay", "onPlaying", "onTimeUpdate", "onPause", "onSeeking", "onSeeked", "onStalled", "onSuspend", "onEnded", "onError", "onCuePointChange", "onChapterChange", "metadata", "tokens", "paused", "playbackId", "playbackRates", "currentTime", "themeProps", "extraSourceParams", "castCustomData", "_hlsConfig", "remainingProps", "useObjectPropEffect_default", "playerEl", "pausedVal", "value", "propName", "defaultHasChanged", "currentTimeVal", "useEventCallbackEffect", "playerSoftwareVersion", "getPlayerVersion", "playerSoftwareName", "MuxPlayer", "_a", "innerPlayerRef", "useRef", "playerRef", "useComposedRefs", "playerInitTime", "useState", "generatePlayerInitTime", "index_default"]}