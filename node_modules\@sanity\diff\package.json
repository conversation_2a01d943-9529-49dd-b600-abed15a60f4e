{"name": "@sanity/diff", "version": "4.1.1", "description": "Generates diffs between documents and primitive types", "keywords": ["sanity", "cms", "headless", "realtime", "content", "diff"], "homepage": "https://www.sanity.io/", "bugs": {"url": "https://github.com/sanity-io/sanity/issues"}, "repository": {"type": "git", "url": "git+https://github.com/sanity-io/sanity.git", "directory": "packages/@sanity/diff"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "type": "commonjs", "exports": {".": {"source": "./src/index.ts", "import": "./lib/index.mjs", "require": "./lib/index.js", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "files": ["lib"], "dependencies": {"@sanity/diff-match-patch": "^3.2.0"}, "devDependencies": {"rimraf": "^5.0.10", "@repo/eslint-config": "4.1.1", "@repo/package.config": "4.1.1"}, "engines": {"node": ">=20.19"}, "publishConfig": {"access": "public"}, "scripts": {"build": "pkg-utils build --strict --check --clean", "check:types": "(cd ../../.. && tsc --project packages/@sanity/diff/tsconfig.lib.json --erasableSyntaxOnly)", "clean": "rimraf lib coverage", "lint": "eslint .", "watch": "pkg-utils watch"}}