// Enhanced Service Worker for BAKASANA PWA
// Advanced caching strategies, offline functionality, and push notifications

const CACHE_VERSION = '2.0.0';
const CACHE_NAME = `bakasana-v${CACHE_VERSION}`;
const STATIC_CACHE = `bakasana-static-v${CACHE_VERSION}`;
const DYNAMIC_CACHE = `bakasana-dynamic-v${CACHE_VERSION}`;
const IMAGE_CACHE = `bakasana-images-v${CACHE_VERSION}`;
const API_CACHE = `bakasana-api-v${CACHE_VERSION}`;

// Critical files for offline functionality
const CRITICAL_FILES = [
  '/',
  '/offline',
  '/manifest.json',
  '/favicon.ico',
  '/apple-touch-icon.png',
  // Core retreat pages
  '/retreaty',
  '/o-mnie',
  '/kontakt',
  '/galeria',
  '/blog',
  // Offline fallback page
  '/offline.html'
];

// API endpoints to cache for offline access
const CACHEABLE_APIS = [
  '/api/retreats',
  '/api/testimonials',
  '/api/blog',
  '/api/destinations'
];

// Install event - cache critical resources
self.addEventListener('install', event => {
  console.log('[SW] Installing enhanced service worker v' + CACHE_VERSION);

  event.waitUntil(
    Promise.all([
      // Cache critical files
      caches.open(STATIC_CACHE).then(cache => {
        console.log('[SW] Caching critical files');
        return cache.addAll(CRITICAL_FILES);
      }),
      
      // Initialize other caches
      caches.open(DYNAMIC_CACHE),
      caches.open(IMAGE_CACHE),
      caches.open(API_CACHE)
    ]).then(() => {
      console.log('[SW] Enhanced service worker installed successfully');
      return self.skipWaiting();
    }).catch(error => {
      console.error('[SW] Installation failed:', error);
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('[SW] Activating enhanced service worker');

  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName.startsWith('bakasana-') && 
                !cacheName.includes(CACHE_VERSION)) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Take control of all clients
      self.clients.claim()
    ]).then(() => {
      console.log('[SW] Enhanced service worker activated');
    })
  );
});

// Fetch event - intelligent caching strategies
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') return;

  // Skip external requests (except fonts and CDNs)
  if (url.origin !== location.origin && 
      !url.hostname.includes('fonts.googleapis.com') &&
      !url.hostname.includes('fonts.gstatic.com') &&
      !url.hostname.includes('cdn.')) {
    return;
  }

  event.respondWith(handleRequest(request));
});

// Intelligent request handling
async function handleRequest(request) {
  const url = new URL(request.url);

  try {
    // Strategy 1: Cache First for static assets
    if (isStaticAsset(request)) {
      return await cacheFirst(request, STATIC_CACHE);
    }

    // Strategy 2: Cache First for images
    if (isImage(request)) {
      return await cacheFirst(request, IMAGE_CACHE);
    }

    // Strategy 3: Network First for API calls
    if (isAPIRequest(request)) {
      return await networkFirst(request, API_CACHE);
    }

    // Strategy 4: Stale While Revalidate for pages
    if (isPageRequest(request)) {
      return await staleWhileRevalidate(request, DYNAMIC_CACHE);
    }

    // Default: Network with cache fallback
    return await networkWithCacheFallback(request);

  } catch (error) {
    console.error('[SW] Request failed:', error);
    return await handleOfflineRequest(request);
  }
}

// Cache First strategy
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('[SW] Cache first failed:', error);
    throw error;
  }
}

// Network First strategy
async function networkFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('[SW] Network failed, trying cache');
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// Stale While Revalidate strategy
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);

  // Update cache in background
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(error => {
    console.log('[SW] Background update failed:', error);
  });

  // Return cached version immediately if available
  if (cachedResponse) {
    return cachedResponse;
  }

  // Otherwise wait for network
  return await fetchPromise;
}

// Network with cache fallback
async function networkWithCacheFallback(request) {
  try {
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// Handle offline requests
async function handleOfflineRequest(request) {
  const url = new URL(request.url);

  // For navigation requests, return offline page
  if (request.mode === 'navigate') {
    const offlinePage = await caches.match('/offline.html');
    return offlinePage || new Response('Offline', { 
      status: 503,
      statusText: 'Service Unavailable'
    });
  }

  // For images, return placeholder
  if (isImage(request)) {
    return new Response(
      '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="150" viewBox="0 0 200 150"><rect width="200" height="150" fill="#f0f0f0"/><text x="100" y="75" text-anchor="middle" fill="#999">Offline</text></svg>',
      { headers: { 'Content-Type': 'image/svg+xml' } }
    );
  }

  // For API requests, return cached data or error
  if (isAPIRequest(request)) {
    const cachedResponse = await caches.match(request);
    return cachedResponse || new Response(
      JSON.stringify({ error: 'Offline', cached: false }),
      { 
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  return new Response('Offline', { status: 503 });
}

// Helper functions
function isStaticAsset(request) {
  return /\.(js|css|woff2|woff|ttf|eot)$/i.test(request.url) ||
         request.url.includes('/_next/static/');
}

function isImage(request) {
  return /\.(png|jpg|jpeg|gif|webp|svg|ico)$/i.test(request.url);
}

function isAPIRequest(request) {
  return request.url.includes('/api/') ||
         CACHEABLE_APIS.some(pattern => request.url.includes(pattern));
}

function isPageRequest(request) {
  return request.mode === 'navigate' ||
         (request.method === 'GET' && 
          request.headers.get('accept')?.includes('text/html'));
}

// Push notification handling
self.addEventListener('push', event => {
  console.log('[SW] Push notification received');

  if (!event.data) return;

  const data = event.data.json();
  const options = {
    body: data.body || 'Nowe informacje o retreatach!',
    icon: '/apple-touch-icon.png',
    badge: '/favicon-32x32.png',
    image: data.image || '/images/og-image.jpg',
    tag: data.tag || 'bakasana-notification',
    data: data.data || {},
    actions: [
      {
        action: 'view',
        title: 'Zobacz szczegóły',
        icon: '/favicon-16x16.png'
      },
      {
        action: 'dismiss',
        title: 'Zamknij',
        icon: '/favicon-16x16.png'
      }
    ],
    requireInteraction: data.requireInteraction || false,
    silent: data.silent || false
  };

  event.waitUntil(
    self.registration.showNotification(data.title || 'BAKASANA', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  console.log('[SW] Notification clicked:', event.notification.tag);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  const urlToOpen = event.notification.data?.url || '/';

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then(clientList => {
        // Check if there's already a window open
        for (const client of clientList) {
          if (client.url === urlToOpen && 'focus' in client) {
            return client.focus();
          }
        }
        
        // Open new window
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen);
        }
      })
  );
});

// Background sync for offline form submissions
self.addEventListener('sync', event => {
  console.log('[SW] Background sync:', event.tag);

  if (event.tag === 'booking-sync') {
    event.waitUntil(syncBookingData());
  } else if (event.tag === 'contact-sync') {
    event.waitUntil(syncContactData());
  }
});

// Sync booking data when back online
async function syncBookingData() {
  try {
    const bookingData = await getStoredData('pending-bookings');
    if (bookingData.length > 0) {
      for (const booking of bookingData) {
        await fetch('/api/booking', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(booking)
        });
      }
      await clearStoredData('pending-bookings');
      console.log('[SW] Booking data synced successfully');
    }
  } catch (error) {
    console.error('[SW] Booking sync failed:', error);
  }
}

// Sync contact data when back online
async function syncContactData() {
  try {
    const contactData = await getStoredData('pending-contacts');
    if (contactData.length > 0) {
      for (const contact of contactData) {
        await fetch('/api/contact', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(contact)
        });
      }
      await clearStoredData('pending-contacts');
      console.log('[SW] Contact data synced successfully');
    }
  } catch (error) {
    console.error('[SW] Contact sync failed:', error);
  }
}

// IndexedDB helpers for offline data storage
async function getStoredData(storeName) {
  // Simplified - in real implementation use IndexedDB
  const stored = localStorage.getItem(storeName);
  return stored ? JSON.parse(stored) : [];
}

async function clearStoredData(storeName) {
  localStorage.removeItem(storeName);
}

// Message handling for communication with main thread
self.addEventListener('message', event => {
  console.log('[SW] Message received:', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

console.log('[SW] Enhanced service worker loaded successfully');
