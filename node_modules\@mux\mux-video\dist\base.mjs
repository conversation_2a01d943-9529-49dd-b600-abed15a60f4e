var C=s=>{throw TypeError(s)};var S=(s,a,t)=>a.has(s)||C("Cannot "+t);var n=(s,a,t)=>(S(s,a,"read from private field"),t?t.call(s):a.get(s)),u=(s,a,t)=>a.has(s)?C("Cannot add the same private member more than once"):a instanceof WeakSet?a.add(s):a.set(s,t),o=(s,a,t,i)=>(S(s,a,"write to private field"),i?i.call(s,t):a.set(s,t),t),M=(s,a,t)=>(S(s,a,"access private method"),t);import{initialize as G,teardown as V,generatePlayerInitTime as F,PlaybackTypes as R,toMuxVideoURL as U,MediaError as At,getError as W,CmcdTypeValues as D,addCuePoints as w,getCuePoints as H,getActiveCuePoint as $,addChapters as j,getActiveChapter as q,getMetadata as z,getStartDate as X,getCurrentPdt as J,getStreamType as Z,getTargetLiveWindow as Q,getLiveEdgeStart as tt,getSeekable as et,getEnded as it,getChapters as rt,toPlaybackIdFromSrc as st,toPlaybackIdParts as nt}from"@mux/playback-core";var Y=()=>{try{return"0.26.1"}catch{}return"UNKNOWN"},B=Y(),P=()=>B;import{CustomVideoElement as I,Events as bt}from"custom-media-element";var k=`
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" part="logo" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2" viewBox="0 0 1600 500"><g fill="#fff"><path d="M994.287 93.486c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31m0-93.486c-34.509 0-62.484 27.976-62.484 62.486v187.511c0 68.943-56.09 125.033-125.032 125.033s-125.03-56.09-125.03-125.033V62.486C681.741 27.976 653.765 0 619.256 0s-62.484 27.976-62.484 62.486v187.511C556.772 387.85 668.921 500 806.771 500c137.851 0 250.001-112.15 250.001-250.003V62.486c0-34.51-27.976-62.486-62.485-62.486M1537.51 468.511c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31m-275.883-218.509-143.33 143.329c-24.402 24.402-24.402 63.966 0 88.368 24.402 24.402 63.967 24.402 88.369 0l143.33-143.329 143.328 143.329c24.402 24.4 63.967 24.402 88.369 0 24.403-24.402 24.403-63.966.001-88.368l-143.33-143.329.001-.004 143.329-143.329c24.402-24.402 24.402-63.965 0-88.367s-63.967-24.402-88.369 0L1349.996 161.63 1206.667 18.302c-24.402-24.401-63.967-24.402-88.369 0s-24.402 63.965 0 88.367l143.329 143.329v.004ZM437.511 468.521c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31M461.426 4.759C438.078-4.913 411.2.432 393.33 18.303L249.999 161.632 106.669 18.303C88.798.432 61.922-4.913 38.573 4.759 15.224 14.43-.001 37.214-.001 62.488v375.026c0 34.51 27.977 62.486 62.487 62.486 34.51 0 62.486-27.976 62.486-62.486V213.341l80.843 80.844c24.404 24.402 63.965 24.402 88.369 0l80.843-80.844v224.173c0 34.51 27.976 62.486 62.486 62.486s62.486-27.976 62.486-62.486V62.488c0-25.274-15.224-48.058-38.573-57.729" style="fill-rule:nonzero"/></g></svg>`;var e={BEACON_COLLECTION_DOMAIN:"beacon-collection-domain",CUSTOM_DOMAIN:"custom-domain",DEBUG:"debug",DISABLE_TRACKING:"disable-tracking",DISABLE_COOKIES:"disable-cookies",DRM_TOKEN:"drm-token",PLAYBACK_TOKEN:"playback-token",ENV_KEY:"env-key",MAX_RESOLUTION:"max-resolution",MIN_RESOLUTION:"min-resolution",RENDITION_ORDER:"rendition-order",PROGRAM_START_TIME:"program-start-time",PROGRAM_END_TIME:"program-end-time",ASSET_START_TIME:"asset-start-time",ASSET_END_TIME:"asset-end-time",METADATA_URL:"metadata-url",PLAYBACK_ID:"playback-id",PLAYER_SOFTWARE_NAME:"player-software-name",PLAYER_SOFTWARE_VERSION:"player-software-version",PLAYER_INIT_TIME:"player-init-time",PREFER_CMCD:"prefer-cmcd",PREFER_PLAYBACK:"prefer-playback",START_TIME:"start-time",STREAM_TYPE:"stream-type",TARGET_LIVE_WINDOW:"target-live-window",LIVE_EDGE_OFFSET:"live-edge-offset",TYPE:"type",LOGO:"logo"},at=Object.values(e),v=P(),x="mux-video",l,f,c,A,b,T,p,_,O,g,m,y,K=class extends I{constructor(){super();u(this,m);u(this,l);u(this,f);u(this,c);u(this,A,{});u(this,b,{});u(this,T);u(this,p);u(this,_);u(this,O);u(this,g,"");o(this,c,F()),this.nativeEl.addEventListener("muxmetadata",t=>{var d;let i=z(this.nativeEl),r=(d=this.metadata)!=null?d:{};this.metadata={...i,...r},(i==null?void 0:i["com.mux.video.branding"])==="mux-free-plan"&&(o(this,g,"default"),this.updateLogo())})}static get NAME(){return x}static get VERSION(){return v}static get observedAttributes(){var t;return[...at,...(t=I.observedAttributes)!=null?t:[]]}static getLogoHTML(t){return!t||t==="false"?"":t==="default"?k:`<img part="logo" src="${t}" />`}static getTemplateHTML(t={}){var i;return`
      ${I.getTemplateHTML(t)}
      <style>
        :host {
          position: relative;
        }
        slot[name="logo"] {
          display: flex;
          justify-content: end;
          position: absolute;
          top: 1rem;
          right: 1rem;
          opacity: 0;
          transition: opacity 0.25s ease-in-out;
          z-index: 1;
        }
        slot[name="logo"]:has([part="logo"]) {
          opacity: 1;
        }
        slot[name="logo"] [part="logo"] {
          width: 5rem;
          pointer-events: none;
          user-select: none;
        }
      </style>
      <slot name="logo">
        ${this.getLogoHTML((i=t[e.LOGO])!=null?i:"")}
      </slot>
    `}get preferCmcd(){var t;return(t=this.getAttribute(e.PREFER_CMCD))!=null?t:void 0}set preferCmcd(t){t!==this.preferCmcd&&(t?D.includes(t)?this.setAttribute(e.PREFER_CMCD,t):console.warn(`Invalid value for preferCmcd. Must be one of ${D.join()}`):this.removeAttribute(e.PREFER_CMCD))}get playerInitTime(){return this.hasAttribute(e.PLAYER_INIT_TIME)?+this.getAttribute(e.PLAYER_INIT_TIME):n(this,c)}set playerInitTime(t){t!=this.playerInitTime&&(t==null?this.removeAttribute(e.PLAYER_INIT_TIME):this.setAttribute(e.PLAYER_INIT_TIME,`${+t}`))}get playerSoftwareName(){var t;return(t=n(this,_))!=null?t:x}set playerSoftwareName(t){o(this,_,t)}get playerSoftwareVersion(){var t;return(t=n(this,p))!=null?t:v}set playerSoftwareVersion(t){o(this,p,t)}get _hls(){var t;return(t=n(this,l))==null?void 0:t.engine}get mux(){var t;return(t=this.nativeEl)==null?void 0:t.mux}get error(){var t;return(t=W(this.nativeEl))!=null?t:null}get errorTranslator(){return n(this,O)}set errorTranslator(t){o(this,O,t)}get src(){return this.getAttribute("src")}set src(t){t!==this.src&&(t==null?this.removeAttribute("src"):this.setAttribute("src",t))}get type(){var t;return(t=this.getAttribute(e.TYPE))!=null?t:void 0}set type(t){t!==this.type&&(t?this.setAttribute(e.TYPE,t):this.removeAttribute(e.TYPE))}get preload(){let t=this.getAttribute("preload");return t===""?"auto":["none","metadata","auto"].includes(t)?t:super.preload}set preload(t){t!=this.getAttribute("preload")&&(["","none","metadata","auto"].includes(t)?this.setAttribute("preload",t):this.removeAttribute("preload"))}get debug(){return this.getAttribute(e.DEBUG)!=null}set debug(t){t!==this.debug&&(t?this.setAttribute(e.DEBUG,""):this.removeAttribute(e.DEBUG))}get disableTracking(){return this.hasAttribute(e.DISABLE_TRACKING)}set disableTracking(t){t!==this.disableTracking&&this.toggleAttribute(e.DISABLE_TRACKING,!!t)}get disableCookies(){return this.hasAttribute(e.DISABLE_COOKIES)}set disableCookies(t){t!==this.disableCookies&&(t?this.setAttribute(e.DISABLE_COOKIES,""):this.removeAttribute(e.DISABLE_COOKIES))}get startTime(){let t=this.getAttribute(e.START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set startTime(t){t!==this.startTime&&(t==null?this.removeAttribute(e.START_TIME):this.setAttribute(e.START_TIME,`${t}`))}get playbackId(){var t;return this.hasAttribute(e.PLAYBACK_ID)?this.getAttribute(e.PLAYBACK_ID):(t=st(this.src))!=null?t:void 0}set playbackId(t){t!==this.playbackId&&(t?this.setAttribute(e.PLAYBACK_ID,t):this.removeAttribute(e.PLAYBACK_ID))}get maxResolution(){var t;return(t=this.getAttribute(e.MAX_RESOLUTION))!=null?t:void 0}set maxResolution(t){t!==this.maxResolution&&(t?this.setAttribute(e.MAX_RESOLUTION,t):this.removeAttribute(e.MAX_RESOLUTION))}get minResolution(){var t;return(t=this.getAttribute(e.MIN_RESOLUTION))!=null?t:void 0}set minResolution(t){t!==this.minResolution&&(t?this.setAttribute(e.MIN_RESOLUTION,t):this.removeAttribute(e.MIN_RESOLUTION))}get renditionOrder(){var t;return(t=this.getAttribute(e.RENDITION_ORDER))!=null?t:void 0}set renditionOrder(t){t!==this.renditionOrder&&(t?this.setAttribute(e.RENDITION_ORDER,t):this.removeAttribute(e.RENDITION_ORDER))}get programStartTime(){let t=this.getAttribute(e.PROGRAM_START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set programStartTime(t){t==null?this.removeAttribute(e.PROGRAM_START_TIME):this.setAttribute(e.PROGRAM_START_TIME,`${t}`)}get programEndTime(){let t=this.getAttribute(e.PROGRAM_END_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set programEndTime(t){t==null?this.removeAttribute(e.PROGRAM_END_TIME):this.setAttribute(e.PROGRAM_END_TIME,`${t}`)}get assetStartTime(){let t=this.getAttribute(e.ASSET_START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set assetStartTime(t){t==null?this.removeAttribute(e.ASSET_START_TIME):this.setAttribute(e.ASSET_START_TIME,`${t}`)}get assetEndTime(){let t=this.getAttribute(e.ASSET_END_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set assetEndTime(t){t==null?this.removeAttribute(e.ASSET_END_TIME):this.setAttribute(e.ASSET_END_TIME,`${t}`)}get customDomain(){var t;return(t=this.getAttribute(e.CUSTOM_DOMAIN))!=null?t:void 0}set customDomain(t){t!==this.customDomain&&(t?this.setAttribute(e.CUSTOM_DOMAIN,t):this.removeAttribute(e.CUSTOM_DOMAIN))}get drmToken(){var t;return(t=this.getAttribute(e.DRM_TOKEN))!=null?t:void 0}set drmToken(t){t!==this.drmToken&&(t?this.setAttribute(e.DRM_TOKEN,t):this.removeAttribute(e.DRM_TOKEN))}get playbackToken(){var t,i,r,d;if(this.hasAttribute(e.PLAYBACK_TOKEN))return(t=this.getAttribute(e.PLAYBACK_TOKEN))!=null?t:void 0;if(this.hasAttribute(e.PLAYBACK_ID)){let[,E]=nt((i=this.playbackId)!=null?i:"");return(r=new URLSearchParams(E).get("token"))!=null?r:void 0}if(this.src)return(d=new URLSearchParams(this.src).get("token"))!=null?d:void 0}set playbackToken(t){t!==this.playbackToken&&(t?this.setAttribute(e.PLAYBACK_TOKEN,t):this.removeAttribute(e.PLAYBACK_TOKEN))}get tokens(){let t=this.getAttribute(e.PLAYBACK_TOKEN),i=this.getAttribute(e.DRM_TOKEN);return{...n(this,b),...t!=null?{playback:t}:{},...i!=null?{drm:i}:{}}}set tokens(t){o(this,b,t!=null?t:{})}get ended(){return it(this.nativeEl,this._hls)}get envKey(){var t;return(t=this.getAttribute(e.ENV_KEY))!=null?t:void 0}set envKey(t){t!==this.envKey&&(t?this.setAttribute(e.ENV_KEY,t):this.removeAttribute(e.ENV_KEY))}get beaconCollectionDomain(){var t;return(t=this.getAttribute(e.BEACON_COLLECTION_DOMAIN))!=null?t:void 0}set beaconCollectionDomain(t){t!==this.beaconCollectionDomain&&(t?this.setAttribute(e.BEACON_COLLECTION_DOMAIN,t):this.removeAttribute(e.BEACON_COLLECTION_DOMAIN))}get streamType(){var t;return(t=this.getAttribute(e.STREAM_TYPE))!=null?t:Z(this.nativeEl)}set streamType(t){t!==this.streamType&&(t?this.setAttribute(e.STREAM_TYPE,t):this.removeAttribute(e.STREAM_TYPE))}get targetLiveWindow(){return this.hasAttribute(e.TARGET_LIVE_WINDOW)?+this.getAttribute(e.TARGET_LIVE_WINDOW):Q(this.nativeEl)}set targetLiveWindow(t){t!=this.targetLiveWindow&&(t==null?this.removeAttribute(e.TARGET_LIVE_WINDOW):this.setAttribute(e.TARGET_LIVE_WINDOW,`${+t}`))}get liveEdgeStart(){var t,i;if(this.hasAttribute(e.LIVE_EDGE_OFFSET)){let{liveEdgeOffset:r}=this,d=(t=this.nativeEl.seekable.end(0))!=null?t:0,E=(i=this.nativeEl.seekable.start(0))!=null?i:0;return Math.max(E,d-r)}return tt(this.nativeEl)}get liveEdgeOffset(){if(this.hasAttribute(e.LIVE_EDGE_OFFSET))return+this.getAttribute(e.LIVE_EDGE_OFFSET)}set liveEdgeOffset(t){t!=this.liveEdgeOffset&&(t==null?this.removeAttribute(e.LIVE_EDGE_OFFSET):this.setAttribute(e.LIVE_EDGE_OFFSET,`${+t}`))}get seekable(){return et(this.nativeEl)}async addCuePoints(t){return w(this.nativeEl,t)}get activeCuePoint(){return $(this.nativeEl)}get cuePoints(){return H(this.nativeEl)}async addChapters(t){return j(this.nativeEl,t)}get activeChapter(){return q(this.nativeEl)}get chapters(){return rt(this.nativeEl)}getStartDate(){return X(this.nativeEl,this._hls)}get currentPdt(){return J(this.nativeEl,this._hls)}get preferPlayback(){let t=this.getAttribute(e.PREFER_PLAYBACK);if(t===R.MSE||t===R.NATIVE)return t}set preferPlayback(t){t!==this.preferPlayback&&(t===R.MSE||t===R.NATIVE?this.setAttribute(e.PREFER_PLAYBACK,t):this.removeAttribute(e.PREFER_PLAYBACK))}get metadata(){return{...this.getAttributeNames().filter(i=>i.startsWith("metadata-")&&![e.METADATA_URL].includes(i)).reduce((i,r)=>{let d=this.getAttribute(r);return d!=null&&(i[r.replace(/^metadata-/,"").replace(/-/g,"_")]=d),i},{}),...n(this,A)}}set metadata(t){o(this,A,t!=null?t:{}),this.mux&&this.mux.emit("hb",n(this,A))}get _hlsConfig(){return n(this,T)}set _hlsConfig(t){o(this,T,t)}get logo(){var t;return(t=this.getAttribute(e.LOGO))!=null?t:n(this,g)}set logo(t){t?this.setAttribute(e.LOGO,t):this.removeAttribute(e.LOGO)}load(){o(this,l,G(this,this.nativeEl,n(this,l)))}unload(){V(this.nativeEl,n(this,l),this),o(this,l,void 0)}attributeChangedCallback(t,i,r){var E,L;switch(I.observedAttributes.includes(t)&&!["src","autoplay","preload"].includes(t)&&super.attributeChangedCallback(t,i,r),t){case e.PLAYER_SOFTWARE_NAME:this.playerSoftwareName=r!=null?r:void 0;break;case e.PLAYER_SOFTWARE_VERSION:this.playerSoftwareVersion=r!=null?r:void 0;break;case"src":{let h=!!i,N=!!r;!h&&N?M(this,m,y).call(this):h&&!N?this.unload():h&&N&&(this.unload(),M(this,m,y).call(this));break}case"autoplay":if(r===i)break;(E=n(this,l))==null||E.setAutoplay(this.autoplay);break;case"preload":if(r===i)break;(L=n(this,l))==null||L.setPreload(r);break;case e.PLAYBACK_ID:this.src=U(this);break;case e.DEBUG:{let h=this.debug;this.mux&&console.info("Cannot toggle debug mode of mux data after initialization. Make sure you set all metadata to override before setting the src."),this._hls&&(this._hls.config.debug=h);break}case e.METADATA_URL:r&&fetch(r).then(h=>h.json()).then(h=>this.metadata=h).catch(()=>console.error(`Unable to load or parse metadata JSON from metadata-url ${r}!`));break;case e.STREAM_TYPE:(r==null||r!==i)&&this.dispatchEvent(new CustomEvent("streamtypechange",{composed:!0,bubbles:!0}));break;case e.TARGET_LIVE_WINDOW:(r==null||r!==i)&&this.dispatchEvent(new CustomEvent("targetlivewindowchange",{composed:!0,bubbles:!0,detail:this.targetLiveWindow}));break;case e.LOGO:(r==null||r!==i)&&this.updateLogo();break}}updateLogo(){if(!this.shadowRoot)return;let t=this.shadowRoot.querySelector('slot[name="logo"]');if(!t)return;let i=this.constructor.getLogoHTML(n(this,g)||this.logo);t.innerHTML=i}connectedCallback(){var t;(t=super.connectedCallback)==null||t.call(this),this.nativeEl&&this.src&&!n(this,l)&&M(this,m,y).call(this)}disconnectedCallback(){this.unload()}handleEvent(t){t.target===this.nativeEl&&this.dispatchEvent(new CustomEvent(t.type,{composed:!0,detail:t.detail}))}};l=new WeakMap,f=new WeakMap,c=new WeakMap,A=new WeakMap,b=new WeakMap,T=new WeakMap,p=new WeakMap,_=new WeakMap,O=new WeakMap,g=new WeakMap,m=new WeakSet,y=async function(){n(this,f)||(await o(this,f,Promise.resolve()),o(this,f,null),this.load())};export{e as Attributes,bt as Events,At as MediaError,K as MuxVideoBaseElement,F as generatePlayerInitTime,x as playerSoftwareName,v as playerSoftwareVersion};
//# sourceMappingURL=base.mjs.map
