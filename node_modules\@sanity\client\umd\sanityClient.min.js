!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).SanityClient={})}(this,(function(e){"use strict";function t(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}const r=!(typeof navigator>"u")&&"ReactNative"===navigator.product,n={timeout:r?6e4:12e4},o=function(e){const t={...n,..."string"==typeof e?{url:e}:e};if(t.timeout=i(t.timeout),t.query){const{url:e,searchParams:n}=function(e){const t=e.indexOf("?");if(-1===t)return{url:e,searchParams:new URLSearchParams};const n=e.slice(0,t),o=e.slice(t+1);if(!r)return{url:n,searchParams:new URLSearchParams(o)};if("function"!=typeof decodeURIComponent)throw new Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");const i=new URLSearchParams;for(const e of o.split("&")){const[t,r]=e.split("=");t&&i.append(s(t),s(r||""))}return{url:n,searchParams:i}}(t.url);for(const[r,o]of Object.entries(t.query)){if(void 0!==o)if(Array.isArray(o))for(const e of o)n.append(r,e);else n.append(r,o);const s=n.toString();s&&(t.url=`${e}?${s}`)}}return t.method=t.body&&!t.method?"POST":(t.method||"GET").toUpperCase(),t};function s(e){return decodeURIComponent(e.replace(/\+/g," "))}function i(e){if(!1===e||0===e)return!1;if(e.connect||e.socket)return e;const t=Number(e);return isNaN(t)?i(n.timeout):{connect:t,socket:t}}const a=/^https?:\/\//i,c=function(e){if(!a.test(e.url))throw new Error(`"${e.url}" is not a valid URL`)};function u(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}const l=["request","response","progress","error","abort"],h=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];function d(e,t){const r=[],n=h.reduce(((e,t)=>(e[t]=e[t]||[],e)),{processOptions:[o],validateOptions:[c]});function s(e){const r=l.reduce(((e,t)=>(e[t]=function(){const e=Object.create(null);let t=0;return{publish:function(t){for(const r in e)e[r](t)},subscribe:function(r){const n=t++;return e[n]=r,function(){delete e[n]}}}}(),e)),{}),o=(e=>function(t,r,...n){const o="onError"===t;let s=r;for(let r=0;r<e[t].length&&(s=(0,e[t][r])(s,...n),!o||s);r++);return s})(n),s=o("processOptions",e);o("validateOptions",s);const i={options:s,channels:r,applyMiddleware:o};let a;const c=r.request.subscribe((e=>{a=t(e,((t,n)=>((e,t,n)=>{let s=e,i=t;if(!s)try{i=o("onResponse",t,n)}catch(e){i=null,s=e}s=s&&o("onError",s,n),s?r.error.publish(s):i&&r.response.publish(i)})(t,n,e)))}));r.abort.subscribe((()=>{c(),a&&a.abort()}));const u=o("onReturn",r,i);return u===r&&r.request.publish(i),u}return s.use=function(e){if(!e)throw new Error("Tried to add middleware that resolved to falsey value");if("function"==typeof e)throw new Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(e.onReturn&&n.onReturn.length>0)throw new Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return h.forEach((t=>{e[t]&&n[t].push(e[t])})),r.push(e),s},s.clone=()=>d(r,t),e.forEach(s.use),s}var p,f,y=u(function(){if(f)return p;f=1;var e=function(e){return e.replace(/^\s+|\s+$/g,"")};return p=function(t){if(!t)return{};for(var r={},n=e(t).split("\n"),o=0;o<n.length;o++){var s=n[o],i=s.indexOf(":"),a=e(s.slice(0,i)).toLowerCase(),c=e(s.slice(i+1));typeof r[a]>"u"?r[a]=c:(u=r[a],"[object Array]"===Object.prototype.toString.call(u)?r[a].push(c):r[a]=[r[a],c])}var u;return r}}());let g=class{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText="";responseType="";status;statusText;withCredentials;#e;#t;#r;#n={};#o;#s={};#i;open(e,t,r){this.#e=e,this.#t=t,this.#r="",this.readyState=1,this.onreadystatechange?.(),this.#o=void 0}abort(){this.#o&&this.#o.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(e,t){this.#n[e]=t}setInit(e,t=!0){this.#s=e,this.#i=t}send(e){const t="arraybuffer"!==this.responseType,r={...this.#s,method:this.#e,headers:this.#n,body:e};"function"==typeof AbortController&&this.#i&&(this.#o=new AbortController,typeof EventTarget<"u"&&this.#o.signal instanceof EventTarget&&(r.signal=this.#o.signal)),typeof document<"u"&&(r.credentials=this.withCredentials?"include":"omit"),fetch(this.#t,r).then((e=>(e.headers.forEach(((e,t)=>{this.#r+=`${t}: ${e}\r\n`})),this.status=e.status,this.statusText=e.statusText,this.readyState=3,this.onreadystatechange?.(),t?e.text():e.arrayBuffer()))).then((e=>{"string"==typeof e?this.responseText=e:this.response=e,this.readyState=4,this.onreadystatechange?.()})).catch((e=>{"AbortError"!==e.name?this.onerror?.(e):this.onabort?.()}))}};const b="function"==typeof XMLHttpRequest?"xhr":"fetch",v="xhr"===b?XMLHttpRequest:g,m=(e,t)=>{const r=e.options,n=e.applyMiddleware("finalizeOptions",r),o={},s=e.applyMiddleware("interceptRequest",void 0,{adapter:b,context:e});if(s){const e=setTimeout(t,0,null,s);return{abort:()=>clearTimeout(e)}}let i=new v;i instanceof g&&"object"==typeof n.fetch&&i.setInit(n.fetch,n.useAbortSignal??!0);const a=n.headers,c=n.timeout;let u=!1,l=!1,h=!1;if(i.onerror=e=>{f(i instanceof g?e instanceof Error?e:new Error(`Request error while attempting to reach is ${n.url}`,{cause:e}):new Error(`Request error while attempting to reach is ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},i.ontimeout=e=>{f(new Error(`Request timeout while attempting to reach ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},i.onabort=()=>{p(!0),u=!0},i.onreadystatechange=function(){c&&(p(),o.socket=setTimeout((()=>d("ESOCKETTIMEDOUT")),c.socket)),!u&&i&&4===i.readyState&&0!==i.status&&function(){if(!(u||l||h)){if(0===i.status)return void f(new Error("Unknown XHR error"));p(),l=!0,t(null,{body:i.response||(""===i.responseType||"text"===i.responseType?i.responseText:""),url:n.url,method:n.method,headers:y(i.getAllResponseHeaders()),statusCode:i.status,statusMessage:i.statusText})}}()},i.open(n.method,n.url,!0),i.withCredentials=!!n.withCredentials,a&&i.setRequestHeader)for(const e in a)a.hasOwnProperty(e)&&i.setRequestHeader(e,a[e]);return n.rawBody&&(i.responseType="arraybuffer"),e.applyMiddleware("onRequest",{options:n,adapter:b,request:i,context:e}),i.send(n.body||null),c&&(o.connect=setTimeout((()=>d("ETIMEDOUT")),c.connect)),{abort:function(){u=!0,i&&i.abort()}};function d(t){h=!0,i.abort();const r=new Error("ESOCKETTIMEDOUT"===t?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);r.code=t,e.channels.error.publish(r)}function p(e){(e||u||i&&i.readyState>=2&&o.connect)&&clearTimeout(o.connect),o.socket&&clearTimeout(o.socket)}function f(e){if(l)return;p(!0),l=!0,i=null;const r=e||new Error(`Network error while attempting to reach ${n.url}`);r.isNetworkError=!0,r.request=n,t(r)}},w=(e=[],t=m)=>d(e,t);var C,E,I,_,x,R={exports:{}};x||(x=1,function(e,t){t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,o=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(o=n))})),t.splice(o,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch{}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch{}return!e&&typeof process<"u"&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){if(typeof window<"u"&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch{}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=(_?I:(_=1,I=function(e){function t(e){let n,o,s,i=null;function a(...e){if(!a.enabled)return;const r=a,o=Number(new Date),s=o-(n||o);r.diff=s,r.prev=n,r.curr=o,n=o,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,o)=>{if("%%"===n)return"%";i++;const s=t.formatters[o];if("function"==typeof s){const t=e[i];n=s.call(r,t),e.splice(i,1),i--}return n})),t.formatArgs.call(r,e),(r.log||t.log).apply(r,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(o!==t.namespaces&&(o=t.namespaces,s=t.enabled(e)),s),set:e=>{i=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,r){const n=t(this.namespace+(typeof r>"u"?":":r)+e);return n.log=this.log,n}function n(e,t){let r=0,n=0,o=-1,s=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(o=n,s=r,n++):(r++,n++);else{if(-1===o)return!1;n=o+1,s++,r=s}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(n(e,r))return!1;for(const r of t.names)if(n(e,r))return!0;return!1},t.humanize=function(){if(E)return C;E=1;var e=1e3,t=60*e,r=60*t,n=24*r,o=7*n;function s(e,t,r,n){var o=t>=1.5*r;return Math.round(e/r)+" "+n+(o?"s":"")}return C=function(i,a){a=a||{};var c,u,l=typeof i;if("string"===l&&i.length>0)return function(s){if(!((s=String(s)).length>100)){var i=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(s);if(i){var a=parseFloat(i[1]);switch((i[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return a*o;case"days":case"day":case"d":return a*n;case"hours":case"hour":case"hrs":case"hr":case"h":return a*r;case"minutes":case"minute":case"mins":case"min":case"m":return a*t;case"seconds":case"second":case"secs":case"sec":case"s":return a*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(i);if("number"===l&&isFinite(i))return a.long?(c=i,(u=Math.abs(c))>=n?s(c,u,n,"day"):u>=r?s(c,u,r,"hour"):u>=t?s(c,u,t,"minute"):u>=e?s(c,u,e,"second"):c+" ms"):function(o){var s=Math.abs(o);return s>=n?Math.round(o/n)+"d":s>=r?Math.round(o/r)+"h":s>=t?Math.round(o/t)+"m":s>=e?Math.round(o/e)+"s":o+"ms"}(i);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(i))}}(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}))(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(R,R.exports));const q=typeof Buffer>"u"?()=>!1:e=>Buffer.isBuffer(e);function $(e){return"[object Object]"===Object.prototype.toString.call(e)}const S=["boolean","string","number"];function T(){return{processOptions:e=>{const t=e.body;return!t||"function"==typeof t.pipe||q(t)||-1===S.indexOf(typeof t)&&!Array.isArray(t)&&!function(e){if(!1===$(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!(!1===$(r)||!1===r.hasOwnProperty("isPrototypeOf"))}(t)?e:Object.assign({},e,{body:JSON.stringify(e.body),headers:Object.assign({},e.headers,{"Content-Type":"application/json"})})}}}function O(e){return{onResponse:r=>{const n=r.headers["content-type"]||"",o=e&&e.force||-1!==n.indexOf("application/json");return r.body&&n&&o?Object.assign({},r,{body:t(r.body)}):r},processOptions:e=>Object.assign({},e,{headers:Object.assign({Accept:"application/json"},e.headers)})};function t(e){try{return JSON.parse(e)}catch(e){throw e.message=`Failed to parsed response body as JSON: ${e.message}`,e}}}let j={};typeof globalThis<"u"?j=globalThis:typeof window<"u"?j=window:typeof global<"u"?j=global:typeof self<"u"&&(j=self);var A=j;function P(e={}){const t=e.implementation||A.Observable;if(!t)throw new Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(e,r)=>new t((t=>(e.error.subscribe((e=>t.error(e))),e.progress.subscribe((e=>t.next(Object.assign({type:"progress"},e)))),e.response.subscribe((e=>{t.next(Object.assign({type:"response"},e)),t.complete()})),e.request.publish(r),()=>e.abort.publish())))}}var D=(e,t,r)=>("GET"===r.method||"HEAD"===r.method)&&(e.isNetworkError||!1);function k(e){return 100*Math.pow(2,e)+100*Math.random()}const M=(e={})=>(e=>{const t=e.maxRetries||5,r=e.retryDelay||k,n=e.shouldRetry;return{onError:(e,o)=>{const s=o.options,i=s.maxRetries||t,a=s.retryDelay||r,c=s.shouldRetry||n,u=s.attemptNumber||0;if(null!==(l=s.body)&&"object"==typeof l&&"function"==typeof l.pipe||!c(e,u,s)||u>=i)return e;var l;const h=Object.assign({},o,{options:Object.assign({},s,{attemptNumber:u+1})});return setTimeout((()=>o.channels.request.publish(h)),a(u)),null}}})({shouldRetry:D,...e});M.shouldRetry=D;var F=function(e,t){return F=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},F(e,t)};function U(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}F(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function N(e,t,r,n){return new(r||(r=Promise))((function(o,s){function i(e){try{c(n.next(e))}catch(e){s(e)}}function a(e){try{c(n.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,a)}c((n=n.apply(e,t||[])).next())}))}function L(e,t){var r,n,o,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=a(0),i.throw=a(1),i.return=a(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function z(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function H(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,s=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(o)throw o.error}}return i}function V(e,t,r){if(r||2===arguments.length)for(var n,o=0,s=t.length;o<s;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function B(e){return this instanceof B?(this.v=e,this):new B(e)}function W(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),s=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",(function(e){return function(t){return Promise.resolve(t).then(e,u)}})),n[Symbol.asyncIterator]=function(){return this},n;function i(e,t){o[e]&&(n[e]=function(t){return new Promise((function(r,n){s.push([e,t,r,n])>1||a(e,t)}))},t&&(n[e]=t(n[e])))}function a(e,t){try{!function(e){e.value instanceof B?Promise.resolve(e.value.v).then(c,u):l(s[0][2],e)}(o[e](t))}catch(e){l(s[0][3],e)}}function c(e){a("next",e)}function u(e){a("throw",e)}function l(e,t){e(t),s.shift(),s.length&&a(s[0][0],s[0][1])}}function G(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=z(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise((function(n,o){(function(e,t,r,n){Promise.resolve(n).then((function(t){e({value:t,done:r})}),t)})(n,o,(t=e[r](t)).done,t.value)}))}}}function J(e){return"function"==typeof e}function Q(e){var t=e((function(e){Error.call(e),e.stack=(new Error).stack}));return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}"function"==typeof SuppressedError&&SuppressedError;var Y=Q((function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}));function X(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var K=function(){function e(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}var t;return e.prototype.unsubscribe=function(){var e,t,r,n,o;if(!this.closed){this.closed=!0;var s=this._parentage;if(s)if(this._parentage=null,Array.isArray(s))try{for(var i=z(s),a=i.next();!a.done;a=i.next()){a.value.remove(this)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}else s.remove(this);var c=this.initialTeardown;if(J(c))try{c()}catch(e){o=e instanceof Y?e.errors:[e]}var u=this._finalizers;if(u){this._finalizers=null;try{for(var l=z(u),h=l.next();!h.done;h=l.next()){var d=h.value;try{te(d)}catch(e){o=null!=o?o:[],e instanceof Y?o=V(V([],H(o)),H(e.errors)):o.push(e)}}}catch(e){r={error:e}}finally{try{h&&!h.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}}if(o)throw new Y(o)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)te(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(t)}},e.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},e.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},e.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&X(t,e)},e.prototype.remove=function(t){var r=this._finalizers;r&&X(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=((t=new e).closed=!0,t),e}(),Z=K.EMPTY;function ee(e){return e instanceof K||e&&"closed"in e&&J(e.remove)&&J(e.add)&&J(e.unsubscribe)}function te(e){J(e)?e():e.unsubscribe()}var re={Promise:void 0},ne=function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setTimeout.apply(void 0,V([e,t],H(r)))};function oe(e){ne((function(){throw e}))}function se(){}function ie(e){e()}var ae=function(e){function t(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,ee(t)&&t.add(r)):r.destination=he,r}return U(t,e),t.create=function(e,t,r){return new ue(e,t,r)},t.prototype.next=function(e){this.isStopped||this._next(e)},t.prototype.error=function(e){this.isStopped||(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(K),ce=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){le(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){le(e)}else le(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){le(e)}},e}(),ue=function(e){function t(t,r,n){var o,s=e.call(this)||this;return o=J(t)||!t?{next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:t,s.destination=new ce(o),s}return U(t,e),t}(ae);function le(e){oe(e)}var he={closed:!0,next:se,error:function(e){throw e},complete:se},de="function"==typeof Symbol&&Symbol.observable||"@@observable";function pe(e){return e}function fe(e){return 0===e.length?pe:1===e.length?e[0]:function(t){return e.reduce((function(e,t){return t(e)}),t)}}var ye=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var n,o=this,s=(n=e)&&n instanceof ae||function(e){return e&&J(e.next)&&J(e.error)&&J(e.complete)}(n)&&ee(n)?e:new ue(e,t,r);return ie((function(){var e=o,t=e.operator,r=e.source;s.add(t?t.call(s,r):r?o._subscribe(s):o._trySubscribe(s))})),s},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=ge(t))((function(t,n){var o=new ue({next:function(t){try{e(t)}catch(e){n(e),o.unsubscribe()}},error:n,complete:t});r.subscribe(o)}))},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[de]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return fe(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=ge(e))((function(e,r){var n;t.subscribe((function(e){return n=e}),(function(e){return r(e)}),(function(){return e(n)}))}))},e.create=function(t){return new e(t)},e}();function ge(e){var t;return null!==(t=null!=e?e:re.Promise)&&void 0!==t?t:Promise}function be(e){return function(t){if(function(e){return J(null==e?void 0:e.lift)}(t))return t.lift((function(t){try{return e(t,this)}catch(e){this.error(e)}}));throw new TypeError("Unable to lift unknown Observable type")}}function ve(e,t,r,n,o){return new me(e,t,r,n,o)}var me=function(e){function t(t,r,n,o,s,i){var a=e.call(this,t)||this;return a.onFinalize=s,a.shouldUnsubscribe=i,a._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,a._error=o?function(e){try{o(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,a}return U(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),!r&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}},t}(ae),we=Q((function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}})),Ce=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return U(t,e),t.prototype.lift=function(e){var t=new Ee(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new we},t.prototype.next=function(e){var t=this;ie((function(){var r,n;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var o=z(t.currentObservers),s=o.next();!s.done;s=o.next()){s.value.next(e)}}catch(e){r={error:e}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}}}))},t.prototype.error=function(e){var t=this;ie((function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var r=t.observers;r.length;)r.shift().error(e)}}))},t.prototype.complete=function(){var e=this;ie((function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}}))},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,r=this,n=r.hasError,o=r.isStopped,s=r.observers;return n||o?Z:(this.currentObservers=null,s.push(e),new K((function(){t.currentObservers=null,X(s,e)})))},t.prototype._checkFinalizedStatuses=function(e){var t=this,r=t.hasError,n=t.thrownError,o=t.isStopped;r?e.error(n):o&&e.complete()},t.prototype.asObservable=function(){var e=new ye;return e.source=this,e},t.create=function(e,t){return new Ee(e,t)},t}(ye),Ee=function(e){function t(t,r){var n=e.call(this)||this;return n.destination=t,n.source=r,n}return U(t,e),t.prototype.next=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===r||r.call(t,e)},t.prototype.error=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===r||r.call(t,e)},t.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},t.prototype._subscribe=function(e){var t,r;return null!==(r=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==r?r:Z},t}(Ce),Ie={now:function(){return(Ie.delegate||Date).now()},delegate:void 0},_e=function(e){function t(t,r,n){void 0===t&&(t=1/0),void 0===r&&(r=1/0),void 0===n&&(n=Ie);var o=e.call(this)||this;return o._bufferSize=t,o._windowTime=r,o._timestampProvider=n,o._buffer=[],o._infiniteTimeWindow=!0,o._infiniteTimeWindow=r===1/0,o._bufferSize=Math.max(1,t),o._windowTime=Math.max(1,r),o}return U(t,e),t.prototype.next=function(t){var r=this,n=r.isStopped,o=r._buffer,s=r._infiniteTimeWindow,i=r._timestampProvider,a=r._windowTime;n||(o.push(t),!s&&o.push(i.now()+a)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),r=this._infiniteTimeWindow,n=this._buffer.slice(),o=0;o<n.length&&!e.closed;o+=r?1:2)e.next(n[o]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this,t=e._bufferSize,r=e._timestampProvider,n=e._buffer,o=e._infiniteTimeWindow,s=(o?1:2)*t;if(t<1/0&&s<n.length&&n.splice(0,n.length-s),!o){for(var i=r.now(),a=0,c=1;c<n.length&&n[c]<=i;c+=2)a=c;a&&n.splice(0,a+1)}},t}(Ce),xe=function(e){function t(t,r){return e.call(this)||this}return U(t,e),t.prototype.schedule=function(e,t){return this},t}(K),Re=function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setInterval.apply(void 0,V([e,t],H(r)))},qe=function(e){return clearInterval(e)},$e=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n.pending=!1,n}return U(t,e),t.prototype.schedule=function(e,t){var r;if(void 0===t&&(t=0),this.closed)return this;this.state=e;var n=this.id,o=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(o,n,t)),this.pending=!0,this.delay=t,this.id=null!==(r=this.id)&&void 0!==r?r:this.requestAsyncId(o,this.id,t),this},t.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),Re(e.flush.bind(e,this),r)},t.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return t;null!=t&&qe(t)},t.prototype.execute=function(e,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var r,n=!1;try{this.work(e)}catch(e){n=!0,r=e||new Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,X(n,this),null!=t&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(xe),Se=function(){function e(t,r){void 0===r&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(r,t)},e.now=Ie.now,e}(),Te=new(function(e){function t(t,r){void 0===r&&(r=Se.now);var n=e.call(this,t,r)||this;return n.actions=[],n._active=!1,n}return U(t,e),t.prototype.flush=function(e){var t=this.actions;if(this._active)t.push(e);else{var r;this._active=!0;do{if(r=e.execute(e.state,e.delay))break}while(e=t.shift());if(this._active=!1,r){for(;e=t.shift();)e.unsubscribe();throw r}}},t}(Se))($e),Oe=new ye((function(e){return e.complete()}));function je(e){return e[e.length-1]}function Ae(e){return(t=je(e))&&J(t.schedule)?e.pop():void 0;var t}var Pe=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function De(e){return J(null==e?void 0:e.then)}function ke(e){return J(e[de])}function Me(e){return Symbol.asyncIterator&&J(null==e?void 0:e[Symbol.asyncIterator])}function Fe(e){return new TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var Ue="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function Ne(e){return J(null==e?void 0:e[Ue])}function Le(e){return W(this,arguments,(function(){var t,r,n;return L(this,(function(o){switch(o.label){case 0:t=e.getReader(),o.label=1;case 1:o.trys.push([1,,9,10]),o.label=2;case 2:return[4,B(t.read())];case 3:return r=o.sent(),n=r.value,r.done?[4,B(void 0)]:[3,5];case 4:return[2,o.sent()];case 5:return[4,B(n)];case 6:return[4,o.sent()];case 7:return o.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}}))}))}function ze(e){return J(null==e?void 0:e.getReader)}function He(e){if(e instanceof ye)return e;if(null!=e){if(ke(e))return o=e,new ye((function(e){var t=o[de]();if(J(t.subscribe))return t.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")}));if(Pe(e))return n=e,new ye((function(e){for(var t=0;t<n.length&&!e.closed;t++)e.next(n[t]);e.complete()}));if(De(e))return r=e,new ye((function(e){r.then((function(t){e.closed||(e.next(t),e.complete())}),(function(t){return e.error(t)})).then(null,oe)}));if(Me(e))return Ve(e);if(Ne(e))return t=e,new ye((function(e){var r,n;try{for(var o=z(t),s=o.next();!s.done;s=o.next()){var i=s.value;if(e.next(i),e.closed)return}}catch(e){r={error:e}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}e.complete()}));if(ze(e))return Ve(Le(e))}var t,r,n,o;throw Fe(e)}function Ve(e){return new ye((function(t){(function(e,t){var r,n,o,s;return N(this,void 0,void 0,(function(){var i,a;return L(this,(function(c){switch(c.label){case 0:c.trys.push([0,5,6,11]),r=G(e),c.label=1;case 1:return[4,r.next()];case 2:if((n=c.sent()).done)return[3,4];if(i=n.value,t.next(i),t.closed)return[2];c.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return a=c.sent(),o={error:a},[3,11];case 6:return c.trys.push([6,,9,10]),n&&!n.done&&(s=r.return)?[4,s.call(r)]:[3,8];case 7:c.sent(),c.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}}))}))})(e,t).catch((function(e){return t.error(e)}))}))}function Be(e,t,r,n,o){void 0===n&&(n=0),void 0===o&&(o=!1);var s=t.schedule((function(){r(),o?e.add(this.schedule(null,n)):this.unsubscribe()}),n);if(e.add(s),!o)return s}function We(e,t){return void 0===t&&(t=0),be((function(r,n){r.subscribe(ve(n,(function(r){return Be(n,e,(function(){return n.next(r)}),t)}),(function(){return Be(n,e,(function(){return n.complete()}),t)}),(function(r){return Be(n,e,(function(){return n.error(r)}),t)})))}))}function Ge(e,t){return void 0===t&&(t=0),be((function(r,n){n.add(e.schedule((function(){return r.subscribe(n)}),t))}))}function Je(e,t){if(!e)throw new Error("Iterable cannot be null");return new ye((function(r){Be(r,t,(function(){var n=e[Symbol.asyncIterator]();Be(r,t,(function(){n.next().then((function(e){e.done?r.complete():r.next(e.value)}))}),0,!0)}))}))}function Qe(e,t){if(null!=e){if(ke(e))return function(e,t){return He(e).pipe(Ge(t),We(t))}(e,t);if(Pe(e))return function(e,t){return new ye((function(r){var n=0;return t.schedule((function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())}))}))}(e,t);if(De(e))return function(e,t){return He(e).pipe(Ge(t),We(t))}(e,t);if(Me(e))return Je(e,t);if(Ne(e))return function(e,t){return new ye((function(r){var n;return Be(r,t,(function(){n=e[Ue](),Be(r,t,(function(){var e,t,o;try{t=(e=n.next()).value,o=e.done}catch(e){return void r.error(e)}o?r.complete():r.next(t)}),0,!0)})),function(){return J(null==n?void 0:n.return)&&n.return()}}))}(e,t);if(ze(e))return function(e,t){return Je(Le(e),t)}(e,t)}throw Fe(e)}function Ye(e,t){return t?Qe(e,t):He(e)}function Xe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Ye(e,Ae(e))}function Ke(e,t){var r=J(e)?e:function(){return e};return new ye((function(e){return e.error(r())}))}var Ze=Q((function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}}));function et(e,t){return new Promise((function(t,r){var n,o=!1;e.subscribe({next:function(e){n=e,o=!0},error:r,complete:function(){o?t(n):r(new Ze)}})}))}function tt(e,t){return new Promise((function(t,r){var n=new ue({next:function(e){t(e),n.unsubscribe()},error:r,complete:function(){r(new Ze)}});e.subscribe(n)}))}function rt(e,t){return be((function(r,n){var o=0;r.subscribe(ve(n,(function(r){n.next(e.call(t,r,o++))})))}))}var nt=Array.isArray;function ot(e){return rt((function(t){return function(e,t){return nt(t)?e.apply(void 0,V([],H(t))):e(t)}(e,t)}))}function st(e,t,r){t()}function it(e,t,r){return void 0===r&&(r=1/0),J(t)?it((function(r,n){return rt((function(e,o){return t(r,e,n,o)}))(He(e(r,n)))}),r):("number"==typeof t&&(r=t),be((function(t,n){return function(e,t,r,n,o,s,i){var a=[],c=0,u=0,l=!1,h=function(){!l||a.length||c||t.complete()},d=function(e){c++;var o=!1;He(r(e,u++)).subscribe(ve(t,(function(e){t.next(e)}),(function(){o=!0}),void 0,(function(){if(o)try{for(c--;a.length&&c<n;)e=void 0,e=a.shift(),i||d(e);h()}catch(e){t.error(e)}var e})))};return e.subscribe(ve(t,(function(e){return c<n?d(e):a.push(e)}),(function(){l=!0,h()}))),function(){}}(t,n,e,r)})))}function at(e){return void 0===e&&(e=1/0),it(pe,e)}function ct(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return at(1)(Ye(e,Ae(e)))}function ut(e){return new ye((function(t){He(e()).subscribe(t)}))}function lt(e,t,r){return void 0===r&&(r=Te),new ye((function(t){var n,o=(n=e)instanceof Date&&!isNaN(n)?1e3-r.now():e;o<0&&(o=0);var s=0;return r.schedule((function(){t.closed||(t.next(s++),t.complete())}),o)}))}function ht(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Ae(e),n=function(e,t){return"number"==typeof je(e)?e.pop():t}(e,1/0),o=e;return o.length?1===o.length?He(o[0]):at(n)(Ye(o,r)):Oe}var dt=Array.isArray;function pt(e,t){return be((function(r,n){var o=0;r.subscribe(ve(n,(function(r){return e.call(t,r,o++)&&n.next(r)})))}))}function ft(e){return be((function(t,r){var n,o=null,s=!1;o=t.subscribe(ve(r,void 0,void 0,(function(i){n=He(e(i,ft(e)(t))),o?(o.unsubscribe(),o=null,n.subscribe(r)):s=!0}))),s&&(o.unsubscribe(),o=null,n.subscribe(r))}))}function yt(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=function(e){return J(je(e))?e.pop():void 0}(e);return r?function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return fe(e)}(yt.apply(void 0,V([],H(e))),ot(r)):be((function(t,r){var n,o,s;(n=V([t],H(function(e){return 1===e.length&&dt(e[0])?e[0]:e}(e))),void 0===s&&(s=pe),function(e){st(0,(function(){for(var t=n.length,r=new Array(t),i=t,a=t,c=function(t){st(0,(function(){var c=Ye(n[t],o),u=!1;c.subscribe(ve(e,(function(n){r[t]=n,u||(u=!0,a--),a||e.next(s(r.slice()))}),(function(){--i||e.complete()})))}))},u=0;u<t;u++)c(u)}))})(r)}))}function gt(e){return be((function(t,r){try{t.subscribe(r)}finally{r.add(e)}}))}function bt(e){void 0===e&&(e={});var t=e.connector,r=void 0===t?function(){return new Ce}:t,n=e.resetOnError,o=void 0===n||n,s=e.resetOnComplete,i=void 0===s||s,a=e.resetOnRefCountZero,c=void 0===a||a;return function(e){var t,n,s,a=0,u=!1,l=!1,h=function(){null==n||n.unsubscribe(),n=void 0},d=function(){h(),t=s=void 0,u=l=!1},p=function(){var e=t;d(),null==e||e.unsubscribe()};return be((function(e,f){a++,l||u||h();var y=s=null!=s?s:r();f.add((function(){0!==--a||l||u||(n=vt(p,c))})),y.subscribe(f),!t&&a>0&&(t=new ue({next:function(e){return y.next(e)},error:function(e){l=!0,h(),n=vt(d,o,e),y.error(e)},complete:function(){u=!0,h(),n=vt(d,i),y.complete()}}),He(e).subscribe(t))}))(e)}}function vt(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(!0!==t){if(!1!==t){var o=new ue({next:function(){o.unsubscribe(),e()}});return He(t.apply(void 0,V([],H(r)))).subscribe(o)}}else e()}function mt(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}var wt={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},Ct={0:8203,1:8204,2:8205,3:65279},Et=new Array(4).fill(String.fromCodePoint(Ct[0])).join("");function It(e,t,r="auto"){return!0===r||"auto"===r&&(function(e){return!(!Number.isNaN(Number(e))||/[a-z]/i.test(e)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(e)||!Date.parse(e))}(e)||function(e){try{new URL(e,e.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(e))?e:`${e}${function(e){let t=JSON.stringify(e);return`${Et}${Array.from(t).map((e=>{let r=e.charCodeAt(0);if(r>255)throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${t} on character ${e} (${r})`);return Array.from(r.toString(4).padStart(4,"0")).map((e=>String.fromCodePoint(Ct[e]))).join("")})).join("")}`}(t)}`}Object.fromEntries(Object.entries(Ct).map((e=>e.reverse()))),Object.fromEntries(Object.entries(wt).map((e=>e.reverse())));var _t=`${Object.values(wt).map((e=>`\\u{${e.toString(16)}}`)).join("")}`,xt=new RegExp(`[${_t}]{4,}`,"gu");function Rt(e){return e&&JSON.parse(function(e){var t;return{cleaned:e.replace(xt,""),encoded:(null==(t=e.match(xt))?void 0:t[0])||""}}(JSON.stringify(e)).cleaned)}const qt=".",$t=`drafts${qt}`,St=`versions${qt}`;function Tt(e){return e.startsWith($t)}function Ot(e){return e.startsWith(St)}function jt(e){if(Ot(e)){const t=Dt(e);return $t+t}return Tt(e)?e:$t+e}function At(e,t){if("drafts"===t||"published"===t)throw new Error('Version can not be "published" or "drafts"');return`${St}${t}${qt}${Dt(e)}`}function Pt(e){if(!Ot(e))return;const[t,r,...n]=e.split(qt);return r}function Dt(e){return Ot(e)?e.split(qt).slice(2).join(qt):Tt(e)?e.slice($t.length):e}let kt=e=>crypto.getRandomValues(new Uint8Array(e));const Mt=/\r\n|[\n\r\u2028\u2029]/;function Ft(e,t,r){const n=e.split(Mt),o={start:Ut(t.start,n),end:t.end?Ut(t.end,n):void 0},{start:s,end:i,markerLines:a}=function(e,t){const r={...e.start},n={...r,...e.end},o=2,s=3,i=r.line??-1,a=r.column??0,c=n.line,u=n.column;let l=Math.max(i-(o+1),0),h=Math.min(t.length,c+s);-1===i&&(l=0),-1===c&&(h=t.length);const d=c-i,p={};if(d)for(let e=0;e<=d;e++){const r=e+i;if(a)if(0===e){const e=t[r-1].length;p[r]=[a,e-a+1]}else if(e===d)p[r]=[0,u];else{const n=t[r-e].length;p[r]=[0,n]}else p[r]=!0}else p[i]=a===u?!a||[a,0]:[a,u-a];return{start:l,end:h,markerLines:p}}(o,n),c=`${i}`.length;return e.split(Mt,i).slice(s,i).map(((e,t)=>{const n=s+1+t,o=` ${` ${n}`.slice(-c)} |`,i=a[n],u=!a[n+1];if(!i)return` ${o}${e.length>0?` ${e}`:""}`;let l="";if(Array.isArray(i)){const t=e.slice(0,Math.max(i[0]-1,0)).replace(/[^\t]/g," "),n=i[1]||1;l=["\n ",o.replace(/\d/g," ")," ",t,"^".repeat(n)].join(""),u&&r&&(l+=" "+r)}return[">",o,e.length>0?` ${e}`:"",l].join("")})).join("\n")}function Ut(e,t){let r=0;for(let n=0;n<t.length;n++){const o=t[n].length+1;if(r+o>e)return{line:n+1,column:e-r};r+=o}return{line:t.length,column:t[t.length-1]?.length??0}}class Nt extends Error{response;statusCode=400;responseBody;details;constructor(e,t){const r=zt(e,t);super(r.message),Object.assign(this,r)}}class Lt extends Error{response;statusCode=500;responseBody;details;constructor(e){const t=zt(e);super(t.message),Object.assign(this,t)}}function zt(e,t){const r=e.body,n={response:e,statusCode:e.statusCode,responseBody:Wt(r,e),message:"",details:void 0};if(!mt(r))return n.message=Bt(e,r),n;const o=r.error;if("string"==typeof o&&"string"==typeof r.message)return n.message=`${o} - ${r.message}`,n;if("object"!=typeof o||null===o)return"string"==typeof o?n.message=o:"string"==typeof r.message?n.message=r.message:n.message=Bt(e,r),n;if(function(e){return"type"in e&&"mutationError"===e.type&&"description"in e&&"string"==typeof e.description}(o)||function(e){return"type"in e&&"actionError"===e.type&&"description"in e&&"string"==typeof e.description}(o)){const e=o.items||[],t=e.slice(0,5).map((e=>e.error?.description)).filter(Boolean);let s=t.length?`:\n- ${t.join("\n- ")}`:"";return e.length>5&&(s+=`\n...and ${e.length-5} more`),n.message=`${o.description}${s}`,n.details=r.error,n}if(Ht(o)){const e=t?.options?.query?.tag;return n.message=Vt(o,e),n.details=r.error,n}return"description"in o&&"string"==typeof o.description?(n.message=o.description,n.details=o,n):(n.message=Bt(e,r),n)}function Ht(e){return mt(e)&&"queryParseError"===e.type&&"string"==typeof e.query&&"number"==typeof e.start&&"number"==typeof e.end}function Vt(e,t){const{query:r,start:n,end:o,description:s}=e;if(!r||typeof n>"u")return`GROQ query parse error: ${s}`;const i=t?`\n\nTag: ${t}`:"";return`GROQ query parse error:\n${Ft(r,{start:n,end:o},s)}${i}`}function Bt(e,t){const r="string"==typeof t?` (${o=t,s=100,o.length>s?`${o.slice(0,s)}…`:o})`:"",n=e.statusMessage?` ${e.statusMessage}`:"";var o,s;return`${e.method}-request to ${e.url} resulted in HTTP ${e.statusCode}${n}${r}`}function Wt(e,t){return-1!==(t.headers["content-type"]||"").toLowerCase().indexOf("application/json")?JSON.stringify(e,null,2):e}class Gt extends Error{projectId;addOriginUrl;constructor({projectId:e}){super("CorsOriginError"),this.name="CorsOriginError",this.projectId=e;const t=new URL(`https://sanity.io/manage/project/${e}/api`);if(typeof location<"u"){const{origin:e}=location;t.searchParams.set("cors","add"),t.searchParams.set("origin",e),this.addOriginUrl=t,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${t}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${t}`}}const Jt={onResponse:(e,t)=>{if(e.statusCode>=500)throw new Lt(e);if(e.statusCode>=400)throw new Nt(e,t);return e}};function Qt(e={}){const t={},r=t=>void 0!==e.ignoreWarnings&&(Array.isArray(e.ignoreWarnings)?e.ignoreWarnings:[e.ignoreWarnings]).some((e=>"string"==typeof e?t.includes(e):e instanceof RegExp&&e.test(t)));return{onResponse:e=>{const n=e.headers["x-sanity-warning"],o=Array.isArray(n)?n:[n];for(const e of o)!e||t[e]||r(e)||(t[e]=!0,console.warn(e));return e}}}function Yt(e,t={}){return w([M({shouldRetry:Xt}),...e,Qt(t),T(),O(),{onRequest:e=>{if("xhr"!==e.adapter)return;const t=e.request,r=e.context;function n(e){return t=>{const n=t.lengthComputable?t.loaded/t.total*100:-1;r.channels.progress.publish({stage:e,percent:n,total:t.total,loaded:t.loaded,lengthComputable:t.lengthComputable})}}"upload"in t&&"onprogress"in t.upload&&(t.upload.onprogress=n("upload")),"onprogress"in t&&(t.onprogress=n("download"))}},Jt,P({implementation:ye})])}function Xt(e,t,r){if(0===r.maxRetries)return!1;const n="GET"===r.method||"HEAD"===r.method,o=(r.uri||r.url).startsWith("/data/query"),s=e.response&&(429===e.response.statusCode||502===e.response.statusCode||503===e.response.statusCode);return!(!n&&!o||!s)||M.shouldRetry(e,t,r)}function Kt(e){return"https://www.sanity.io/help/"+e}const Zt=["image","file"],er=["before","after","replace"],tr=e=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(e))throw new Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},rr=(e,t)=>{if(null===t||"object"!=typeof t||Array.isArray(t))throw new Error(`${e}() takes an object of properties`)},nr=(e,t)=>{if("string"!=typeof t||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(t)||t.includes(".."))throw new Error(`${e}(): "${t}" is not a valid document ID`)},or=(e,t)=>{if(!t._id)throw new Error(`${e}() requires that the document contains an ID ("_id" property)`);nr(e,t._id)},sr=(e,t)=>{if(!t._type)throw new Error(`\`${e}()\` requires that the document contains a type (\`_type\` property)`);((e,t)=>{if("string"!=typeof t)throw new Error(`\`${e}()\`: \`${t}\` is not a valid document type`)})(e,t._type)},ir=e=>{if(!e.dataset)throw new Error("`dataset` must be provided to perform queries");return e.dataset||""},ar=e=>{if("string"!=typeof e||!/^[a-z0-9._-]{1,75}$/i.test(e))throw new Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return e},cr=e=>{if(!e["~experimental_resource"])throw new Error("`resource` must be provided to perform resource queries");const{type:t,id:r}=e["~experimental_resource"];switch(t){case"dataset":if(2!==r.split(".").length)throw new Error('Dataset resource ID must be in the format "project.dataset"');return;case"dashboard":case"media-library":case"canvas":return;default:throw new Error(`Unsupported resource type: ${t.toString()}`)}},ur=(e,t)=>{if(t["~experimental_resource"])throw new Error(`\`${e}\` does not support resource-based operations`)};const lr=e=>function(e){let t,r=!1;return(...n)=>(r||(t=e(...n),r=!0),t)}(((...t)=>console.warn(e.join(" "),...t))),hr=lr(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),dr=lr(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),pr=lr(["The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),fr=lr(["The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version"]),yr=lr(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${Kt("js-client-browser-token")} for more information and how to hide this warning.`]),gr=lr(["You have configured Sanity client to use a token, but also provided `withCredentials: true`.","This is no longer supported - only token will be used - remove `withCredentials: true`."]),br=lr(["Using the Sanity client without specifying an API version is deprecated.",`See ${Kt("js-client-api-version")}`]),vr=lr(["The default export of @sanity/client has been deprecated. Use the named export `createClient` instead."]),mr=lr(["You have called `createVersion()` with a defined `document`. The recommended approach is to provide a `baseId` and `releaseId` instead."]),wr={apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}},Cr=["localhost","127.0.0.1","0.0.0.0"];function Er(e){if(Array.isArray(e)&&e.length>1&&e.includes("raw"))throw new TypeError('Invalid API perspective value: "raw". The raw-perspective can not be combined with other perspectives')}const Ir=(e,t)=>{const r={...t,...e,stega:{..."boolean"==typeof t.stega?{enabled:t.stega}:t.stega||wr.stega,..."boolean"==typeof e.stega?{enabled:e.stega}:e.stega||{}}};r.apiVersion||br();const n={...wr,...r},o=n.useProjectHostname&&!n["~experimental_resource"];if(typeof Promise>"u"){const e=Kt("js-client-promise-polyfill");throw new Error(`No native Promise-implementation found, polyfill needed - see ${e}`)}if(o&&!n.projectId)throw new Error("Configuration must contain `projectId`");if(n["~experimental_resource"]&&cr(n),typeof n.perspective<"u"&&Er(n.perspective),"encodeSourceMap"in n)throw new Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in n)throw new Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if("boolean"!=typeof n.stega.enabled)throw new Error(`stega.enabled must be a boolean, received ${n.stega.enabled}`);if(n.stega.enabled&&void 0===n.stega.studioUrl)throw new Error("stega.studioUrl must be defined when stega.enabled is true");if(n.stega.enabled&&"string"!=typeof n.stega.studioUrl&&"function"!=typeof n.stega.studioUrl)throw new Error(`stega.studioUrl must be a string or a function, received ${n.stega.studioUrl}`);const s=typeof window<"u"&&window.location&&window.location.hostname,i=s&&(e=>-1!==Cr.indexOf(e))(window.location.hostname),a=!!n.token;n.withCredentials&&a&&(gr(),n.withCredentials=!1),s&&i&&a&&!0!==n.ignoreBrowserTokenWarning?yr():typeof n.useCdn>"u"&&dr(),o&&(e=>{if(!/^[-a-z0-9]+$/i.test(e))throw new Error("`projectId` can only contain only a-z, 0-9 and dashes")})(n.projectId),n.dataset&&tr(n.dataset),"requestTagPrefix"in n&&(n.requestTagPrefix=n.requestTagPrefix?ar(n.requestTagPrefix).replace(/\.+$/,""):void 0),n.apiVersion=`${n.apiVersion}`.replace(/^v/,""),n.isDefaultApi=n.apiHost===wr.apiHost,!0===n.useCdn&&n.withCredentials&&hr(),n.useCdn=!1!==n.useCdn&&!n.withCredentials,function(e){if("1"===e||"X"===e)return;const t=new Date(e);if(!(/^\d{4}-\d{2}-\d{2}$/.test(e)&&t instanceof Date&&t.getTime()>0))throw new Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}(n.apiVersion);const c=n.apiHost.split("://",2),u=c[0],l=c[1],h=n.isDefaultApi?"apicdn.sanity.io":l;return o?(n.url=`${u}://${n.projectId}.${l}/v${n.apiVersion}`,n.cdnUrl=`${u}://${n.projectId}.${h}/v${n.apiVersion}`):(n.url=`${n.apiHost}/v${n.apiVersion}`,n.cdnUrl=n.url),n};class _r extends Error{name="ConnectionFailedError"}class xr extends Error{name="DisconnectError";reason;constructor(e,t,r={}){super(e,r),this.reason=t}}class Rr extends Error{name="ChannelError";data;constructor(e,t){super(e),this.data=t}}class qr extends Error{name="MessageError";data;constructor(e,t,r={}){super(e,r),this.data=t}}class $r extends Error{name="MessageParseError"}const Sr=["channelError","disconnect"];function Tr(e,t){return ut((()=>{const t=e();return(r=t)&&(r instanceof ye||J(r.lift)&&J(r.subscribe))?t:Xe(t);var r})).pipe(it((e=>function(e,t){return new ye((r=>{const n=t.includes("open"),o=t.includes("reconnect");function s(t){if("data"in t){const[e,n]=Or(t);r.error(e?new $r("Unable to parse EventSource error message",{cause:n}):new qr((n?.data).message,n))}else e.readyState===e.CLOSED?r.error(new _r("EventSource connection failed")):o&&r.next({type:"reconnect"})}function i(){r.next({type:"open"})}function a(t){const[n,o]=Or(t);if(n)r.error(new $r("Unable to parse EventSource message",{cause:n}));else if("channelError"!==t.type)"disconnect"!==t.type?r.next({type:t.type,id:t.lastEventId,...o.data?{data:o.data}:{}}):r.error(new xr(`Server disconnected client: ${o.data?.reason||"unknown error"}`));else{const t=new URL(e.url).searchParams.get("tag");r.error(new Rr(function(e,t){const r=e.error;return r?Ht(r)?Vt(r,t):r.description?r.description:"string"==typeof r?r:JSON.stringify(r,null,2):e.message||"Unknown listener error"}(o?.data,t),o.data))}}e.addEventListener("error",s),n&&e.addEventListener("open",i);const c=[...new Set([...Sr,...t])].filter((e=>"error"!==e&&"open"!==e&&"reconnect"!==e));return c.forEach((t=>e.addEventListener(t,a))),()=>{e.removeEventListener("error",s),n&&e.removeEventListener("open",i),c.forEach((t=>e.removeEventListener(t,a))),e.close()}}))}(e,t))))}function Or(e){try{const t="string"==typeof e.data&&JSON.parse(e.data);return[null,{type:e.type,id:e.lastEventId,...jr(t)?{}:{data:t}}]}catch(e){return[e,null]}}function jr(e){for(const t in e)return!1;return!0}function Ar(e){if("string"==typeof e)return{id:e};if(Array.isArray(e))return{query:"*[_id in $ids]",params:{ids:e}};if("object"==typeof e&&null!==e&&"query"in e&&"string"==typeof e.query)return"params"in e&&"object"==typeof e.params&&null!==e.params?{query:e.query,params:e.params}:{query:e.query};const t=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join("\n");throw new Error(`Unknown selection - must be one of:\n\n${t}`)}class Pr{selection;operations;constructor(e,t={}){this.selection=e,this.operations=t}set(e){return this._assign("set",e)}setIfMissing(e){return this._assign("setIfMissing",e)}diffMatchPatch(e){return rr("diffMatchPatch",e),this._assign("diffMatchPatch",e)}unset(e){if(!Array.isArray(e))throw new Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:e}),this}inc(e){return this._assign("inc",e)}dec(e){return this._assign("dec",e)}insert(e,t,r){return((e,t,r)=>{const n="insert(at, selector, items)";if(-1===er.indexOf(e)){const e=er.map((e=>`"${e}"`)).join(", ");throw new Error(`${n} takes an "at"-argument which is one of: ${e}`)}if("string"!=typeof t)throw new Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw new Error(`${n} takes an "items"-argument which must be an array`)})(e,t,r),this._assign("insert",{[e]:t,items:r})}append(e,t){return this.insert("after",`${e}[-1]`,t)}prepend(e,t){return this.insert("before",`${e}[0]`,t)}splice(e,t,r,n){const o=t<0?t-1:t,s=typeof r>"u"||-1===r?-1:Math.max(0,t+r),i=`${e}[${o}:${o<0&&s>=0?"":s}]`;return this.insert("replace",i,n||[])}ifRevisionId(e){return this.operations.ifRevisionID=e,this}serialize(){return{...Ar(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(e,t,r=!0){return rr(e,t),this.operations=Object.assign({},this.operations,{[e]:Object.assign({},r&&this.operations[e]||{},t)}),this}_set(e,t){return this._assign(e,t,!1)}}class Dr extends Pr{#a;constructor(e,t,r){super(e,t),this.#a=r}clone(){return new Dr(this.selection,{...this.operations},this.#a)}commit(e){if(!this.#a)throw new Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");const t="string"==typeof this.selection,r=Object.assign({returnFirst:t,returnDocuments:!0},e);return this.#a.mutate({patch:this.serialize()},r)}}class kr extends Pr{#a;constructor(e,t,r){super(e,t),this.#a=r}clone(){return new kr(this.selection,{...this.operations},this.#a)}commit(e){if(!this.#a)throw new Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");const t="string"==typeof this.selection,r=Object.assign({returnFirst:t,returnDocuments:!0},e);return this.#a.mutate({patch:this.serialize()},r)}}const Mr={returnDocuments:!1};class Fr{operations;trxId;constructor(e=[],t){this.operations=e,this.trxId=t}create(e){return rr("create",e),this._add({create:e})}createIfNotExists(e){const t="createIfNotExists";return rr(t,e),or(t,e),this._add({[t]:e})}createOrReplace(e){const t="createOrReplace";return rr(t,e),or(t,e),this._add({[t]:e})}delete(e){return nr("delete",e),this._add({delete:{id:e}})}transactionId(e){return e?(this.trxId=e,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(e){return this.operations.push(e),this}}class Ur extends Fr{#a;constructor(e,t,r){super(e,r),this.#a=t}clone(){return new Ur([...this.operations],this.#a,this.trxId)}commit(e){if(!this.#a)throw new Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},Mr,e||{}))}patch(e,t){const r="function"==typeof t,n="object"==typeof e&&("query"in e||"id"in e);if("string"!=typeof e&&e instanceof kr)return this._add({patch:e.serialize()});if(r){const r=t(new kr(e,{},this.#a));if(!(r instanceof kr))throw new Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}if(n){const r=new kr(e,t||{},this.#a);return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}class Nr extends Fr{#a;constructor(e,t,r){super(e,r),this.#a=t}clone(){return new Nr([...this.operations],this.#a,this.trxId)}commit(e){if(!this.#a)throw new Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},Mr,e||{}))}patch(e,t){const r="function"==typeof t;if("string"!=typeof e&&e instanceof Dr)return this._add({patch:e.serialize()});if(r){const r=t(new Dr(e,{},this.#a));if(!(r instanceof Dr))throw new Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}const Lr=({query:e,params:t={},options:r={}})=>{const n=new URLSearchParams,{tag:o,includeMutations:s,returnQuery:i,...a}=r;o&&n.append("tag",o),n.append("query",e);for(const[e,r]of Object.entries(t))void 0!==r&&n.append(`$${e}`,JSON.stringify(r));for(const[e,t]of Object.entries(a))t&&n.append(e,`${t}`);return!1===i&&n.append("returnQuery","false"),!1===s&&n.append("includeMutations","false"),`?${n}`},zr=(e={})=>{return{dryRun:e.dryRun,returnIds:!0,returnDocuments:(t=e.returnDocuments,r=!0,!1===t?void 0:typeof t>"u"?r:t),visibility:e.visibility||"sync",autoGenerateArrayKeys:e.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:e.skipCrossDatasetReferenceValidation};var t,r},Hr=e=>"response"===e.type,Vr=e=>e.body;function Br(e,t,r,n,o={},s={}){const i="stega"in s?{...r||{},..."boolean"==typeof s.stega?{enabled:s.stega}:s.stega||{}}:r,a=i.enabled?Rt(o):o,c=!1===s.filterResponse?e=>e:e=>e.result,{cache:u,next:l,...h}={useAbortSignal:typeof s.signal<"u",resultSourceMap:i.enabled?"withKeyArraySelector":s.resultSourceMap,...s,returnQuery:!1===s.filterResponse&&!1!==s.returnQuery},d=sn(e,t,"query",{query:n,params:a},typeof u<"u"||typeof l<"u"?{...h,fetch:{cache:u,next:l}}:h);return i.enabled?d.pipe(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return yt.apply(void 0,V([],H(e)))}(Ye(Promise.resolve().then((function(){return $o})).then((function(e){return e.stegaEncodeSourceMap$1})).then((({stegaEncodeSourceMap:e})=>e)))),rt((([e,t])=>{const r=t(e.result,e.resultSourceMap,i);return c({...e,result:r})}))):d.pipe(rt(c))}function Wr(e,t,r,n={}){return hn(e,t,{uri:pn(e,"doc",(()=>{if(!n.releaseId)return r;const e=Pt(r);if(!e){if(Tt(r))throw new Error(`The document ID (\`${r}\`) is a draft, but \`options.releaseId\` is set as \`${n.releaseId}\``);return At(r,n.releaseId)}if(e!==n.releaseId)throw new Error(`The document ID (\`${r}\`) is already a version of \`${e}\` release, but this does not match the provided \`options.releaseId\` (\`${n.releaseId}\`)`);return r})()),json:!0,tag:n.tag,signal:n.signal}).pipe(pt(Hr),rt((e=>e.body.documents&&e.body.documents[0])))}function Gr(e,t,r,n={}){return hn(e,t,{uri:pn(e,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal}).pipe(pt(Hr),rt((e=>{const t=(n=e.body.documents||[],o=e=>e._id,n.reduce(((e,t)=>(e[o(t)]=t,e)),Object.create(null)));var n,o;return r.map((e=>t[e]||null))})))}function Jr(e,t,r,n={}){return sn(e,t,"query",{query:"*[sanity::partOfRelease($releaseId)]",params:{releaseId:r}},n)}function Qr(e,t,r,n){return or("createIfNotExists",r),an(e,t,r,"createIfNotExists",n)}function Yr(e,t,r,n){return or("createOrReplace",r),an(e,t,r,"createOrReplace",n)}function Xr(e,t,r,n,o){return or("createVersion",r),sr("createVersion",r),mr(),on(e,t,{actionType:"sanity.action.document.version.create",publishedId:n,document:r},o)}function Kr(e,t,r,n,o,s,i){if(!n)throw new Error("`createVersion()` requires `baseId` when no `document` is provided");if(!r)throw new Error("`createVersion()` requires `publishedId` when `baseId` is provided");nr("createVersion",n),nr("createVersion",r);return on(e,t,{actionType:"sanity.action.document.version.create",publishedId:r,baseId:n,versionId:o?At(r,o):jt(r),ifBaseRevisionId:s},i)}function Zr(e,t,r,n){return sn(e,t,"mutate",{mutations:[{delete:Ar(r)}]},n)}function en(e,t,r,n=!1,o){return on(e,t,{actionType:"sanity.action.document.version.discard",versionId:r,purge:n},o)}function tn(e,t,r,n){return or("replaceVersion",r),sr("replaceVersion",r),on(e,t,{actionType:"sanity.action.document.version.replace",document:r},n)}function rn(e,t,r,n,o){return on(e,t,{actionType:"sanity.action.document.version.unpublish",versionId:r,publishedId:n},o)}function nn(e,t,r,n){let o;o=r instanceof kr||r instanceof Dr?{patch:r.serialize()}:r instanceof Ur||r instanceof Nr?r.serialize():r;return sn(e,t,"mutate",{mutations:Array.isArray(o)?o:[o],transactionId:n&&n.transactionId||void 0},n)}function on(e,t,r,n){return sn(e,t,"actions",{actions:Array.isArray(r)?r:[r],transactionId:n&&n.transactionId||void 0,skipCrossDatasetReferenceValidation:n&&n.skipCrossDatasetReferenceValidation||void 0,dryRun:n&&n.dryRun||void 0},n)}function sn(e,t,r,n,o={}){const s="mutate"===r,i="actions"===r,a="query"===r,c=s||i?"":Lr(n),u=!s&&!i&&c.length<11264,l=u?c:"",h=o.returnFirst,{timeout:d,token:p,tag:f,headers:y,returnQuery:g,lastLiveEventId:b,cacheMode:v}=o;return hn(e,t,{method:u?"GET":"POST",uri:pn(e,r,l),json:!0,body:u?void 0:n,query:s&&zr(o),timeout:d,headers:y,token:p,tag:f,returnQuery:g,perspective:o.perspective,resultSourceMap:o.resultSourceMap,lastLiveEventId:Array.isArray(b)?b[0]:b,cacheMode:v,canUseCdn:a,signal:o.signal,fetch:o.fetch,useAbortSignal:o.useAbortSignal,useCdn:o.useCdn}).pipe(pt(Hr),rt(Vr),rt((e=>{if(!s)return e;const t=e.results||[];if(o.returnDocuments)return h?t[0]&&t[0].document:t.map((e=>e.document));const r=h?"documentId":"documentIds",n=h?t[0]&&t[0].id:t.map((e=>e.id));return{transactionId:e.transactionId,results:t,[r]:n}})))}function an(e,t,r,n,o={}){return sn(e,t,"mutate",{mutations:[{[n]:r}]},Object.assign({returnFirst:!0,returnDocuments:!0},o))}const cn=e=>void 0!==e.config().dataset&&void 0!==e.config().projectId||void 0!==e.config()["~experimental_resource"],un=(e,t)=>cn(e)&&t.startsWith(pn(e,"query")),ln=(e,t)=>t.startsWith("/data/")||un(e,t)||((e,t)=>cn(e)&&t.startsWith(pn(e,"mutate")))(e,t)||((e,t)=>cn(e)&&t.startsWith(pn(e,"doc","")))(e,t)||((e,t)=>cn(e)&&t.startsWith(pn(e,"listen")))(e,t)||((e,t)=>cn(e)&&t.startsWith(pn(e,"history","")))(e,t);function hn(e,t,r){const n=r.url||r.uri,o=e.config(),s=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&ln(e,n):r.canUseCdn;let i=(r.useCdn??o.useCdn)&&s;const a=r.tag&&o.requestTagPrefix?[o.requestTagPrefix,r.tag].join("."):r.tag||o.requestTagPrefix;if(a&&null!==r.tag&&(r.query={tag:ar(a),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&un(e,n)){const e=r.resultSourceMap??o.resultSourceMap;void 0!==e&&!1!==e&&(r.query={resultSourceMap:e,...r.query});const t=r.perspective||o.perspective;typeof t<"u"&&("previewDrafts"===t&&fr(),Er(t),r.query={perspective:Array.isArray(t)?t.join(","):t,...r.query},(Array.isArray(t)&&t.length>0||"previewDrafts"===t||"drafts"===t)&&i&&(i=!1,pr())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),!1===r.returnQuery&&(r.query={returnQuery:"false",...r.query}),i&&"noStale"==r.cacheMode&&(r.query={cacheMode:"noStale",...r.query})}const c=function(e,t={}){const r={};e.headers&&Object.assign(r,e.headers);const n=t.token||e.token;n&&(r.Authorization=`Bearer ${n}`),!t.useGlobalApi&&!e.useProjectHostname&&e.projectId&&(r["X-Sanity-Project-ID"]=e.projectId);const o=!!(typeof t.withCredentials>"u"?e.withCredentials:t.withCredentials),s=typeof t.timeout>"u"?e.timeout:t.timeout;return Object.assign({},t,{headers:Object.assign({},r,t.headers||{}),timeout:typeof s>"u"?3e5:s,proxy:t.proxy||e.proxy,json:!0,withCredentials:o,fetch:"object"==typeof t.fetch&&"object"==typeof e.fetch?{...e.fetch,...t.fetch}:t.fetch||e.fetch})}(o,Object.assign({},r,{url:fn(e,n,i)})),u=new ye((e=>t(c,o.requester).subscribe(e)));return r.signal?u.pipe((l=r.signal,e=>new ye((t=>{const r=()=>t.error(function(e){if(yn)return new DOMException(e?.reason??"The operation was aborted.","AbortError");const t=new Error(e?.reason??"The operation was aborted.");return t.name="AbortError",t}(l));if(l&&l.aborted)return void r();const n=e.subscribe(t);return l.addEventListener("abort",r),()=>{l.removeEventListener("abort",r),n.unsubscribe()}})))):u;var l}function dn(e,t,r){return hn(e,t,r).pipe(pt((e=>"response"===e.type)),rt((e=>e.body)))}function pn(e,t,r){const n=e.config();if(n["~experimental_resource"]){cr(n);return`${gn(n)}/${void 0!==r?`${t}/${r}`:t}`.replace(/\/($|\?)/,"$1")}const o=`/${t}/${ir(n)}`;return`/data${void 0!==r?`${o}/${r}`:o}`.replace(/\/($|\?)/,"$1")}function fn(e,t,r=!1){const{url:n,cdnUrl:o}=e.config();return`${r?o:n}/${t.replace(/^\//,"")}`}const yn=!!globalThis.DOMException;const gn=e=>{if(!e["~experimental_resource"])throw new Error("`resource` must be provided to perform resource queries");const{type:t,id:r}=e["~experimental_resource"];switch(t){case"dataset":{const e=r.split(".");if(2!==e.length)throw new Error('Dataset ID must be in the format "project.dataset"');return`/projects/${e[0]}/datasets/${e[1]}`}case"canvas":return`/canvases/${r}`;case"media-library":return`/media-libraries/${r}`;case"dashboard":return`/dashboards/${r}`;default:throw new Error(`Unsupported resource type: ${t.toString()}`)}};function bn(e,t,r){const n=ir(e.config());return dn(e,t,{method:"POST",uri:`/agent/action/generate/${n}`,body:r})}function vn(e,t,r){const n=ir(e.config());return dn(e,t,{method:"POST",uri:`/agent/action/transform/${n}`,body:r})}function mn(e,t,r){const n=ir(e.config());return dn(e,t,{method:"POST",uri:`/agent/action/translate/${n}`,body:r})}class wn{#a;#c;constructor(e,t){this.#a=e,this.#c=t}generate(e){return bn(this.#a,this.#c,e)}transform(e){return vn(this.#a,this.#c,e)}translate(e){return mn(this.#a,this.#c,e)}}class Cn{#a;#c;constructor(e,t){this.#a=e,this.#c=t}generate(e){return et(bn(this.#a,this.#c,e))}transform(e){return et(vn(this.#a,this.#c,e))}translate(e){return et(mn(this.#a,this.#c,e))}prompt(e){return et(function(e,t,r){const n=ir(e.config());return dn(e,t,{method:"POST",uri:`/agent/action/prompt/${n}`,body:r})}(this.#a,this.#c,e))}patch(e){return et(function(e,t,r){const n=ir(e.config());return dn(e,t,{method:"POST",uri:`/agent/action/patch/${n}`,body:r})}(this.#a,this.#c,e))}}class En{#a;#c;constructor(e,t){this.#a=e,this.#c=t}upload(e,t,r){return _n(this.#a,this.#c,e,t,r)}}class In{#a;#c;constructor(e,t){this.#a=e,this.#c=t}upload(e,t,r){return et(_n(this.#a,this.#c,e,t,r).pipe(pt((e=>"response"===e.type)),rt((e=>e.body.document))))}}function _n(e,t,r,n,o={}){(e=>{if(-1===Zt.indexOf(e))throw new Error(`Invalid asset type: ${e}. Must be one of ${Zt.join(", ")}`)})(r);let s=o.extract||void 0;s&&!s.length&&(s=["none"]);const i=e.config(),a=function(e,t){return typeof File>"u"||!(t instanceof File)?e:Object.assign({filename:!1===e.preserveFilename?void 0:t.name,contentType:t.type},e)}(o,n),{tag:c,label:u,title:l,description:h,creditLine:d,filename:p,source:f}=a,y={label:u,title:l,description:h,filename:p,meta:s,creditLine:d};return f&&(y.sourceId=f.id,y.sourceName=f.name,y.sourceUrl=f.url),hn(e,t,{tag:c,method:"POST",timeout:a.timeout||0,uri:xn(i,r),headers:a.contentType?{"Content-Type":a.contentType}:{},query:y,body:n})}function xn(e,t){const r="image"===t?"images":"files";if(e["~experimental_resource"]){const{type:t,id:n}=e["~experimental_resource"];switch(t){case"dataset":throw new Error("Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.");case"canvas":return`/canvases/${n}/assets/${r}`;case"media-library":return`/media-libraries/${n}/upload`;case"dashboard":return`/dashboards/${n}/assets/${r}`;default:throw new Error(`Unsupported resource type: ${t.toString()}`)}}return`assets/${r}/${ir(e)}`}const Rn=ut((()=>Promise.resolve().then((function(){return Fo})))).pipe(rt((({default:e})=>e)),(Sn=1,bt({connector:function(){return new _e(Sn,qn,$n)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:!1})));var qn,$n,Sn;function Tn(){return function(e){return e.pipe(ft(((e,t)=>e instanceof _r?ct(Xe({type:"reconnect"}),lt(1e3).pipe(it((()=>t)))):Ke((()=>e)))))}}const On=["includePreviousRevision","includeResult","includeMutations","includeAllVersions","visibility","effectFormat","tag"],jn={includeResult:!0};function An(e,t,r={}){const{url:n,token:o,withCredentials:s,requestTagPrefix:i,headers:a}=this.config(),c=r.tag&&i?[i,r.tag].join("."):r.tag,u={...(d=r,p=jn,Object.keys(p).concat(Object.keys(d)).reduce(((e,t)=>(e[t]=typeof d[t]>"u"?p[t]:d[t],e)),{})),tag:c},l=((e,t)=>t.reduce(((t,r)=>(typeof e[r]>"u"||(t[r]=e[r]),t)),{}))(u,On),h=`${n}${pn(this,"listen",Lr({query:e,params:t,options:{tag:c,...l}}))}`;var d,p;if(h.length>14800)return Ke((()=>new Error("Query too large for listener")));const f=u.events?u.events:["mutation"],y={};return s&&(y.withCredentials=!0),(o||a)&&(y.headers={},o&&(y.headers.Authorization=`Bearer ${o}`),a&&Object.assign(y.headers,a)),Tr((()=>(typeof EventSource>"u"||y.headers?Rn:Xe(EventSource)).pipe(rt((e=>new e(h,y))))),f).pipe(Tn(),pt((e=>f.includes(e.type))),rt((e=>({type:e.type,..."data"in e?e.data:{}}))))}function Pn(e,t){return function(e){return t=>{let r,n=!1;const{predicate:o,...s}=e,i=t.pipe((l=J(a=t=>{e.predicate(t)&&(n=!0,r=t)})||c||u?{next:a,error:c,complete:u}:a)?be((function(e,t){var r;null===(r=l.subscribe)||void 0===r||r.call(l);var n=!0;e.subscribe(ve(t,(function(e){var r;null===(r=l.next)||void 0===r||r.call(l,e),t.next(e)}),(function(){var e;n=!1,null===(e=l.complete)||void 0===e||e.call(l),t.complete()}),(function(e){var r;n=!1,null===(r=l.error)||void 0===r||r.call(l,e),t.error(e)}),(function(){var e,t;n&&(null===(e=l.unsubscribe)||void 0===e||e.call(l)),null===(t=l.finalize)||void 0===t||t.call(l)})))})):pe,gt((()=>{n=!1,r=void 0})),bt(s));var a,c,u,l;return ht(i,new ye((e=>{n&&e.next(r),e.complete()})))}}("function"==typeof e?{predicate:e,...t}:e)}const Dn="2021-03-25";class kn{#a;constructor(e){this.#a=e}events({includeDrafts:e=!1,tag:t}={}){ur("live",this.#a.config());const{projectId:r,apiVersion:n,token:o,withCredentials:s,requestTagPrefix:i,headers:a}=this.#a.config(),c=n.replace(/^v/,"");if("X"!==c&&c<Dn)throw new Error(`The live events API requires API version ${Dn} or later. The current API version is ${c}. Please update your API version to use this feature.`);if(e&&!o&&!s)throw new Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");const u=pn(this.#a,"live/events"),l=new URL(this.#a.getUrl(u,!1)),h=t&&i?[i,t].join("."):t;h&&l.searchParams.set("tag",h),e&&l.searchParams.set("includeDrafts","true");const d={};e&&s&&(d.withCredentials=!0),(e&&o||a)&&(d.headers={},e&&o&&(d.headers.Authorization=`Bearer ${o}`),a&&Object.assign(d.headers,a));const p=`${l.href}::${JSON.stringify(d)}`,f=Mn.get(p);if(f)return f;const y=Tr((()=>(typeof EventSource>"u"||d.headers?Rn:Xe(EventSource)).pipe(rt((e=>new e(l.href,d))))),["message","restart","welcome","reconnect","goaway"]).pipe(Tn(),rt((e=>{if("message"===e.type){const{data:t,...r}=e;return{...r,tags:t.tags}}return e}))),g=function(e,t){return new ye((r=>{const n=new AbortController,o=n.signal;return fetch(e,{...t,signal:n.signal}).then((e=>{r.next(e),r.complete()}),(e=>{o.aborted||r.error(e)})),()=>n.abort()}))}(l,{method:"OPTIONS",mode:"cors",credentials:d.withCredentials?"include":"omit",headers:d.headers}).pipe(it((()=>Oe)),ft((()=>{throw new Gt({projectId:r})}))),b=ct(g,y).pipe(gt((()=>Mn.delete(p))),Pn({predicate:e=>"welcome"===e.type}));return Mn.set(p,b),b}}const Mn=new Map;class Fn{#a;#c;constructor(e,t){this.#a=e,this.#c=t}create(e,t){return Nn(this.#a,this.#c,"PUT",e,t)}edit(e,t){return Nn(this.#a,this.#c,"PATCH",e,t)}delete(e){return Nn(this.#a,this.#c,"DELETE",e)}list(){return dn(this.#a,this.#c,{uri:"/datasets",tag:null})}}class Un{#a;#c;constructor(e,t){this.#a=e,this.#c=t}create(e,t){return ur("dataset",this.#a.config()),et(Nn(this.#a,this.#c,"PUT",e,t))}edit(e,t){return ur("dataset",this.#a.config()),et(Nn(this.#a,this.#c,"PATCH",e,t))}delete(e){return ur("dataset",this.#a.config()),et(Nn(this.#a,this.#c,"DELETE",e))}list(){return ur("dataset",this.#a.config()),et(dn(this.#a,this.#c,{uri:"/datasets",tag:null}))}}function Nn(e,t,r,n,o){return ur("dataset",e.config()),tr(n),dn(e,t,{method:r,uri:`/datasets/${n}`,body:o,tag:null})}class Ln{#a;#c;constructor(e,t){this.#a=e,this.#c=t}list(e){ur("projects",this.#a.config());const t=!1===e?.includeMembers?"/projects?includeMembers=false":"/projects";return dn(this.#a,this.#c,{uri:t})}getById(e){return ur("projects",this.#a.config()),dn(this.#a,this.#c,{uri:`/projects/${e}`})}}class zn{#a;#c;constructor(e,t){this.#a=e,this.#c=t}list(e){ur("projects",this.#a.config());const t=!1===e?.includeMembers?"/projects?includeMembers=false":"/projects";return et(dn(this.#a,this.#c,{uri:t}))}getById(e){return ur("projects",this.#a.config()),et(dn(this.#a,this.#c,{uri:`/projects/${e}`}))}}const Hn=((e,t=21)=>((e,t,r)=>{let n=(2<<Math.log(e.length-1)/Math.LN2)-1,o=-~(1.6*n*t/e.length);return(s=t)=>{let i="";for(;;){let t=r(o),a=0|o;for(;a--;)if(i+=e[t[a]&n]||"",i.length===s)return i}}})(e,t,kt))("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",8),Vn=(e,t)=>t?At(e,t):jt(e);function Bn(e,{releaseId:t,publishedId:r,document:n}){if(r&&n._id){const e=Vn(r,t);return((e,t)=>{if(t._id&&t._id!==e)throw new Error(`The provided document ID (\`${t._id}\`) does not match the generated version ID (\`${e}\`)`)})(e,n),e}if(n._id){const r=Tt(n._id),o=Ot(n._id);if(!r&&!o)throw new Error(`\`${e}()\` requires a document with an \`_id\` that is a version or draft ID`);if(t){if(r)throw new Error(`\`${e}()\` was called with a document ID (\`${n._id}\`) that is a draft ID, but a release ID (\`${t}\`) was also provided.`);const o=Pt(n._id);if(o!==t)throw new Error(`\`${e}()\` was called with a document ID (\`${n._id}\`) that is a version ID, but the release ID (\`${t}\`) does not match the document's version ID (\`${o}\`).`)}return n._id}if(r)return Vn(r,t);throw new Error(`\`${e}()\` requires either a publishedId or a document with an \`_id\``)}const Wn=(e,t)=>{const[r,n,o]=((e,t)=>{if("object"==typeof e&&null!==e&&("releaseId"in e||"metadata"in e)){const{releaseId:r=Hn(),metadata:n={}}=e;return[r,n,t]}return[Hn(),{},e]})(e,t);return{action:{actionType:"sanity.action.release.create",releaseId:r,metadata:{...n,releaseType:n.releaseType||"undecided"}},options:o}};class Gn{#a;#c;constructor(e,t){this.#a=e,this.#c=t}get({releaseId:e},t){return Wr(this.#a,this.#c,`_.releases.${e}`,t)}create(e,t){const{action:r,options:n}=Wn(e,t),{releaseId:o,metadata:s}=r;return on(this.#a,this.#c,r,n).pipe(rt((e=>({...e,releaseId:o,metadata:s}))))}edit({releaseId:e,patch:t},r){const n={actionType:"sanity.action.release.edit",releaseId:e,patch:t};return on(this.#a,this.#c,n,r)}publish({releaseId:e},t){const r={actionType:"sanity.action.release.publish",releaseId:e};return on(this.#a,this.#c,r,t)}archive({releaseId:e},t){const r={actionType:"sanity.action.release.archive",releaseId:e};return on(this.#a,this.#c,r,t)}unarchive({releaseId:e},t){const r={actionType:"sanity.action.release.unarchive",releaseId:e};return on(this.#a,this.#c,r,t)}schedule({releaseId:e,publishAt:t},r){const n={actionType:"sanity.action.release.schedule",releaseId:e,publishAt:t};return on(this.#a,this.#c,n,r)}unschedule({releaseId:e},t){const r={actionType:"sanity.action.release.unschedule",releaseId:e};return on(this.#a,this.#c,r,t)}delete({releaseId:e},t){const r={actionType:"sanity.action.release.delete",releaseId:e};return on(this.#a,this.#c,r,t)}fetchDocuments({releaseId:e},t){return Jr(this.#a,this.#c,e,t)}}class Jn{#a;#c;constructor(e,t){this.#a=e,this.#c=t}get({releaseId:e},t){return et(Wr(this.#a,this.#c,`_.releases.${e}`,t))}async create(e,t){const{action:r,options:n}=Wn(e,t),{releaseId:o,metadata:s}=r;return{...await et(on(this.#a,this.#c,r,n)),releaseId:o,metadata:s}}edit({releaseId:e,patch:t},r){const n={actionType:"sanity.action.release.edit",releaseId:e,patch:t};return et(on(this.#a,this.#c,n,r))}publish({releaseId:e},t){const r={actionType:"sanity.action.release.publish",releaseId:e};return et(on(this.#a,this.#c,r,t))}archive({releaseId:e},t){const r={actionType:"sanity.action.release.archive",releaseId:e};return et(on(this.#a,this.#c,r,t))}unarchive({releaseId:e},t){const r={actionType:"sanity.action.release.unarchive",releaseId:e};return et(on(this.#a,this.#c,r,t))}schedule({releaseId:e,publishAt:t},r){const n={actionType:"sanity.action.release.schedule",releaseId:e,publishAt:t};return et(on(this.#a,this.#c,n,r))}unschedule({releaseId:e},t){const r={actionType:"sanity.action.release.unschedule",releaseId:e};return et(on(this.#a,this.#c,r,t))}delete({releaseId:e},t){const r={actionType:"sanity.action.release.delete",releaseId:e};return et(on(this.#a,this.#c,r,t))}fetchDocuments({releaseId:e},t){return et(Jr(this.#a,this.#c,e,t))}}class Qn{#a;#c;constructor(e,t){this.#a=e,this.#c=t}getById(e){return dn(this.#a,this.#c,{uri:`/users/${e}`})}}class Yn{#a;#c;constructor(e,t){this.#a=e,this.#c=t}getById(e){return et(dn(this.#a,this.#c,{uri:`/users/${e}`}))}}class Xn{assets;datasets;live;projects;users;agent;releases;#u;#c;listen=An;constructor(e,t=wr){this.config(t),this.#c=e,this.assets=new En(this,this.#c),this.datasets=new Fn(this,this.#c),this.live=new kn(this),this.projects=new Ln(this,this.#c),this.users=new Qn(this,this.#c),this.agent={action:new wn(this,this.#c)},this.releases=new Gn(this,this.#c)}clone(){return new Xn(this.#c,this.config())}config(e){if(void 0===e)return{...this.#u};if(this.#u&&!1===this.#u.allowReconfigure)throw new Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.#u=Ir(e,this.#u||{}),this}withConfig(e){const t=this.config();return new Xn(this.#c,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return Br(this,this.#c,this.#u.stega,e,t,r)}getDocument(e,t){return Wr(this,this.#c,e,t)}getDocuments(e,t){return Gr(this,this.#c,e,t)}create(e,t){return an(this,this.#c,e,"create",t)}createIfNotExists(e,t){return Qr(this,this.#c,e,t)}createOrReplace(e,t){return Yr(this,this.#c,e,t)}createVersion({document:e,publishedId:t,releaseId:r,baseId:n,ifBaseRevisionId:o},s){if(!e)return Kr(this,this.#c,t,n,r,o,s);const i=Bn("createVersion",{document:e,publishedId:t,releaseId:r}),a={...e,_id:i},c=t||Dt(e._id);return Xr(this,this.#c,a,c,s)}delete(e,t){return Zr(this,this.#c,e,t)}discardVersion({releaseId:e,publishedId:t},r,n){const o=Vn(t,e);return en(this,this.#c,o,r,n)}replaceVersion({document:e,publishedId:t,releaseId:r},n){const o=Bn("replaceVersion",{document:e,publishedId:t,releaseId:r}),s={...e,_id:o};return tn(this,this.#c,s,n)}unpublishVersion({releaseId:e,publishedId:t},r){const n=At(t,e);return rn(this,this.#c,n,t,r)}mutate(e,t){return nn(this,this.#c,e,t)}patch(e,t){return new Dr(e,t,this)}transaction(e){return new Nr(e,this)}action(e,t){return on(this,this.#c,e,t)}request(e){return dn(this,this.#c,e)}getUrl(e,t){return fn(this,e,t)}getDataUrl(e,t){return pn(this,e,t)}}class Kn{assets;datasets;live;projects;users;agent;releases;observable;#u;#c;listen=An;constructor(e,t=wr){this.config(t),this.#c=e,this.assets=new In(this,this.#c),this.datasets=new Un(this,this.#c),this.live=new kn(this),this.projects=new zn(this,this.#c),this.users=new Yn(this,this.#c),this.agent={action:new Cn(this,this.#c)},this.releases=new Jn(this,this.#c),this.observable=new Xn(e,t)}clone(){return new Kn(this.#c,this.config())}config(e){if(void 0===e)return{...this.#u};if(this.#u&&!1===this.#u.allowReconfigure)throw new Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(e),this.#u=Ir(e,this.#u||{}),this}withConfig(e){const t=this.config();return new Kn(this.#c,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return et(Br(this,this.#c,this.#u.stega,e,t,r))}getDocument(e,t){return et(Wr(this,this.#c,e,t))}getDocuments(e,t){return et(Gr(this,this.#c,e,t))}create(e,t){return et(an(this,this.#c,e,"create",t))}createIfNotExists(e,t){return et(Qr(this,this.#c,e,t))}createOrReplace(e,t){return et(Yr(this,this.#c,e,t))}createVersion({document:e,publishedId:t,releaseId:r,baseId:n,ifBaseRevisionId:o},s){if(!e)return tt(Kr(this,this.#c,t,n,r,o,s));const i=Bn("createVersion",{document:e,publishedId:t,releaseId:r}),a={...e,_id:i},c=t||Dt(e._id);return tt(Xr(this,this.#c,a,c,s))}delete(e,t){return et(Zr(this,this.#c,e,t))}discardVersion({releaseId:e,publishedId:t},r,n){const o=Vn(t,e);return et(en(this,this.#c,o,r,n))}replaceVersion({document:e,publishedId:t,releaseId:r},n){const o=Bn("replaceVersion",{document:e,publishedId:t,releaseId:r}),s={...e,_id:o};return tt(tn(this,this.#c,s,n))}unpublishVersion({releaseId:e,publishedId:t},r){const n=At(t,e);return et(rn(this,this.#c,n,t,r))}mutate(e,t){return et(nn(this,this.#c,e,t))}patch(e,t){return new kr(e,t,this)}transaction(e){return new Ur(e,this)}action(e,t){return et(on(this,this.#c,e,t))}request(e){return et(dn(this,this.#c,e))}dataRequest(e,t,r){return et(sn(this,this.#c,e,t,r))}getUrl(e,t){return fn(this,e,t)}getDataUrl(e,t){return pn(this,e,t)}}const Zn=(so=Kn,{requester:Yt(oo=[]),createClient:e=>{const t=Yt(oo,{ignoreWarnings:e.ignoreWarnings});return new so(((r,n)=>(n||t)({maxRedirects:0,maxRetries:e.maxRetries,retryDelay:e.retryDelay,...r})),e)}}),eo=Zn.requester,to=Zn.createClient,ro=(no=to,function(e){return vr(),no(e)});var no,oo,so;const io=/_key\s*==\s*['"](.*)['"]/;function ao(e){if(!Array.isArray(e))throw new Error("Path is not an array");return e.reduce(((e,t,r)=>{const n=typeof t;if("number"===n)return`${e}[${t}]`;if("string"===n)return`${e}${0===r?"":"."}${t}`;if(function(e){return"string"==typeof e?io.test(e.trim()):"object"==typeof e&&"_key"in e}(t)&&t._key)return`${e}[_key=="${t._key}"]`;if(Array.isArray(t)){const[r,n]=t;return`${e}[${r}:${n}]`}throw new Error(`Unsupported path segment \`${JSON.stringify(t)}\``)}),"")}const co={"\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","'":"\\'","\\":"\\\\"},uo={"\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t","\\'":"'","\\\\":"\\"};function lo(e){const t=[],r=/\['(.*?)'\]|\[(\d+)\]|\[\?\(@\._key=='(.*?)'\)\]/g;let n;for(;null!==(n=r.exec(e));)if(void 0===n[1])if(void 0===n[2])if(void 0===n[3]);else{const e=n[3].replace(/\\(\\')/g,(e=>uo[e]));t.push({_key:e,_index:-1})}else t.push(parseInt(n[2],10));else{const e=n[1].replace(/\\(\\|f|n|r|t|')/g,(e=>uo[e]));t.push(e)}return t}function ho(e){return e.map((e=>{if("string"==typeof e||"number"==typeof e)return e;if(""!==e._key)return{_key:e._key};if(-1!==e._index)return e._index;throw new Error(`invalid segment:${JSON.stringify(e)}`)}))}function po(e,t){if(!t?.mappings)return;const r=function(e){return`$${e.map((e=>"string"==typeof e?`['${e.replace(/[\f\n\r\t'\\]/g,(e=>co[e]))}']`:"number"==typeof e?`[${e}]`:""!==e._key?`[?(@._key=='${e._key.replace(/['\\]/g,(e=>co[e]))}')]`:`[${e._index}]`)).join("")}`}(e.map((e=>{if("string"==typeof e||"number"==typeof e)return e;if(-1!==e._index)return e._index;throw new Error(`invalid segment:${JSON.stringify(e)}`)})));if(void 0!==t.mappings[r])return{mapping:t.mappings[r],matchedPath:r,pathSuffix:""};const n=Object.entries(t.mappings).filter((([e])=>r.startsWith(e))).sort((([e],[t])=>t.length-e.length));if(0==n.length)return;const[o,s]=n[0];return{mapping:s,matchedPath:o,pathSuffix:r.substring(o.length)}}function fo(e,t,r=[]){if(function(e){return null!==e&&Array.isArray(e)}(e))return e.map(((e,n)=>{if(mt(e)){const o=e._key;if("string"==typeof o)return fo(e,t,r.concat({_key:o,_index:n}))}return fo(e,t,r.concat(n))}));if(mt(e)){if("block"===e._type||"span"===e._type){const n={...e};return"block"===e._type?n.children=fo(e.children,t,r.concat("children")):"span"===e._type&&(n.text=fo(e.text,t,r.concat("text"))),n}return Object.fromEntries(Object.entries(e).map((([e,n])=>[e,fo(n,t,r.concat(e))])))}return t(e,r)}function yo(e,t,r){return fo(e,((e,n)=>{if("string"!=typeof e)return e;const o=po(n,t);if(!o)return e;const{mapping:s,matchedPath:i}=o;if("value"!==s.type||"documentValue"!==s.source.type)return e;const a=t.documents[s.source.document],c=t.paths[s.source.path],u=lo(i),l=lo(c).concat(n.slice(u.length));return r({sourcePath:l,sourceDocument:a,resultPath:n,value:e})}))}const go=".",bo=`drafts${go}`,vo=`versions${go}`;function mo(e){return e.startsWith(bo)}function wo(e){return e.startsWith(vo)}function Co(e){const{baseUrl:t,workspace:r="default",tool:n="default",id:o,type:s,path:i,projectId:a,dataset:c}=e;if(!t)throw new Error("baseUrl is required");if(!i)throw new Error("path is required");if(!o)throw new Error("id is required");if("/"!==t&&t.endsWith("/"))throw new Error("baseUrl must not end with a slash");const u="default"===r?void 0:r,l="default"===n?void 0:n,h=function(e){return wo(e)?e.split(go).slice(2).join(go):mo(e)?e.slice(bo.length):e}(o),d=Array.isArray(i)?ao(ho(i)):i,p=new URLSearchParams({baseUrl:t,id:h,type:s,path:d});if(u&&p.set("workspace",u),l&&p.set("tool",l),a&&p.set("projectId",a),c&&p.set("dataset",c),function(e){return!mo(e)&&!wo(e)}(o))p.set("perspective","published");else if(wo(o)){const e=function(e){if(!wo(e))return;const[t,r,...n]=e.split(go);return r}(o);p.set("perspective",e)}const f=["/"===t?"":t];u&&f.push(u);const y=["mode=presentation",`id=${h}`,`type=${s}`,`path=${encodeURIComponent(d)}`];return l&&y.push(`tool=${l}`),f.push("intent","edit",`${y.join(";")}?${p}`),f.join("/")}const Eo=({sourcePath:e,resultPath:t,value:r})=>{if(/^\d{4}-\d{2}-\d{2}/.test(n=r)&&Date.parse(n)||function(e){try{new URL(e,e.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(r))return!1;var n;const o=e.at(-1);return!("slug"===e.at(-2)&&"current"===o||"string"==typeof o&&(o.startsWith("_")||o.endsWith("Id"))||e.some((e=>"meta"===e||"metadata"===e||"openGraph"===e||"seo"===e))||_o(e)||_o(t)||"string"==typeof o&&Io.has(o))},Io=new Set(["color","colour","currency","email","format","gid","hex","href","hsl","hsla","icon","id","index","key","language","layout","link","linkAction","locale","lqip","page","path","ref","rgb","rgba","route","secret","slug","status","tag","template","theme","type","textTheme","unit","url","username","variant","website"]);function _o(e){return e.some((e=>"string"==typeof e&&null!==e.match(/type/i)))}function xo(e,t,r){const{filter:n,logger:o,enabled:s}=r;if(!s){const n="config.enabled must be true, don't call this function otherwise";throw o?.error?.(`[@sanity/client]: ${n}`,{result:e,resultSourceMap:t,config:r}),new TypeError(n)}if(!t)return o?.error?.("[@sanity/client]: Missing Content Source Map from response body",{result:e,resultSourceMap:t,config:r}),e;if(!r.studioUrl){const n="config.studioUrl must be defined";throw o?.error?.(`[@sanity/client]: ${n}`,{result:e,resultSourceMap:t,config:r}),new TypeError(n)}const i={encoded:[],skipped:[]},a=yo(e,t,(({sourcePath:e,sourceDocument:t,resultPath:s,value:a})=>{if(!1===("function"==typeof n?n({sourcePath:e,resultPath:s,filterDefault:Eo,sourceDocument:t,value:a}):Eo({sourcePath:e,resultPath:s,value:a})))return o&&i.skipped.push({path:Ro(e),value:`${a.slice(0,20)}${a.length>20?"...":""}`,length:a.length}),a;o&&i.encoded.push({path:Ro(e),value:`${a.slice(0,20)}${a.length>20?"...":""}`,length:a.length});const{baseUrl:c,workspace:u,tool:l}=function(e){let t="string"==typeof e?e:e.baseUrl;return"/"!==t&&(t=t.replace(/\/$/,"")),"string"==typeof e?{baseUrl:t}:{...e,baseUrl:t}}("function"==typeof r.studioUrl?r.studioUrl(t):r.studioUrl);if(!c)return a;const{_id:h,_type:d,_projectId:p,_dataset:f}=t;return It(a,{origin:"sanity.io",href:Co({baseUrl:c,workspace:u,tool:l,id:h,type:d,path:e,...!r.omitCrossDatasetReferenceData&&{dataset:f,projectId:p}})},!1)}));if(o){const e=i.skipped.length,t=i.encoded.length;if((e||t)&&((o?.groupCollapsed||o.log)?.("[@sanity/client]: Encoding source map into result"),o.log?.(`[@sanity/client]: Paths encoded: ${i.encoded.length}, skipped: ${i.skipped.length}`)),i.encoded.length>0&&(o?.log?.("[@sanity/client]: Table of encoded paths"),(o?.table||o.log)?.(i.encoded)),i.skipped.length>0){const e=new Set;for(const{path:t}of i.skipped)e.add(t.replace(io,"0").replace(/\[\d+\]/g,"[]"));o?.log?.("[@sanity/client]: List of skipped paths",[...e.values()])}(e||t)&&o?.groupEnd?.()}return a}function Ro(e){return ao(ho(e))}var qo=Object.freeze({__proto__:null,stegaEncodeSourceMap:xo}),$o=Object.freeze({__proto__:null,encodeIntoResult:yo,stegaEncodeSourceMap:xo,stegaEncodeSourceMap$1:qo});function So(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var To,Oo,jo,Ao={exports:{}},Po=Ao.exports;
/** @license
   * eventsource.js
   * Available under MIT License (MIT)
   * https://github.com/Yaffle/EventSource/
   */function Do(){return To||(To=1,function(e,t){!function(r){var n=r.setTimeout,o=r.clearTimeout,s=r.XMLHttpRequest,i=r.XDomainRequest,a=r.ActiveXObject,c=r.EventSource,u=r.document,l=r.Promise,h=r.fetch,d=r.Response,p=r.TextDecoder,f=r.TextEncoder,y=r.AbortController;if("undefined"==typeof window||void 0===u||"readyState"in u||null!=u.body||(u.readyState="loading",window.addEventListener("load",(function(e){u.readyState="complete"}),!1)),null==s&&null!=a&&(s=function(){return new a("Microsoft.XMLHTTP")}),null==Object.create&&(Object.create=function(e){function t(){}return t.prototype=e,new t}),Date.now||(Date.now=function(){return(new Date).getTime()}),null==y){var g=h;h=function(e,t){var r=t.signal;return g(e,{headers:t.headers,credentials:t.credentials,cache:t.cache}).then((function(e){var t=e.body.getReader();return r._reader=t,r._aborted&&r._reader.cancel(),{status:e.status,statusText:e.statusText,headers:e.headers,body:{getReader:function(){return t}}}}))},y=function(){this.signal={_reader:null,_aborted:!1},this.abort=function(){null!=this.signal._reader&&this.signal._reader.cancel(),this.signal._aborted=!0}}}function b(){this.bitsNeeded=0,this.codePoint=0}b.prototype.decode=function(e){function t(e,t,r){if(1===r)return e>=128>>t&&e<<t<=2047;if(2===r)return e>=2048>>t&&e<<t<=55295||e>=57344>>t&&e<<t<=65535;if(3===r)return e>=65536>>t&&e<<t<=1114111;throw new Error}function r(e,t){if(6===e)return t>>6>15?3:t>31?2:1;if(12===e)return t>15?3:2;if(18===e)return 3;throw new Error}for(var n=65533,o="",s=this.bitsNeeded,i=this.codePoint,a=0;a<e.length;a+=1){var c=e[a];0!==s&&(c<128||c>191||!t(i<<6|63&c,s-6,r(s,i)))&&(s=0,i=n,o+=String.fromCharCode(i)),0===s?(c>=0&&c<=127?(s=0,i=c):c>=192&&c<=223?(s=6,i=31&c):c>=224&&c<=239?(s=12,i=15&c):c>=240&&c<=247?(s=18,i=7&c):(s=0,i=n),0===s||t(i,s,r(s,i))||(s=0,i=n)):(s-=6,i=i<<6|63&c),0===s&&(i<=65535?o+=String.fromCharCode(i):(o+=String.fromCharCode(55296+(i-65535-1>>10)),o+=String.fromCharCode(56320+(i-65535-1&1023))))}return this.bitsNeeded=s,this.codePoint=i,o};null!=p&&null!=f&&function(){try{return"test"===(new p).decode((new f).encode("test"),{stream:!0})}catch(e){console.debug("TextDecoder does not support streaming option. Using polyfill instead: "+e)}return!1}()||(p=b);var v=function(){};function m(e){this.withCredentials=!1,this.readyState=0,this.status=0,this.statusText="",this.responseText="",this.onprogress=v,this.onload=v,this.onerror=v,this.onreadystatechange=v,this._contentType="",this._xhr=e,this._sendTimeout=0,this._abort=v}function w(e){return e.replace(/[A-Z]/g,(function(e){return String.fromCharCode(e.charCodeAt(0)+32)}))}function C(e){for(var t=Object.create(null),r=e.split("\r\n"),n=0;n<r.length;n+=1){var o=r[n].split(": "),s=o.shift(),i=o.join(": ");t[w(s)]=i}this._map=t}function E(){}function I(e){this._headers=e}function _(){}function x(){this._listeners=Object.create(null)}function R(e){n((function(){throw e}),0)}function q(e){this.type=e,this.target=void 0}function $(e,t){q.call(this,e),this.data=t.data,this.lastEventId=t.lastEventId}function S(e,t){q.call(this,e),this.status=t.status,this.statusText=t.statusText,this.headers=t.headers}function T(e,t){q.call(this,e),this.error=t.error}m.prototype.open=function(e,t){this._abort(!0);var r=this,i=this._xhr,a=1,c=0;this._abort=function(e){0!==r._sendTimeout&&(o(r._sendTimeout),r._sendTimeout=0),1!==a&&2!==a&&3!==a||(a=4,i.onload=v,i.onerror=v,i.onabort=v,i.onprogress=v,i.onreadystatechange=v,i.abort(),0!==c&&(o(c),c=0),e||(r.readyState=4,r.onabort(null),r.onreadystatechange())),a=0};var u=function(){if(1===a){var e=0,t="",n=void 0;if("contentType"in i)e=200,t="OK",n=i.contentType;else try{e=i.status,t=i.statusText,n=i.getResponseHeader("Content-Type")}catch(r){e=0,t="",n=void 0}0!==e&&(a=2,r.readyState=2,r.status=e,r.statusText=t,r._contentType=n,r.onreadystatechange())}},l=function(){if(u(),2===a||3===a){a=3;var e="";try{e=i.responseText}catch(e){}r.readyState=3,r.responseText=e,r.onprogress()}},h=function(e,t){if(null!=t&&null!=t.preventDefault||(t={preventDefault:v}),l(),1===a||2===a||3===a){if(a=4,0!==c&&(o(c),c=0),r.readyState=4,"load"===e)r.onload(t);else if("error"===e)r.onerror(t);else{if("abort"!==e)throw new TypeError;r.onabort(t)}r.onreadystatechange()}},d=function(){c=n((function(){d()}),500),3===i.readyState&&l()};"onload"in i&&(i.onload=function(e){h("load",e)}),"onerror"in i&&(i.onerror=function(e){h("error",e)}),"onabort"in i&&(i.onabort=function(e){h("abort",e)}),"onprogress"in i&&(i.onprogress=l),"onreadystatechange"in i&&(i.onreadystatechange=function(e){!function(e){null!=i&&(4===i.readyState?"onload"in i&&"onerror"in i&&"onabort"in i||h(""===i.responseText?"error":"load",e):3===i.readyState?"onprogress"in i||l():2===i.readyState&&u())}(e)}),!("contentType"in i)&&"ontimeout"in s.prototype||(t+=(-1===t.indexOf("?")?"?":"&")+"padding=true"),i.open(e,t,!0),"readyState"in i&&(c=n((function(){d()}),0))},m.prototype.abort=function(){this._abort(!1)},m.prototype.getResponseHeader=function(e){return this._contentType},m.prototype.setRequestHeader=function(e,t){var r=this._xhr;"setRequestHeader"in r&&r.setRequestHeader(e,t)},m.prototype.getAllResponseHeaders=function(){return null!=this._xhr.getAllResponseHeaders&&this._xhr.getAllResponseHeaders()||""},m.prototype.send=function(){if("ontimeout"in s.prototype&&("sendAsBinary"in s.prototype||"mozAnon"in s.prototype)||null==u||null==u.readyState||"complete"===u.readyState){var e=this._xhr;"withCredentials"in e&&(e.withCredentials=this.withCredentials);try{e.send(void 0)}catch(e){throw e}}else{var t=this;t._sendTimeout=n((function(){t._sendTimeout=0,t.send()}),4)}},C.prototype.get=function(e){return this._map[w(e)]},null!=s&&null==s.HEADERS_RECEIVED&&(s.HEADERS_RECEIVED=2),E.prototype.open=function(e,t,r,n,o,i,a){e.open("GET",o);var c=0;for(var u in e.onprogress=function(){var t=e.responseText.slice(c);c+=t.length,r(t)},e.onerror=function(e){e.preventDefault(),n(new Error("NetworkError"))},e.onload=function(){n(null)},e.onabort=function(){n(null)},e.onreadystatechange=function(){if(e.readyState===s.HEADERS_RECEIVED){var r=e.status,n=e.statusText,o=e.getResponseHeader("Content-Type"),i=e.getAllResponseHeaders();t(r,n,o,new C(i))}},e.withCredentials=i,a)Object.prototype.hasOwnProperty.call(a,u)&&e.setRequestHeader(u,a[u]);return e.send(),e},I.prototype.get=function(e){return this._headers.get(e)},_.prototype.open=function(e,t,r,n,o,s,i){var a=null,c=new y,u=c.signal,d=new p;return h(o,{headers:i,credentials:s?"include":"same-origin",signal:u,cache:"no-store"}).then((function(e){return a=e.body.getReader(),t(e.status,e.statusText,e.headers.get("Content-Type"),new I(e.headers)),new l((function(e,t){var n=function(){a.read().then((function(t){if(t.done)e(void 0);else{var o=d.decode(t.value,{stream:!0});r(o),n()}})).catch((function(e){t(e)}))};n()}))})).catch((function(e){return"AbortError"===e.name?void 0:e})).then((function(e){n(e)})),{abort:function(){null!=a&&a.cancel(),c.abort()}}},x.prototype.dispatchEvent=function(e){e.target=this;var t=this._listeners[e.type];if(null!=t)for(var r=t.length,n=0;n<r;n+=1){var o=t[n];try{"function"==typeof o.handleEvent?o.handleEvent(e):o.call(this,e)}catch(e){R(e)}}},x.prototype.addEventListener=function(e,t){e=String(e);var r=this._listeners,n=r[e];null==n&&(n=[],r[e]=n);for(var o=!1,s=0;s<n.length;s+=1)n[s]===t&&(o=!0);o||n.push(t)},x.prototype.removeEventListener=function(e,t){e=String(e);var r=this._listeners,n=r[e];if(null!=n){for(var o=[],s=0;s<n.length;s+=1)n[s]!==t&&o.push(n[s]);0===o.length?delete r[e]:r[e]=o}},$.prototype=Object.create(q.prototype),S.prototype=Object.create(q.prototype),T.prototype=Object.create(q.prototype);var O=-1,j=-1,A=/^text\/event\-stream(;.*)?$/i,P=function(e,t){var r=null==e?t:parseInt(e,10);return r!=r&&(r=t),D(r)},D=function(e){return Math.min(Math.max(e,1e3),18e6)},k=function(e,t,r){try{"function"==typeof t&&t.call(e,r)}catch(e){R(e)}};function M(e,t){x.call(this),t=t||{},this.onopen=void 0,this.onmessage=void 0,this.onerror=void 0,this.url=void 0,this.readyState=void 0,this.withCredentials=void 0,this.headers=void 0,this._close=void 0,function(e,t,r){t=String(t);var a=Boolean(r.withCredentials),c=r.lastEventIdQueryParameterName||"lastEventId",u=D(1e3),l=P(r.heartbeatTimeout,45e3),h="",d=u,p=!1,f=0,y=r.headers||{},g=r.Transport,b=F&&null==g?void 0:new m(null!=g?new g:null!=s&&"withCredentials"in s.prototype||null==i?new s:new i),v=null!=g&&"string"!=typeof g?new g:null==b?new _:new E,w=void 0,C=0,I=O,x="",R="",q="",M="",U=0,N=0,L=0,z=function(t,r,n,o){if(0===I)if(200===t&&null!=n&&A.test(n)){I=1,p=Date.now(),d=u,e.readyState=1;var s=new S("open",{status:t,statusText:r,headers:o});e.dispatchEvent(s),k(e,e.onopen,s)}else{var i="";200!==t?(r&&(r=r.replace(/\s+/g," ")),i="EventSource's response has a status "+t+" "+r+" that is not 200. Aborting the connection."):i="EventSource's response has a Content-Type specifying an unsupported type: "+(null==n?"-":n.replace(/\s+/g," "))+". Aborting the connection.",B();s=new S("error",{status:t,statusText:r,headers:o});e.dispatchEvent(s),k(e,e.onerror,s),console.error(i)}},H=function(t){if(1===I){for(var r=-1,s=0;s<t.length;s+=1){(c=t.charCodeAt(s))!=="\n".charCodeAt(0)&&c!=="\r".charCodeAt(0)||(r=s)}var i=(-1!==r?M:"")+t.slice(0,r+1);M=(-1===r?M:"")+t.slice(r+1),""!==t&&(p=Date.now(),f+=t.length);for(var a=0;a<i.length;a+=1){var c=i.charCodeAt(a);if(U===j&&c==="\n".charCodeAt(0))U=0;else if(U===j&&(U=0),c==="\r".charCodeAt(0)||c==="\n".charCodeAt(0)){if(0!==U){1===U&&(L=a+1);var y=i.slice(N,L-1),g=i.slice(L+(L<a&&i.charCodeAt(L)===" ".charCodeAt(0)?1:0),a);"data"===y?(x+="\n",x+=g):"id"===y?R=g:"event"===y?q=g:"retry"===y?(u=P(g,u),d=u):"heartbeatTimeout"===y&&(l=P(g,l),0!==C&&(o(C),C=n((function(){W()}),l)))}if(0===U){if(""!==x){h=R,""===q&&(q="message");var b=new $(q,{data:x.slice(1),lastEventId:R});if(e.dispatchEvent(b),"open"===q?k(e,e.onopen,b):"message"===q?k(e,e.onmessage,b):"error"===q&&k(e,e.onerror,b),2===I)return}x="",q=""}U=c==="\r".charCodeAt(0)?j:0}else 0===U&&(N=a,U=1),1===U?c===":".charCodeAt(0)&&(L=a+1,U=2):2===U&&(U=3)}}},V=function(t){if(1===I||0===I){I=O,0!==C&&(o(C),C=0),C=n((function(){W()}),d),d=D(Math.min(16*u,2*d)),e.readyState=0;var r=new T("error",{error:t});e.dispatchEvent(r),k(e,e.onerror,r),null!=t&&console.error(t)}},B=function(){I=2,null!=w&&(w.abort(),w=void 0),0!==C&&(o(C),C=0),e.readyState=2},W=function(){if(C=0,I===O){p=!1,f=0,C=n((function(){W()}),l),I=0,x="",q="",R=h,M="",N=0,L=0,U=0;var r=t;if("data:"!==t.slice(0,5)&&"blob:"!==t.slice(0,5)&&""!==h){var o=t.indexOf("?");r=-1===o?t:t.slice(0,o+1)+t.slice(o+1).replace(/(?:^|&)([^=&]*)(?:=[^&]*)?/g,(function(e,t){return t===c?"":e})),r+=(-1===t.indexOf("?")?"?":"&")+c+"="+encodeURIComponent(h)}var s=e.withCredentials,i={Accept:"text/event-stream"},a=e.headers;if(null!=a)for(var u in a)Object.prototype.hasOwnProperty.call(a,u)&&(i[u]=a[u]);try{w=v.open(b,z,H,V,r,s,i)}catch(e){throw B(),e}}else if(p||null==w){var d=Math.max((p||Date.now())+l-Date.now(),1);p=!1,C=n((function(){W()}),d)}else V(new Error("No activity within "+l+" milliseconds. "+(0===I?"No response received.":f+" chars received.")+" Reconnecting.")),null!=w&&(w.abort(),w=void 0)};e.url=t,e.readyState=0,e.withCredentials=a,e.headers=y,e._close=B,W()}(this,e,t)}var F=null!=h&&null!=d&&"body"in d.prototype;M.prototype=Object.create(x.prototype),M.prototype.CONNECTING=0,M.prototype.OPEN=1,M.prototype.CLOSED=2,M.prototype.close=function(){this._close()},M.CONNECTING=0,M.OPEN=1,M.CLOSED=2,M.prototype.withCredentials=void 0;var U=c;null==s||null!=c&&"withCredentials"in c.prototype||(U=M),function(){var r=function(e){e.EventSourcePolyfill=M,e.NativeEventSource=c,e.EventSource=U}(t);void 0!==r&&(e.exports=r)}()}("undefined"==typeof globalThis?"undefined"!=typeof window?window:"undefined"!=typeof self?self:Po:globalThis)}(Ao,Ao.exports)),Ao.exports}function ko(){return jo?Oo:(jo=1,Oo=Do().EventSourcePolyfill)}var Mo=ko(),Fo=t({__proto__:null,default:So(Mo)},[Mo]);e.BasePatch=Pr,e.BaseTransaction=Fr,e.ChannelError=Rr,e.ClientError=Nt,e.ConnectionFailedError=_r,e.CorsOriginError=Gt,e.DisconnectError=xr,e.EXPERIMENTAL_API_WARNING="This is an experimental API version",e.MessageError=qr,e.MessageParseError=$r,e.ObservablePatch=Dr,e.ObservableSanityClient=Xn,e.ObservableTransaction=Nr,e.Patch=kr,e.SanityClient=Kn,e.ServerError=Lt,e.Transaction=Ur,e.connectEventSource=Tr,e.createClient=to,e.default=ro,e.formatQueryParseError=Vt,e.isHttpError=function(e){if(!mt(e))return!1;const t=e.response;return!("number"!=typeof e.statusCode||"string"!=typeof e.message||!mt(t)||typeof t.body>"u"||"string"!=typeof t.url||"string"!=typeof t.method||"object"!=typeof t.headers||"number"!=typeof t.statusCode)},e.isQueryParseError=Ht,e.requester=eo,e.unstable__adapter=b,e.unstable__environment="browser",e.validateApiPerspective=Er,Object.defineProperty(e,"__esModule",{value:!0})}));
