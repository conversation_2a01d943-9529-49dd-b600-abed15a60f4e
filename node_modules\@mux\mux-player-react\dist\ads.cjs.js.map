{"version": 3, "sources": ["../src/ads.tsx", "../src/useComposedRefs.ts", "../src/useEventCallbackEffect.ts"], "sourcesContent": ["'use client';\nimport React, { forwardRef, useRef } from 'react';\n// Register <mux-player> (ads) web component.\nimport '@mux/mux-player/ads';\nimport MuxPlayer from '@mux/mux-player-react';\nimport type { GenericEventListener, Props as MuxPlayerIndexProps } from '@mux/mux-player-react';\nimport { useComposedRefs } from './useComposedRefs';\nimport { useEventCallbackEffect } from './useEventCallbackEffect';\nimport type MuxPlayerElement from '@mux/mux-player/ads';\nimport type { EventMap as MuxPlayerElementEventMap } from '@mux/mux-player/ads';\n\nexport interface MuxPlayerProps extends Omit<MuxPlayerIndexProps, 'playerSoftwareVersion' | 'playerSoftwareName'> {\n  adTagUrl?: string;\n  allowAdBlocker?: boolean;\n\n  onAdRequest?: GenericEventListener<MuxPlayerElementEventMap['adrequest']>;\n  onAdResponse?: GenericEventListener<MuxPlayerElementEventMap['adresponse']>;\n  onAdImpression?: GenericEventListener<MuxPlayerElementEventMap['adimpression']>;\n  onAdBreakStart?: GenericEventListener<MuxPlayerElementEventMap['adbreakstart']>;\n  onAdPlay?: GenericEventListener<MuxPlayerElementEventMap['adplay']>;\n  onAdPlaying?: GenericEventListener<MuxPlayerElementEventMap['adplaying']>;\n  onAdPause?: GenericEventListener<MuxPlayerElementEventMap['adpause']>;\n  onAdFirstQuartile?: GenericEventListener<MuxPlayerElementEventMap['adfirstquartile']>;\n  onAdMidpoint?: GenericEventListener<MuxPlayerElementEventMap['admidpoint']>;\n  onAdThirdQuartile?: GenericEventListener<MuxPlayerElementEventMap['adthirdquartile']>;\n  onAdError?: GenericEventListener<MuxPlayerElementEventMap['aderror']>;\n  onAdClick?: GenericEventListener<MuxPlayerElementEventMap['adclick']>;\n  onAdSkip?: GenericEventListener<MuxPlayerElementEventMap['adskip']>;\n  onAdEnded?: GenericEventListener<MuxPlayerElementEventMap['adended']>;\n  onAdBreakEnd?: GenericEventListener<MuxPlayerElementEventMap['adbreakend']>;\n  onAdClose?: GenericEventListener<MuxPlayerElementEventMap['adclose']>;\n}\n\nconst MuxPlayerAds = forwardRef<MuxPlayerElement, MuxPlayerProps>((props, ref) => {\n  const playerRef = useRef<MuxPlayerElement>(null);\n\n  const adEventProps: Record<string, (e: Event) => void> = {};\n  const reactProps: Record<string, unknown> = {};\n\n  for (const [k, v] of Object.entries(props)) {\n    if (k.startsWith('onAd')) {\n      adEventProps[k] = v;\n    } else {\n      reactProps[k] = v;\n    }\n  }\n\n  // Set up event listeners on the custom element.\n  // Still handle events for React 19+ because they don't yet offer\n  // a way to have nicely camelCased event prop names on custom elements.\n  for (const propName in adEventProps) {\n    const callback = adEventProps[propName as keyof typeof adEventProps];\n    const eventName = propName.slice(2).toLowerCase() as keyof MuxPlayerElementEventMap;\n    useEventCallbackEffect<MuxPlayerElement, MuxPlayerElementEventMap>(eventName, playerRef, callback);\n  }\n\n  return <MuxPlayer ref={useComposedRefs(playerRef, ref)} {...reactProps} />;\n});\n\nexport default MuxPlayerAds;\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T): (() => void) | void | undefined {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n", "import React, { useEffect } from 'react';\nimport type { GenericEventListener } from './index';\n\nexport const useEventCallbackEffect = <\n  TElement extends EventTarget = EventTarget,\n  TEventMap extends Record<string, Event> = Record<string, Event>,\n  K extends keyof TEventMap = keyof TEventMap,\n>(\n  type: K,\n  ref: // | ((instance: EventTarget | null) => void)\n  React.MutableRefObject<TElement | null> | null | undefined,\n  callback: GenericEventListener<TEventMap[K]> | undefined\n) => {\n  return useEffect(() => {\n    const eventTarget = ref?.current;\n    if (!eventTarget || !callback) return;\n\n    // Type assertion needed because TypeScript can't infer the exact event type\n    const eventName = type as string;\n    const listener = callback as EventListener;\n\n    eventTarget.addEventListener(eventName, listener);\n    return () => {\n      eventTarget.removeEventListener(eventName, listener);\n    };\n  }, [ref?.current, callback, type]);\n};\n"], "mappings": "ukBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GACA,IAAAI,EAA0C,oBAE1CC,EAAO,+BACPC,EAAsB,oCCJtB,IAAAC,EAAuB,oBAQvB,SAASC,EAAUC,EAAqBC,EAA2C,CACjF,GAAI,OAAOD,GAAQ,WACjB,OAAOA,EAAIC,CAAK,EACPD,GAAQ,OAChBA,EAAkC,QAAUC,EAEjD,CAMA,SAASC,KAAkBC,EAA8C,CACvE,OAAQC,GAAS,CACf,IAAIC,EAAa,GACXC,EAAWH,EAAK,IAAKH,GAAQ,CACjC,IAAMO,EAAUR,EAAOC,EAAKI,CAAI,EAChC,MAAI,CAACC,GAAc,OAAOE,GAAW,aACnCF,EAAa,IAERE,CACT,CAAC,EAMD,GAAIF,EACF,MAAO,IAAM,CACX,QAASG,EAAI,EAAGA,EAAIF,EAAS,OAAQE,IAAK,CACxC,IAAMD,EAAUD,EAASE,CAAC,EACtB,OAAOD,GAAW,WACpBA,EAAQ,EAERR,EAAOI,EAAKK,CAAC,EAAG,IAAI,CAExB,CACF,CAEJ,CACF,CAMA,SAASC,KAAsBN,EAA8C,CAE3E,OAAa,cAAYD,EAAY,GAAGC,CAAI,EAAGA,CAAI,CACrD,CCzDA,IAAAO,EAAiC,iBAGpBC,EAAyB,CAKpCC,EACAC,EAEAC,OAEO,aAAU,IAAM,CACrB,IAAMC,EAAcF,GAAA,YAAAA,EAAK,QACzB,GAAI,CAACE,GAAe,CAACD,EAAU,OAG/B,IAAME,EAAYJ,EACZK,EAAWH,EAEjB,OAAAC,EAAY,iBAAiBC,EAAWC,CAAQ,EACzC,IAAM,CACXF,EAAY,oBAAoBC,EAAWC,CAAQ,CACrD,CACF,EAAG,CAACJ,GAAA,YAAAA,EAAK,QAASC,EAAUF,CAAI,CAAC,EFQnC,IAAMM,KAAe,cAA6C,CAACC,EAAOC,IAAQ,CAChF,IAAMC,KAAY,UAAyB,IAAI,EAEzCC,EAAmD,CAAC,EACpDC,EAAsC,CAAC,EAE7C,OAAW,CAACC,EAAGC,CAAC,IAAK,OAAO,QAAQN,CAAK,EACnCK,EAAE,WAAW,MAAM,EACrBF,EAAaE,CAAC,EAAIC,EAElBF,EAAWC,CAAC,EAAIC,EAOpB,QAAWC,KAAYJ,EAAc,CACnC,IAAMK,EAAWL,EAAaI,CAAqC,EAC7DE,EAAYF,EAAS,MAAM,CAAC,EAAE,YAAY,EAChDG,EAAmED,EAAWP,EAAWM,CAAQ,CACnG,CAEA,OAAO,EAAAG,QAAA,cAAC,EAAAC,QAAA,CAAU,IAAKC,EAAgBX,EAAWD,CAAG,EAAI,GAAGG,EAAY,CAC1E,CAAC,EAEMU,EAAQf", "names": ["ads_exports", "__export", "ads_default", "__toCommonJS", "import_react", "import_ads", "import_mux_player_react", "React", "setRef", "ref", "value", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "cleanup", "i", "useComposedRefs", "import_react", "useEventCallbackEffect", "type", "ref", "callback", "eventTarget", "eventName", "listener", "MuxPlayerAds", "props", "ref", "playerRef", "adEventProps", "reactProps", "k", "v", "propName", "callback", "eventName", "useEventCallbackEffect", "React", "MuxPlayer", "useComposedRefs", "ads_default"]}