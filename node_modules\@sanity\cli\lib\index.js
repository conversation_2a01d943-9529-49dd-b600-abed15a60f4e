"use strict";
Object.defineProperty(exports, "__esModule", { value: !0 });
var client = require("@sanity/client"), getCliConfig = require("./_chunks-cjs/getCliConfig.js"), loadEnv = require("./_chunks-cjs/loadEnv.js");
function getCliClientImpl(options = {}) {
  if (typeof process != "object")
    throw new Error("getCliClient() should only be called from node.js scripts");
  const {
    // eslint-disable-next-line no-process-env
    cwd = process.env.SANITY_BASE_PATH || process.cwd(),
    useCdn = !1,
    apiVersion = "2022-06-06",
    projectId,
    dataset,
    token = getCliClient.__internal__getToken()
  } = options;
  if (projectId && dataset)
    return client.createClient({ projectId, dataset, apiVersion, useCdn, token });
  const rootDir = loadEnv.resolveRootDir(cwd), { config } = getCliConfig.getCliConfigSync(rootDir) || {};
  if (!config)
    throw new Error("Unable to resolve CLI configuration");
  const apiConfig = config?.api || {};
  if (!apiConfig.projectId || !apiConfig.dataset)
    throw new Error("Unable to resolve project ID/dataset from CLI configuration");
  return client.createClient({
    projectId: apiConfig.projectId,
    dataset: apiConfig.dataset,
    apiVersion,
    useCdn,
    token
  });
}
getCliClientImpl.__internal__getToken = () => {
};
const getCliClient = getCliClientImpl;
function defineCliConfig(config) {
  return config;
}
function createCliConfig(config) {
  return config;
}
exports.loadEnv = loadEnv.loadEnv;
exports.createCliConfig = createCliConfig;
exports.defineCliConfig = defineCliConfig;
exports.getCliClient = getCliClient;
//# sourceMappingURL=index.js.map
