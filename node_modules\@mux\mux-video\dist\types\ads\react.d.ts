import MuxVideoElement from '@mux/mux-video/ads';
import React from 'react';
declare const _default: React.ForwardRefExoticComponent<Omit<React.HTMLAttributes<MuxVideoElement>, "play" | "pause" | "seeking" | "error" | "ended" | "height" | "width" | "type" | "debug" | "buffered" | "disableCookies" | "errorTranslator" | "beaconCollectionDomain" | "muted" | "preload" | "metadata" | "src" | "seekable" | "autoplay" | "controls" | "crossOrigin" | "currentSrc" | "currentTime" | "defaultMuted" | "defaultPlaybackRate" | "disableRemotePlayback" | "duration" | "loop" | "mediaKeys" | "networkState" | "onencrypted" | "onwaitingforkey" | "paused" | "playbackRate" | "played" | "preservesPitch" | "readyState" | "remote" | "sinkId" | "srcObject" | "textTracks" | "volume" | "addTextTrack" | "canPlayType" | "fastSeek" | "load" | "setMediaKeys" | "setSinkId" | "NETWORK_EMPTY" | "NETWORK_IDLE" | "NETWORK_LOADING" | "NETWORK_NO_SOURCE" | "HAVE_NOTHING" | "HAVE_METADATA" | "HAVE_CURRENT_DATA" | "HAVE_FUTURE_DATA" | "HAVE_ENOUGH_DATA" | "mux" | "getVideoPlaybackQuality" | "audioTracks" | "chapters" | "customDomain" | "_hlsConfig" | "disableTracking" | "drmToken" | "playbackToken" | "envKey" | "liveEdgeStart" | "maxResolution" | "minResolution" | "playbackId" | "playerInitTime" | "preferCmcd" | "preferPlayback" | "programStartTime" | "programEndTime" | "assetStartTime" | "assetEndTime" | "renditionOrder" | "startTime" | "streamType" | "targetLiveWindow" | "tokens" | "playerSoftwareName" | "playerSoftwareVersion" | "muxDataSDK" | "muxDataSDKOptions" | "muxDataKeepSession" | "poster" | "logo" | "liveEdgeOffset" | "_hls" | "playsInline" | "disablePictureInPicture" | "muxCastCustomData" | "castCustomData" | "addCuePoints" | "activeCuePoint" | "cuePoints" | "addChapters" | "activeChapter" | "getStartDate" | "currentPdt" | "unload" | "updateLogo" | "handleEvent" | "nativeEl" | "init" | "onenterpictureinpicture" | "onleavepictureinpicture" | "videoHeight" | "videoWidth" | "cancelVideoFrameCallback" | "requestPictureInPicture" | "requestVideoFrameCallback" | "videoTracks" | "addVideoTrack" | "addAudioTrack" | "removeVideoTrack" | "removeAudioTrack" | "videoRenditions" | "audioRenditions" | "allowAdBlocker" | "adTagUrl" | "ad" | "adsLoader"> & {} & Partial<Omit<MuxVideoElement, keyof HTMLElement | "connectedCallback" | "disconnectedCallback" | "attributeChangedCallback" | "adoptedCallback">> & React.RefAttributes<MuxVideoElement>>;
export default _default;
