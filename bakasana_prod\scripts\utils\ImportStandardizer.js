/**
 * Import Standardizer Utility
 * Standardizes import patterns across the codebase
 */

const fs = require('fs');
const path = require('path');

const FileProcessor = require('./FileProcessor');
const Logger = require('./Logger');

class ImportStandardizer extends FileProcessor {
  constructor() {
    super();
    this.logger = new Logger('ImportStandardizer');
    this.fixes = [];
    this.importOrder = [
      // 1. React and Next.js
      /^(react|next)/,
      // 2. External libraries
      /^[^@.]/,
      // 3. Internal @/ imports - UI components first
      /^@\/components\/ui/,
      // 4. Other @/ components
      /^@\/components/,
      // 5. Other @/ imports (hooks, lib, data)
      /^@\//,
      // 6. Relative imports
      /^\./,
    ];
  }

  /**
   * Standardize imports in a file
   */
  standardizeImports(filePath, content) {
    const lines = content.split('\n');
    const imports = [];
    const nonImports = [];
    let inImportSection = true;

    // Separate imports from other code
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line.startsWith('import ') || line.startsWith('export ')) {
        if (inImportSection) {
          imports.push({ line: lines[i], index: i, original: line });
        } else {
          // Import found after non-import code - this needs fixing
          imports.push({
            line: lines[i],
            index: i,
            original: line,
            misplaced: true,
          });
        }
      } else if (line && !line.startsWith('//') && !line.startsWith('/*')) {
        inImportSection = false;
        nonImports.push({ line: lines[i], index: i });
      } else {
        // Comments and empty lines
        if (inImportSection) {
          imports.push({
            line: lines[i],
            index: i,
            original: line,
            isComment: true,
          });
        } else {
          nonImports.push({ line: lines[i], index: i });
        }
      }
    }

    // Sort imports according to our standard order
    const sortedImports = this.sortImports(
      imports.filter(imp => !imp.isComment)
    );
    const comments = imports.filter(imp => imp.isComment);

    // Check if reordering is needed
    const needsReordering = this.needsImportReordering(imports, sortedImports);

    if (needsReordering) {
      const newContent = this.reconstructFile(
        sortedImports,
        comments,
        nonImports,
        lines
      );
      this.fixes.push({
        file: filePath,
        type: 'import-order',
        description: 'Reordered imports according to standard',
      });
      return newContent;
    }

    return null; // No changes needed
  }

  /**
   * Sort imports according to standard order
   */
  sortImports(imports) {
    return imports.sort((a, b) => {
      const aOrder = this.getImportOrder(a.original);
      const bOrder = this.getImportOrder(b.original);

      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }

      // Within same category, sort alphabetically
      return a.original.localeCompare(b.original);
    });
  }

  /**
   * Get import order category
   */
  getImportOrder(importLine) {
    const match = importLine.match(/from ['"]([^'"]+)['"]/);
    if (!match) return 999; // Unknown imports go last

    const moduleName = match[1];

    for (let i = 0; i < this.importOrder.length; i++) {
      if (this.importOrder[i].test(moduleName)) {
        return i;
      }
    }

    return 999; // Default to last
  }

  /**
   * Check if imports need reordering
   */
  needsImportReordering(original, sorted) {
    const originalOrder = original
      .filter(imp => !imp.isComment && imp.original.includes('from'))
      .map(imp => imp.original);

    const sortedOrder = sorted
      .filter(imp => imp.original.includes('from'))
      .map(imp => imp.original);

    return JSON.stringify(originalOrder) !== JSON.stringify(sortedOrder);
  }

  /**
   * Reconstruct file with sorted imports
   */
  reconstructFile(sortedImports, comments, nonImports, originalLines) {
    const newLines = [];

    // Add file header comments
    const headerComments = comments.filter(c => c.index < 5);
    headerComments.forEach(comment => {
      newLines.push(comment.line);
    });

    // Add sorted imports with proper spacing
    let lastCategory = -1;
    sortedImports.forEach(imp => {
      const currentCategory = this.getImportOrder(imp.original);

      // Add blank line between different categories
      if (lastCategory !== -1 && currentCategory !== lastCategory) {
        newLines.push('');
      }

      newLines.push(imp.line);
      lastCategory = currentCategory;
    });

    // Add blank line before non-import code
    if (sortedImports.length > 0 && nonImports.length > 0) {
      newLines.push('');
    }

    // Add rest of the file
    nonImports.forEach(item => {
      newLines.push(item.line);
    });

    return newLines.join('\n');
  }

  /**
   * Fix common import issues
   */
  fixImportIssues(filePath, content) {
    let fixed = content;
    let hasChanges = false;

    // Fix duplicate imports
    const duplicateRegex =
      /import\s+.*from\s+['"]([^'"]+)['"]\s*;\s*import\s+.*from\s+['"]([^'"]+)['"]/g;
    if (duplicateRegex.test(fixed)) {
      // This is complex - for now just log it
      this.logger.warn(`Potential duplicate imports in ${filePath}`);
    }

    // Fix missing semicolons
    fixed = fixed.replace(/^(import\s+.*from\s+['"][^'"]+['"])\s*$/gm, '$1;');
    if (fixed !== content) {
      hasChanges = true;
      this.fixes.push({
        file: filePath,
        type: 'missing-semicolon',
        description: 'Added missing semicolons to imports',
      });
    }

    // Fix spacing around braces
    fixed = fixed.replace(
      /import\s*\{\s*([^}]+)\s*\}\s*from/g,
      'import { $1 } from'
    );
    if (fixed !== content && !hasChanges) {
      hasChanges = true;
      this.fixes.push({
        file: filePath,
        type: 'import-spacing',
        description: 'Fixed spacing in import statements',
      });
    }

    return hasChanges ? fixed : null;
  }

  /**
   * Process all files and standardize imports
   */
  async standardizeAllImports() {
    this.logger.section('Import Standardization');

    const files = this.getAllFiles(this.srcPath, [
      '.js',
      '.jsx',
      '.ts',
      '.tsx',
    ]);
    this.logger.info(`Processing ${files.length} files...`);

    const results = await this.processFiles(
      files,
      async (filePath, content) => {
        // First fix basic import issues
        const fixedContent = this.fixImportIssues(filePath, content);

        // Then standardize import order
        const standardizedContent = this.standardizeImports(
          filePath,
          fixedContent || content
        );

        if (standardizedContent || fixedContent) {
          const finalContent = standardizedContent || fixedContent;
          this.safeWriteFile(filePath, finalContent);
          return 'fixed';
        }

        return 'no-changes';
      },
      {
        onProgress: (current, total, file) => {
          this.logger.progress(
            current,
            total,
            path.relative(this.srcPath, file)
          );
        },
      }
    );

    // Generate report
    this.generateReport(results);
  }

  /**
   * Generate standardization report
   */
  generateReport(results) {
    const stats = this.getStats();
    const fixedFiles = results.filter(r => r.result === 'fixed').length;

    this.logger.subsection('Standardization Results');
    this.logger.info(`Files processed: ${stats.totalFiles}`);
    this.logger.info(`Files fixed: ${fixedFiles}`);
    this.logger.info(`Success rate: ${stats.successRate}%`);

    if (this.fixes.length > 0) {
      this.logger.subsection('Applied Fixes');

      const fixesByType = {};
      this.fixes.forEach(fix => {
        if (!fixesByType[fix.type]) {
          fixesByType[fix.type] = [];
        }
        fixesByType[fix.type].push(fix);
      });

      Object.entries(fixesByType).forEach(([type, fixes]) => {
        this.logger.info(`${type}: ${fixes.length} files`);
        fixes.slice(0, 3).forEach(fix => {
          this.logger.debug(`  - ${fix.file}`);
        });
        if (fixes.length > 3) {
          this.logger.debug(`  ... and ${fixes.length - 3} more`);
        }
      });
    }

    // Save detailed report
    const reportPath = path.join(
      this.projectRoot,
      'import-standardization-report.json'
    );
    const report = {
      timestamp: new Date().toISOString(),
      stats,
      fixes: this.fixes,
      summary: {
        totalFiles: stats.totalFiles,
        fixedFiles,
        successRate: stats.successRate,
      },
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.logger.success(`Report saved to: ${reportPath}`);
  }
}

module.exports = ImportStandardizer;
