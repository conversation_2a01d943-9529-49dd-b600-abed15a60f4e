{"version": 3, "file": "index.js", "sources": ["../src/cliClient.ts", "../src/config.ts"], "sourcesContent": ["import {createClient, type SanityClient} from '@sanity/client'\n\nimport {getCliConfigSync} from './util/getCliConfig'\nimport {resolveRootDir} from './util/resolveRootDir'\n\nexport interface CliClientOptions {\n  cwd?: string\n\n  projectId?: string\n  dataset?: string\n  useCdn?: boolean\n  token?: string\n  apiVersion?: string\n}\n\ninterface GetCliClient {\n  (options?: CliClientOptions): SanityClient\n  /**\n   * @internal\n   * @deprecated This is only for INTERNAL use, and should not be relied upon outside of official Sanity modules\n   * @returns A token to use when constructing a client without a `token` explicitly defined, or undefined\n   */\n  __internal__getToken: () => string | undefined\n}\n\nfunction getCliClientImpl(options: CliClientOptions = {}): SanityClient {\n  if (typeof process !== 'object') {\n    throw new Error('getCliClient() should only be called from node.js scripts')\n  }\n\n  const {\n    // eslint-disable-next-line no-process-env\n    cwd = process.env.SANITY_BASE_PATH || process.cwd(),\n    useCdn = false,\n    apiVersion = '2022-06-06',\n    projectId,\n    dataset,\n    token = getCliClient.__internal__getToken(),\n  } = options\n\n  if (projectId && dataset) {\n    return createClient({projectId, dataset, apiVersion, useCdn, token})\n  }\n\n  const rootDir = resolveRootDir(cwd)\n  const {config} = getCliConfigSync(rootDir) || {}\n  if (!config) {\n    throw new Error('Unable to resolve CLI configuration')\n  }\n\n  const apiConfig = config?.api || {}\n  if (!apiConfig.projectId || !apiConfig.dataset) {\n    throw new Error('Unable to resolve project ID/dataset from CLI configuration')\n  }\n\n  return createClient({\n    projectId: apiConfig.projectId,\n    dataset: apiConfig.dataset,\n    apiVersion,\n    useCdn,\n    token,\n  })\n}\n\n/* eslint-disable camelcase */\ngetCliClientImpl.__internal__getToken = (): string | undefined => undefined\n/* eslint-enable camelcase */\n\n/** @internal */\nexport const getCliClient: GetCliClient = getCliClientImpl\n", "import {type CliConfig} from './types'\n\n/** @beta */\nexport function defineCliConfig(config: CliConfig): CliConfig {\n  return config\n}\n\n/**\n * @deprecated Use `defineCliConfig` instead\n * @beta\n */\nexport function createCliConfig(config: CliConfig): CliConfig {\n  return config\n}\n"], "names": ["createClient", "resolveRootDir", "getCliConfigSync"], "mappings": ";;;AAyBA,SAAS,iBAAiB,UAA4B,IAAkB;AACtE,MAAI,OAAO,WAAY;AACf,UAAA,IAAI,MAAM,2DAA2D;AAGvE,QAAA;AAAA;AAAA,IAEJ,MAAM,QAAQ,IAAI,oBAAoB,QAAQ,IAAI;AAAA,IAClD,SAAS;AAAA,IACT,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,QAAQ,aAAa,qBAAqB;AAAA,EAAA,IACxC;AAEJ,MAAI,aAAa;AACf,WAAOA,OAAAA,aAAa,EAAC,WAAW,SAAS,YAAY,QAAQ,OAAM;AAG/D,QAAA,UAAUC,QAAe,eAAA,GAAG,GAC5B,EAAC,WAAUC,aAAAA,iBAAiB,OAAO,KAAK,CAAC;AAC/C,MAAI,CAAC;AACG,UAAA,IAAI,MAAM,qCAAqC;AAGjD,QAAA,YAAY,QAAQ,OAAO,CAAC;AAClC,MAAI,CAAC,UAAU,aAAa,CAAC,UAAU;AAC/B,UAAA,IAAI,MAAM,6DAA6D;AAG/E,SAAOF,oBAAa;AAAA,IAClB,WAAW,UAAU;AAAA,IACrB,SAAS,UAAU;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EAAA,CACD;AACH;AAGA,iBAAiB,uBAAuB,MAAuB;AAAA;AAIxD,MAAM,eAA6B;AClEnC,SAAS,gBAAgB,QAA8B;AACrD,SAAA;AACT;AAMO,SAAS,gBAAgB,QAA8B;AACrD,SAAA;AACT;;;;;"}