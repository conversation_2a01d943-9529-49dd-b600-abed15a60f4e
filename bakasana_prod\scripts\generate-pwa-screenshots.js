#!/usr/bin/env node

/**
 * Enterprise PWA Screenshot Generator
 * Generates high-quality screenshots for PWA installation prompts
 * Compatible with all major browsers and devices
 */

const puppeteer = require('puppeteer');
const sharp = require('sharp');

const fs = require('fs').promises;
const path = require('path');

// Enterprise-grade screenshot configurations
const SCREENSHOT_CONFIGS = [
  // Mobile Screenshots (required for PWA)
  {
    name: 'mobile-homepage',
    url: 'http://localhost:3000',
    width: 390,
    height: 844,
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true,
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  },
  {
    name: 'mobile-retreats',
    url: 'http://localhost:3000/retreaty',
    width: 390,
    height: 844,
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true,
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  },
  {
    name: 'mobile-booking',
    url: 'http://localhost:3000/rezerwacja',
    width: 390,
    height: 844,
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true,
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  },
  {
    name: 'mobile-blog',
    url: 'http://localhost:3000/blog',
    width: 390,
    height: 844,
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true,
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  },
  // Desktop Screenshots (for desktop PWA)
  {
    name: 'desktop-homepage',
    url: 'http://localhost:3000',
    width: 1920,
    height: 1080,
    deviceScaleFactor: 2,
    isMobile: false,
    hasTouch: false,
    userAgent:
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  },
  {
    name: 'desktop-retreats',
    url: 'http://localhost:3000/retreaty',
    width: 1920,
    height: 1080,
    deviceScaleFactor: 2,
    isMobile: false,
    hasTouch: false,
    userAgent:
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  },
  // Tablet Screenshots (for tablet PWA)
  {
    name: 'tablet-homepage',
    url: 'http://localhost:3000',
    width: 768,
    height: 1024,
    deviceScaleFactor: 2,
    isMobile: true,
    hasTouch: true,
    userAgent:
      'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  },
  {
    name: 'tablet-retreats',
    url: 'http://localhost:3000/retreaty',
    width: 768,
    height: 1024,
    deviceScaleFactor: 2,
    isMobile: true,
    hasTouch: true,
    userAgent:
      'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  },
];

class EnterpriseScreenshotGenerator {
  constructor() {
    this.browser = null;
    this.outputDir = path.join(
      __dirname,
      '..',
      'public',
      'images',
      'pwa-screenshots'
    );
    this.optimizedOutputDir = path.join(
      __dirname,
      '..',
      'public',
      'images',
      'pwa-screenshots',
      'optimized'
    );
  }

  async init() {
    console.log('🚀 Initializing Enterprise PWA Screenshot Generator...');

    // Ensure output directories exist
    await this.ensureDirectories();

    // Launch browser with enterprise configuration
    this.browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
      ],
      timeout: 60000,
    });

    console.log('✅ Browser launched successfully');
  }

  async ensureDirectories() {
    try {
      await fs.mkdir(this.outputDir, { recursive: true });
      await fs.mkdir(this.optimizedOutputDir, { recursive: true });
      console.log('📁 Output directories created');
    } catch (error) {
      console.error('❌ Error creating directories:', error);
      throw error;
    }
  }

  async generateScreenshots() {
    console.log('📸 Starting screenshot generation...');

    for (const config of SCREENSHOT_CONFIGS) {
      try {
        console.log(`📱 Generating screenshot: ${config.name}`);
        await this.generateScreenshot(config);
        console.log(`✅ Screenshot generated: ${config.name}`);
      } catch (error) {
        console.error(`❌ Error generating screenshot ${config.name}:`, error);
        // Continue with other screenshots even if one fails
      }
    }
  }

  async generateScreenshot(config) {
    const page = await this.browser.newPage();

    try {
      // Set viewport and user agent
      await page.setViewport({
        width: config.width,
        height: config.height,
        deviceScaleFactor: config.deviceScaleFactor,
        isMobile: config.isMobile,
        hasTouch: config.hasTouch,
      });

      await page.setUserAgent(config.userAgent);

      // Navigate to page
      await page.goto(config.url, {
        waitUntil: 'networkidle2',
        timeout: 60000,
      });

      // Wait for page to be fully loaded
      await page.waitForTimeout(3000);

      // Remove any loading states or overlays
      await page.evaluate(() => {
        // Remove loading spinners
        const loadingElements = document.querySelectorAll(
          '[class*="loading"], [class*="spinner"], [class*="skeleton"]'
        );
        loadingElements.forEach(el => el.remove());

        // Remove cookie banners or popups
        const overlayElements = document.querySelectorAll(
          '[class*="overlay"], [class*="modal"], [class*="popup"]'
        );
        overlayElements.forEach(el => {
          if (
            el.style.position === 'fixed' ||
            el.style.position === 'absolute'
          ) {
            el.remove();
          }
        });
      });

      // Wait a bit more for any animations to complete
      await page.waitForTimeout(2000);

      // Take screenshot
      const screenshotBuffer = await page.screenshot({
        type: 'png',
        fullPage: false,
        omitBackground: false,
      });

      // Save original screenshot
      const originalPath = path.join(this.outputDir, `${config.name}.png`);
      await fs.writeFile(originalPath, screenshotBuffer);

      // Generate optimized versions
      await this.optimizeScreenshot(screenshotBuffer, config);
    } finally {
      await page.close();
    }
  }

  async optimizeScreenshot(buffer, config) {
    const optimizedPath = path.join(
      this.optimizedOutputDir,
      `${config.name}.webp`
    );
    const thumbnailPath = path.join(
      this.optimizedOutputDir,
      `${config.name}-thumb.webp`
    );

    // Generate optimized WebP version
    await sharp(buffer).webp({ quality: 90, effort: 6 }).toFile(optimizedPath);

    // Generate thumbnail
    await sharp(buffer)
      .resize(Math.floor(config.width / 2), Math.floor(config.height / 2), {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 },
      })
      .webp({ quality: 80, effort: 6 })
      .toFile(thumbnailPath);

    console.log(`🎨 Optimized screenshots generated for ${config.name}`);
  }

  async updateManifest() {
    console.log('📝 Updating manifest with screenshots...');

    const manifestPath = path.join(__dirname, '..', 'public', 'manifest.json');
    let manifest;

    try {
      const manifestContent = await fs.readFile(manifestPath, 'utf8');
      manifest = JSON.parse(manifestContent);
    } catch (error) {
      console.error('❌ Error reading manifest:', error);
      throw error;
    }

    // Add screenshots section
    manifest.screenshots = [
      {
        src: '/images/pwa-screenshots/mobile-homepage.png',
        sizes: '390x844',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'BAKASANA - Homepage',
      },
      {
        src: '/images/pwa-screenshots/mobile-retreats.png',
        sizes: '390x844',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'BAKASANA - Retreats',
      },
      {
        src: '/images/pwa-screenshots/mobile-booking.png',
        sizes: '390x844',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'BAKASANA - Booking',
      },
      {
        src: '/images/pwa-screenshots/mobile-blog.png',
        sizes: '390x844',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'BAKASANA - Blog',
      },
      {
        src: '/images/pwa-screenshots/desktop-homepage.png',
        sizes: '1920x1080',
        type: 'image/png',
        form_factor: 'wide',
        label: 'BAKASANA - Homepage Desktop',
      },
      {
        src: '/images/pwa-screenshots/desktop-retreats.png',
        sizes: '1920x1080',
        type: 'image/png',
        form_factor: 'wide',
        label: 'BAKASANA - Retreats Desktop',
      },
      {
        src: '/images/pwa-screenshots/tablet-homepage.png',
        sizes: '768x1024',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'BAKASANA - Homepage Tablet',
      },
      {
        src: '/images/pwa-screenshots/tablet-retreats.png',
        sizes: '768x1024',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'BAKASANA - Retreats Tablet',
      },
    ];

    // Update manifest
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));
    console.log('✅ Manifest updated with screenshots');
  }

  async generateReport() {
    console.log('📊 Generating screenshot report...');

    const reportPath = path.join(this.outputDir, 'screenshot-report.json');
    const report = {
      generatedAt: new Date().toISOString(),
      totalScreenshots: SCREENSHOT_CONFIGS.length,
      screenshots: [],
      stats: {
        mobile: 0,
        desktop: 0,
        tablet: 0,
      },
    };

    for (const config of SCREENSHOT_CONFIGS) {
      const screenshotPath = path.join(this.outputDir, `${config.name}.png`);

      try {
        const stats = await fs.stat(screenshotPath);
        report.screenshots.push({
          name: config.name,
          size: stats.size,
          dimensions: `${config.width}x${config.height}`,
          deviceType: config.isMobile
            ? config.width < 500
              ? 'mobile'
              : 'tablet'
            : 'desktop',
          generated: true,
        });

        // Update stats
        if (config.isMobile) {
          if (config.width < 500) {
            report.stats.mobile++;
          } else {
            report.stats.tablet++;
          }
        } else {
          report.stats.desktop++;
        }
      } catch (error) {
        report.screenshots.push({
          name: config.name,
          generated: false,
          error: error.message,
        });
      }
    }

    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    console.log('✅ Screenshot report generated');

    // Print summary
    console.log('\n📊 SCREENSHOT GENERATION SUMMARY:');
    console.log(`📱 Mobile: ${report.stats.mobile} screenshots`);
    console.log(`📱 Tablet: ${report.stats.tablet} screenshots`);
    console.log(`💻 Desktop: ${report.stats.desktop} screenshots`);
    console.log(
      `📊 Total: ${report.screenshots.filter(s => s.generated).length}/${report.totalScreenshots} successfully generated`
    );
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Browser closed');
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 BAKASANA PWA Screenshot Generator - Enterprise Edition');
  console.log('================================================');

  const generator = new EnterpriseScreenshotGenerator();

  try {
    await generator.init();
    await generator.generateScreenshots();
    await generator.updateManifest();
    await generator.generateReport();

    console.log('\n🎉 SUCCESS! PWA screenshots generated successfully!');
    console.log(
      '📱 Your PWA is now ready for installation prompts on all devices!'
    );
  } catch (error) {
    console.error('\n❌ ERROR:', error);
    process.exit(1);
  } finally {
    await generator.cleanup();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { EnterpriseScreenshotGenerator };
