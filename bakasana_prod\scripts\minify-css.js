#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const glob = require('glob');

console.log('🎨 Starting CSS minification...');

// Create dist directory if it doesn't exist
const distDir = path.join(process.cwd(), 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Find all CSS files
const cssFiles = glob.sync('src/**/*.css', { 
  ignore: ['node_modules/**', 'dist/**', '**/*.min.css']
});

console.log(`Found ${cssFiles.length} CSS files to minify`);

if (cssFiles.length === 0) {
  console.log('⚠️ No CSS files found to minify');
  process.exit(0);
}

let totalOriginalSize = 0;
let totalMinifiedSize = 0;
let combinedContent = '';

cssFiles.forEach(file => {
  try {
    console.log(`⚡ Processing ${path.basename(file)}...`);
    
    const originalContent = fs.readFileSync(file, 'utf8');
    totalOriginalSize += originalContent.length;
    
    // Create temporary file for cssnano
    const tempFile = path.join(distDir, 'temp.css');
    fs.writeFileSync(tempFile, originalContent);
    
    // Minify with cssnano
    const minifiedFile = path.join(distDir, 'temp.min.css');
    execSync(`npx cssnano ${tempFile} ${minifiedFile}`, { stdio: 'pipe' });
    
    const minifiedContent = fs.readFileSync(minifiedFile, 'utf8');
    totalMinifiedSize += minifiedContent.length;
    
    // Add to combined content
    combinedContent += `\n/* ${path.basename(file)} */\n${minifiedContent}`;
    
    // Create individual minified file
    const individualMinFile = path.join(distDir, path.basename(file, '.css') + '.min.css');
    fs.writeFileSync(individualMinFile, minifiedContent);
    
    // Clean up temp files
    fs.unlinkSync(tempFile);
    fs.unlinkSync(minifiedFile);
    
    const savings = Math.round(((originalContent.length - minifiedContent.length) / originalContent.length) * 100);
    console.log(`  ✅ ${path.basename(file)}: ${originalContent.length} → ${minifiedContent.length} bytes (${savings}% saved)`);
    
  } catch (error) {
    console.error(`  ❌ Error minifying ${file}:`, error.message);
  }
});

// Write combined minified file
const combinedOutputFile = path.join(distDir, 'styles.min.css');
fs.writeFileSync(combinedOutputFile, combinedContent);

// Calculate total savings
const totalSavings = Math.round(((totalOriginalSize - totalMinifiedSize) / totalOriginalSize) * 100);

console.log(`\n📊 CSS Minification Results:`);
console.log(`   Original size: ${Math.round(totalOriginalSize / 1024)} KB`);
console.log(`   Minified size: ${Math.round(totalMinifiedSize / 1024)} KB`);
console.log(`   Space saved: ${totalSavings}%`);
console.log(`   Combined output: ${combinedOutputFile}`);

console.log('✅ CSS minification completed!');
