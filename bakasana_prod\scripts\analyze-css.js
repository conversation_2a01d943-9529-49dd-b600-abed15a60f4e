#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const glob = require('glob');

console.log('📊 BAKASANA CSS ANALYZER - Analiza wykorzystania stylów...\n');

// Konfiguracja ścieżek
const srcDir = path.join(__dirname, '../src');
const stylesDir = path.join(srcDir, 'styles');
const appDir = path.join(srcDir, 'app');

// Znajdź wszystkie pliki CSS
const cssFiles = [
  path.join(stylesDir, 'main.css'),
  path.join(stylesDir, 'hero.css'),
  path.join(stylesDir, 'sections.css'),
  path.join(stylesDir, 'modern-css.css'),
  path.join(stylesDir, 'advanced-typography.css'),
  path.join(stylesDir, 'microinteractions.css'),
  path.join(stylesDir, 'premium-utilities.css'),
  path.join(stylesDir, 'unified-system.css'),
  path.join(stylesDir, 'color-migration.css'),
  path.join(appDir, 'globals.css'),
  path.join(appDir, 'enhanced-globals.css'),
  path.join(appDir, 'bakasana-visuals.css'),
].filter(file => fs.existsSync(file));

// Znajdź wszystkie pliki zawartości
const contentFiles = glob.sync(path.join(srcDir, '**/*.{js,jsx,ts,tsx,html}'), {
  ignore: ['**/node_modules/**', '**/.next/**', '**/backup/**'],
});

function extractCSSClasses(cssContent) {
  const classes = new Set();
  const ids = new Set();
  const variables = new Set();

  // Wyciągnij klasy CSS (.class)
  const classMatches = cssContent.match(/\.[a-zA-Z][a-zA-Z0-9_-]*/g);
  if (classMatches) {
    classMatches.forEach(match => classes.add(match.substring(1)));
  }

  // Wyciągnij ID (#id)
  const idMatches = cssContent.match(/#[a-zA-Z][a-zA-Z0-9_-]*/g);
  if (idMatches) {
    idMatches.forEach(match => ids.add(match.substring(1)));
  }

  // Wyciągnij zmienne CSS (--variable)
  const variableMatches = cssContent.match(/--[a-zA-Z][a-zA-Z0-9_-]*/g);
  if (variableMatches) {
    variableMatches.forEach(match => variables.add(match));
  }

  return { classes, ids, variables };
}

function extractUsedClasses(content) {
  const used = new Set();

  // Wyciągnij klasy z className, class, i innych atrybutów
  const patterns = [
    /className\s*=\s*["'`]([^"'`]+)["'`]/g,
    /class\s*=\s*["'`]([^"'`]+)["'`]/g,
    /classList\.add\s*\(\s*["'`]([^"'`]+)["'`]/g,
    /classList\.toggle\s*\(\s*["'`]([^"'`]+)["'`]/g,
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const classes = match[1].split(/\s+/).filter(cls => cls.length > 0);
      classes.forEach(cls => used.add(cls));
    }
  });

  return used;
}

function analyzeCSSFile(filePath) {
  console.log(`🔍 Analizuję: ${path.relative(process.cwd(), filePath)}`);

  const cssContent = fs.readFileSync(filePath, 'utf8');
  const { classes, ids, variables } = extractCSSClasses(cssContent);

  // Znajdź użycie w plikach zawartości
  const usedClasses = new Set();
  const usedIds = new Set();
  const usedVariables = new Set();

  contentFiles.forEach(contentFile => {
    const content = fs.readFileSync(contentFile, 'utf8');

    // Sprawdź klasy
    classes.forEach(className => {
      if (content.includes(className)) {
        usedClasses.add(className);
      }
    });

    // Sprawdź ID
    ids.forEach(id => {
      if (content.includes(id)) {
        usedIds.add(id);
      }
    });

    // Sprawdź zmienne
    variables.forEach(variable => {
      if (content.includes(variable)) {
        usedVariables.add(variable);
      }
    });
  });

  const unusedClasses = [...classes].filter(cls => !usedClasses.has(cls));
  const unusedIds = [...ids].filter(id => !usedIds.has(id));
  const unusedVariables = [...variables].filter(
    variable => !usedVariables.has(variable)
  );

  const fileSize = Buffer.byteLength(cssContent, 'utf8');
  const usagePercent = {
    classes:
      classes.size > 0
        ? ((usedClasses.size / classes.size) * 100).toFixed(1)
        : 0,
    ids: ids.size > 0 ? ((usedIds.size / ids.size) * 100).toFixed(1) : 0,
    variables:
      variables.size > 0
        ? ((usedVariables.size / variables.size) * 100).toFixed(1)
        : 0,
  };

  console.log(`   📏 Rozmiar: ${(fileSize / 1024).toFixed(1)} KB`);
  console.log(
    `   🎯 Klasy: ${usedClasses.size}/${classes.size} używanych (${usagePercent.classes}%)`
  );
  console.log(
    `   🆔 ID: ${usedIds.size}/${ids.size} używanych (${usagePercent.ids}%)`
  );
  console.log(
    `   🔧 Zmienne: ${usedVariables.size}/${variables.size} używanych (${usagePercent.variables}%)`
  );

  if (unusedClasses.length > 0) {
    console.log(`   ❌ Nieużywane klasy (${unusedClasses.length}):`);
    unusedClasses.slice(0, 10).forEach(cls => console.log(`      - .${cls}`));
    if (unusedClasses.length > 10) {
      console.log(`      ... i ${unusedClasses.length - 10} więcej`);
    }
  }

  console.log('');

  return {
    file: path.basename(filePath),
    fileSize,
    classes: {
      total: classes.size,
      used: usedClasses.size,
      unused: unusedClasses.length,
      usagePercent: parseFloat(usagePercent.classes),
    },
    ids: {
      total: ids.size,
      used: usedIds.size,
      unused: unusedIds.length,
      usagePercent: parseFloat(usagePercent.ids),
    },
    variables: {
      total: variables.size,
      used: usedVariables.size,
      unused: unusedVariables.length,
      usagePercent: parseFloat(usagePercent.variables),
    },
    unusedClasses,
    unusedIds,
    unusedVariables,
  };
}

async function analyzeAllCSS() {
  console.log(`📁 Znaleziono ${cssFiles.length} plików CSS do analizy:`);
  cssFiles.forEach(file =>
    console.log(`   - ${path.relative(process.cwd(), file)}`)
  );
  console.log(
    `📄 Analizuję względem ${contentFiles.length} plików zawartości\n`
  );

  const results = [];
  let totalSize = 0;
  let totalClasses = 0;
  let totalUsedClasses = 0;

  for (const cssFile of cssFiles) {
    const result = analyzeCSSFile(cssFile);
    results.push(result);
    totalSize += result.fileSize;
    totalClasses += result.classes.total;
    totalUsedClasses += result.classes.used;
  }

  // Podsumowanie
  const overallUsage =
    totalClasses > 0 ? ((totalUsedClasses / totalClasses) * 100).toFixed(1) : 0;

  console.log('📊 PODSUMOWANIE ANALIZY CSS:');
  console.log(
    `   📏 Całkowity rozmiar CSS: ${(totalSize / 1024).toFixed(1)} KB`
  );
  console.log(
    `   🎯 Ogólne wykorzystanie klas: ${totalUsedClasses}/${totalClasses} (${overallUsage}%)`
  );
  console.log(
    `   💡 Potencjał optymalizacji: ${(100 - overallUsage).toFixed(1)}%`
  );

  // Zapisz szczegółowy raport
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: cssFiles.length,
      totalSize,
      totalClasses,
      totalUsedClasses,
      overallUsage: parseFloat(overallUsage),
    },
    files: results,
  };

  fs.writeFileSync(
    path.join(__dirname, '../css-analysis-report.json'),
    JSON.stringify(report, null, 2)
  );

  console.log('\n✨ Analiza CSS zakończona!');
  console.log(`📄 Szczegółowy raport: css-analysis-report.json`);
}

// Uruchom analizę
analyzeAllCSS().catch(console.error);
