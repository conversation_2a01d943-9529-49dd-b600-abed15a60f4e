#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const glob = require('glob');

console.log(
  '🎨 BAKASANA CSS PROPERTY SORTER - Sortowanie właściwości CSS...\n'
);

// Konfiguracja ścieżek
const srcDir = path.join(__dirname, '../src');
const stylesDir = path.join(srcDir, 'styles');
const appDir = path.join(srcDir, 'app');

// Znajdź wszystkie pliki CSS
const cssFiles = [
  ...glob.sync(path.join(stylesDir, '*.css').replace(/\\/g, '/')),
  ...glob.sync(path.join(appDir, '*.css').replace(/\\/g, '/')),
].filter(
  file =>
    !file.includes('node_modules') &&
    !file.includes('.backup') &&
    !file.includes('clean')
);

console.log(`📁 Znaleziono ${cssFiles.length} plików CSS do sortowania:`);
cssFiles.forEach(file =>
  console.log(`   - ${path.relative(process.cwd(), file)}`)
);
console.log();

// Kolejność właściwości CSS (zgodnie z najlepszymi praktykami)
const propertyOrder = [
  // Positioning
  'position',
  'top',
  'right',
  'bottom',
  'left',
  'z-index',

  // Display & Box Model
  'display',
  'flex',
  'flex-direction',
  'flex-wrap',
  'flex-flow',
  'justify-content',
  'align-items',
  'align-content',
  'align-self',
  'grid',
  'grid-template',
  'grid-template-rows',
  'grid-template-columns',
  'grid-template-areas',
  'grid-gap',
  'grid-row',
  'grid-column',
  'float',
  'clear',
  'visibility',
  'overflow',
  'overflow-x',
  'overflow-y',
  'clip',
  'zoom',

  // Box Model
  'box-sizing',
  'width',
  'min-width',
  'max-width',
  'height',
  'min-height',
  'max-height',
  'margin',
  'margin-top',
  'margin-right',
  'margin-bottom',
  'margin-left',
  'padding',
  'padding-top',
  'padding-right',
  'padding-bottom',
  'padding-left',

  // Border
  'border',
  'border-top',
  'border-right',
  'border-bottom',
  'border-left',
  'border-width',
  'border-top-width',
  'border-right-width',
  'border-bottom-width',
  'border-left-width',
  'border-style',
  'border-top-style',
  'border-right-style',
  'border-bottom-style',
  'border-left-style',
  'border-color',
  'border-top-color',
  'border-right-color',
  'border-bottom-color',
  'border-left-color',
  'border-radius',
  'border-top-left-radius',
  'border-top-right-radius',
  'border-bottom-right-radius',
  'border-bottom-left-radius',
  'outline',
  'outline-width',
  'outline-style',
  'outline-color',
  'outline-offset',

  // Background
  'background',
  'background-color',
  'background-image',
  'background-repeat',
  'background-attachment',
  'background-position',
  'background-size',
  'background-clip',
  'background-origin',
  'box-shadow',

  // Typography
  'color',
  'font',
  'font-family',
  'font-size',
  'font-weight',
  'font-style',
  'font-variant',
  'font-size-adjust',
  'font-stretch',
  'line-height',
  'letter-spacing',
  'word-spacing',
  'text-align',
  'text-align-last',
  'text-decoration',
  'text-decoration-color',
  'text-decoration-line',
  'text-decoration-style',
  'text-indent',
  'text-justify',
  'text-overflow',
  'text-shadow',
  'text-transform',
  'text-wrap',
  'white-space',
  'word-break',
  'word-wrap',

  // Lists
  'list-style',
  'list-style-position',
  'list-style-type',
  'list-style-image',

  // Tables
  'table-layout',
  'border-collapse',
  'border-spacing',
  'caption-side',
  'empty-cells',

  // Content
  'content',
  'quotes',
  'counter-reset',
  'counter-increment',

  // Transforms & Animations
  'transform',
  'transform-origin',
  'transform-style',
  'perspective',
  'perspective-origin',
  'backface-visibility',
  'transition',
  'transition-property',
  'transition-duration',
  'transition-timing-function',
  'transition-delay',
  'animation',
  'animation-name',
  'animation-duration',
  'animation-timing-function',
  'animation-delay',
  'animation-iteration-count',
  'animation-direction',
  'animation-fill-mode',
  'animation-play-state',

  // Other
  'opacity',
  'filter',
  'cursor',
  'pointer-events',
  'user-select',
  'resize',
  'will-change',
];

// Funkcja do sortowania właściwości w regule CSS
function sortCSSProperties(cssContent) {
  return cssContent.replace(
    /([^{}]+)\s*\{([^{}]*)\}/g,
    (match, selector, properties) => {
      // Pomiń komentarze i @rules
      if (selector.trim().startsWith('/*') || selector.trim().startsWith('@')) {
        return match;
      }

      // Parsuj właściwości
      const props = [];
      const propRegex = /([^:;]+):\s*([^;]+);?/g;
      let propMatch;

      while ((propMatch = propRegex.exec(properties)) !== null) {
        const property = propMatch[1].trim();
        const value = propMatch[2].trim();
        props.push({ property, value, original: propMatch[0] });
      }

      // Sortuj właściwości
      props.sort((a, b) => {
        const indexA = propertyOrder.indexOf(a.property);
        const indexB = propertyOrder.indexOf(b.property);

        // Jeśli obie właściwości są w liście, sortuj według kolejności
        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB;
        }

        // Jeśli tylko jedna jest w liście, ta ma pierwszeństwo
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;

        // Jeśli żadna nie jest w liście, sortuj alfabetycznie
        return a.property.localeCompare(b.property);
      });

      // Zbuduj posortowane właściwości
      const sortedProperties = props
        .map(prop => `  ${prop.property}: ${prop.value};`)
        .join('\n');

      return `${selector.trim()} {\n${sortedProperties}\n}`;
    }
  );
}

// Funkcja do przetwarzania pliku CSS
function processCSSFile(filePath) {
  const fileName = path.basename(filePath);
  console.log(`🎨 Sortowanie: ${fileName}`);

  try {
    // Wczytaj plik
    const content = fs.readFileSync(filePath, 'utf8');

    // Sortuj właściwości
    const sortedContent = sortCSSProperties(content);

    // Zapisz posortowany plik
    fs.writeFileSync(filePath, sortedContent, 'utf8');

    console.log(`   ✅ Posortowano właściwości w ${fileName}`);
    return true;
  } catch (error) {
    console.error(`   ❌ Błąd w ${fileName}:`, error.message);
    return false;
  }
}

// Główna funkcja
function sortAllCSS() {
  console.log('🚀 Rozpoczynam sortowanie właściwości CSS...\n');

  let processed = 0;
  let errors = 0;

  cssFiles.forEach(filePath => {
    if (processCSSFile(filePath)) {
      processed++;
    } else {
      errors++;
    }
  });

  console.log('\n📊 PODSUMOWANIE SORTOWANIA:');
  console.log(`   ✅ Przetworzone pliki: ${processed}`);
  console.log(`   ❌ Błędy: ${errors}`);
  console.log(`   📁 Całkowita liczba plików: ${cssFiles.length}`);

  if (errors === 0) {
    console.log('\n🎉 Wszystkie pliki CSS zostały posortowane!');
  } else {
    console.log(
      '\n⚠️  Niektóre pliki nie zostały przetworzone z powodu błędów.'
    );
  }
}

// Uruchom sortowanie
try {
  sortAllCSS();
  console.log('\n✨ Sortowanie właściwości CSS zakończone!');
} catch (error) {
  console.error('❌ Błąd podczas sortowania:', error.message);
  process.exit(1);
}
