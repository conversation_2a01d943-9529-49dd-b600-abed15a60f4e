import { AddedAnnotationPaths, AnnotationDefinition, AnnotationPath, AnnotationSchemaType, BaseDefinition, BlockAnnotationRenderProps, BlockChildRenderProps, BlockDecoratorRenderProps, BlockListItemRenderProps, BlockObjectDefinition, BlockObjectSchemaType, BlockOffset, BlockPath, BlockRenderProps, BlockStyleRenderProps, BlurChange, ChildPath, ConnectionChange, DecoratorDefinition, DecoratorSchemaType, EditableAPI, EditableAPIDeleteOptions, Editor, EditorChange, EditorChanges, EditorConfig, EditorContext, EditorEmittedEvent, EditorEvent, EditorEventListener, EditorProvider, EditorProviderProps, EditorSchema, EditorSelection, EditorSelectionPoint, EditorSelector, EditorSnapshot, ErrorChange, FieldDefinition, FocusChange, HotkeyOptions, InlineObjectDefinition, InlineObjectSchemaType, InvalidValue, InvalidValueResolution, ListDefinition, ListSchemaType, LoadingChange, MutationChange, MutationEvent, OnBeforeInputFn, OnCopyFn, OnPasteFn, OnPasteResult, OnPasteResultOrPromise, PasteData, Patch, PatchChange, PatchObservable, PatchesEvent, PortableTextBlock, PortableTextChild, PortableTextEditable, PortableTextEditableProps, PortableTextEditor, PortableTextEditorProps, PortableTextMemberSchemaTypes, PortableTextObject, PortableTextSpan, PortableTextTextBlock, RangeDecoration, RangeDecorationOnMovedDetails, ReadyChange, RedoChange, RenderAnnotationFunction, RenderBlockFunction, RenderChildFunction, RenderDecoratorFunction, RenderEditableFunction, RenderListItemFunction, RenderPlaceholderFunction, RenderStyleFunction, SchemaDefinition, ScrollSelectionIntoViewFunction, SelectionChange, StyleDefinition, StyleSchemaType, UndoChange, UnsetChange, ValueChange, defaultKeyGenerator, defineSchema, useEditor, useEditorSelector, usePortableTextEditor, usePortableTextEditorSelection } from "./_chunks-dts/behavior.types.action.cjs";
export { AddedAnnotationPaths, AnnotationDefinition, AnnotationPath, AnnotationSchemaType, BaseDefinition, BlockAnnotationRenderProps, BlockChildRenderProps, BlockDecoratorRenderProps, BlockListItemRenderProps, BlockObjectDefinition, BlockObjectSchemaType, BlockOffset, BlockPath, BlockRenderProps, BlockStyleRenderProps, BlurChange, ChildPath, ConnectionChange, DecoratorDefinition, DecoratorSchemaType, EditableAPI, EditableAPIDeleteOptions, Editor, EditorChange, EditorChanges, EditorConfig, EditorContext, EditorEmittedEvent, EditorEvent, EditorEventListener, EditorProvider, EditorProviderProps, EditorSchema, EditorSelection, EditorSelectionPoint, EditorSelector, EditorSnapshot, ErrorChange, FieldDefinition, FocusChange, HotkeyOptions, InlineObjectDefinition, InlineObjectSchemaType, InvalidValue, InvalidValueResolution, ListDefinition, ListSchemaType, LoadingChange, MutationChange, MutationEvent, OnBeforeInputFn, OnCopyFn, OnPasteFn, OnPasteResult, OnPasteResultOrPromise, PasteData, Patch, PatchChange, PatchObservable, PatchesEvent, PortableTextBlock, PortableTextChild, PortableTextEditable, PortableTextEditableProps, PortableTextEditor, PortableTextEditorProps, PortableTextMemberSchemaTypes, PortableTextObject, PortableTextSpan, PortableTextTextBlock, RangeDecoration, RangeDecorationOnMovedDetails, ReadyChange, RedoChange, RenderAnnotationFunction, RenderBlockFunction, RenderChildFunction, RenderDecoratorFunction, RenderEditableFunction, RenderListItemFunction, RenderPlaceholderFunction, RenderStyleFunction, SchemaDefinition, ScrollSelectionIntoViewFunction, SelectionChange, StyleDefinition, StyleSchemaType, UndoChange, UnsetChange, ValueChange, defineSchema, defaultKeyGenerator as keyGenerator, useEditor, useEditorSelector, usePortableTextEditor, usePortableTextEditorSelection };