{"version": 3, "file": "selector.is-selection-expanded.js", "sources": ["../../src/selectors/selector.get-focus-block.ts", "../../src/selectors/selector.get-focus-text-block.ts", "../../src/selectors/selector.get-focus-child.ts", "../../src/selectors/selector.get-focus-span.ts", "../../src/selectors/selector.get-selection-start-point.ts", "../../src/selectors/selector.get-previous-inline-object.ts", "../../src/selectors/selector.get-selected-value.ts", "../../src/selectors/selector.get-selection-text.ts", "../../src/selectors/selector.is-selection-collapsed.ts", "../../src/selectors/selector.is-selection-expanded.ts"], "sourcesContent": ["import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {getBlockKeyFromSelectionPoint} from '../selection/selection-point'\nimport type {BlockPath} from '../types/paths'\n\n/**\n * @public\n */\nexport const getFocusBlock: EditorSelector<\n  {node: PortableTextBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  const key = getBlockKeyFromSelectionPoint(snapshot.context.selection.focus)\n  const index = key ? snapshot.blockIndexMap.get(key) : undefined\n\n  const node =\n    index !== undefined ? snapshot.context.value.at(index) : undefined\n\n  return node && key ? {node, path: [{_key: key}]} : undefined\n}\n", "import type {PortableTextTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport type {BlockPath} from '../types/paths'\nimport {getFocusBlock} from './selector.get-focus-block'\n\n/**\n * @public\n */\nexport const getFocusTextBlock: EditorSelector<\n  {node: PortableTextTextBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  const focusBlock = getFocusBlock(snapshot)\n\n  return focusBlock && isTextBlock(snapshot.context, focusBlock.node)\n    ? {node: focusBlock.node, path: focusBlock.path}\n    : undefined\n}\n", "import type {PortableTextObject, PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {getChildKeyFromSelectionPoint} from '../selection/selection-point'\nimport type {ChildPath} from '../types/paths'\nimport {getFocusTextBlock} from './selector.get-focus-text-block'\n\n/**\n * @public\n */\nexport const getFocusChild: EditorSelector<\n  | {\n      node: PortableTextObject | PortableTextSpan\n      path: ChildPath\n    }\n  | undefined\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  const focusBlock = getFocusTextBlock(snapshot)\n\n  if (!focusBlock) {\n    return undefined\n  }\n\n  const key = getChildKeyFromSelectionPoint(snapshot.context.selection.focus)\n\n  const node = key\n    ? focusBlock.node.children.find((span) => span._key === key)\n    : undefined\n\n  return node && key\n    ? {node, path: [...focusBlock.path, 'children', {_key: key}]}\n    : undefined\n}\n", "import type {PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isSpan} from '../internal-utils/parse-blocks'\nimport type {ChildPath} from '../types/paths'\nimport {getFocusChild} from './selector.get-focus-child'\n\n/**\n * @public\n */\nexport const getFocusSpan: EditorSelector<\n  {node: PortableTextSpan; path: ChildPath} | undefined\n> = (snapshot) => {\n  const focusChild = getFocusChild(snapshot)\n\n  return focusChild && isSpan(snapshot.context, focusChild.node)\n    ? {node: focusChild.node, path: focusChild.path}\n    : undefined\n}\n", "import type {EditorSelectionPoint} from '..'\nimport type {EditorSelector} from '../editor/editor-selector'\n\n/**\n * @public\n */\nexport const getSelectionStartPoint: EditorSelector<\n  EditorSelectionPoint | undefined\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  return snapshot.context.selection.backward\n    ? snapshot.context.selection.focus\n    : snapshot.context.selection.anchor\n}\n", "import {isKeySegment, type PortableTextObject} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {ChildPath} from '../types/paths'\nimport {isSpan} from '../utils'\nimport {getFocusTextBlock} from './selector.get-focus-text-block'\nimport {getSelectionStartPoint} from './selector.get-selection-start-point'\n\n/**\n * @public\n */\nexport const getPreviousInlineObject: EditorSelector<\n  | {\n      node: PortableTextObject\n      path: ChildPath\n    }\n  | undefined\n> = (snapshot) => {\n  const focusTextBlock = getFocusTextBlock(snapshot)\n  const selectionStartPoint = getSelectionStartPoint(snapshot)\n  const selectionStartPointChildKey =\n    selectionStartPoint && isKeySegment(selectionStartPoint.path[2])\n      ? selectionStartPoint.path[2]._key\n      : undefined\n\n  if (!focusTextBlock || !selectionStartPointChildKey) {\n    return undefined\n  }\n\n  let inlineObject:\n    | {\n        node: PortableTextObject\n        path: ChildPath\n      }\n    | undefined\n\n  for (const child of focusTextBlock.node.children) {\n    if (child._key === selectionStartPointChildKey) {\n      break\n    }\n\n    if (!isSpan(snapshot.context, child)) {\n      inlineObject = {\n        node: child,\n        path: [...focusTextBlock.path, 'children', {_key: child._key}],\n      }\n    }\n  }\n\n  return inlineObject\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {getBlockKeyFromSelectionPoint} from '../selection/selection-point'\nimport {\n  getSelectionEndPoint,\n  getSelectionStartPoint,\n  sliceBlocks,\n} from '../utils'\n\n/**\n * @public\n */\nexport const getSelectedValue: EditorSelector<Array<PortableTextBlock>> = (\n  snapshot,\n) => {\n  const selection = snapshot.context.selection\n\n  if (!selection) {\n    return []\n  }\n\n  const startPoint = getSelectionStartPoint(selection)\n  const endPoint = getSelectionEndPoint(selection)\n  const startBlockKey = getBlockKeyFromSelectionPoint(startPoint)\n  const endBlockKey = getBlockKeyFromSelectionPoint(endPoint)\n\n  if (!startBlockKey || !endBlockKey) {\n    return []\n  }\n\n  const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey)\n  const endBlockIndex = snapshot.blockIndexMap.get(endBlockKey)\n\n  if (startBlockIndex === undefined || endBlockIndex === undefined) {\n    return []\n  }\n\n  const slicedValue = snapshot.context.value.slice(\n    startBlockIndex,\n    endBlockIndex + 1,\n  )\n\n  return sliceBlocks({\n    context: snapshot.context,\n    blocks: slicedValue,\n  })\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {getSelectedValue} from './selector.get-selected-value'\n\n/**\n * @public\n */\nexport const getSelectionText: EditorSelector<string> = (snapshot) => {\n  const selectedValue = getSelectedValue(snapshot)\n\n  return selectedValue.reduce((text, block) => {\n    if (!isTextBlock(snapshot.context, block)) {\n      return text\n    }\n\n    return (\n      text +\n      block.children.reduce((text, child) => {\n        if (isSpan(snapshot.context, child)) {\n          return text + child.text\n        }\n\n        return text\n      }, '')\n    )\n  }, '')\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\n\n/**\n * @public\n */\nexport const isSelectionCollapsed: EditorSelector<boolean> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return false\n  }\n\n  return (\n    JSON.stringify(snapshot.context.selection.anchor.path) ===\n      JSON.stringify(snapshot.context.selection.focus.path) &&\n    snapshot.context.selection?.anchor.offset ===\n      snapshot.context.selection?.focus.offset\n  )\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {isSelectionCollapsed} from './selector.is-selection-collapsed'\n\n/**\n * @public\n */\nexport const isSelectionExpanded: EditorSelector<boolean> = (snapshot) => {\n  return !isSelectionCollapsed(snapshot)\n}\n"], "names": ["getFocusBlock", "snapshot", "context", "selection", "key", "getBlockKeyFromSelectionPoint", "focus", "index", "blockIndexMap", "get", "undefined", "node", "value", "at", "path", "_key", "getFocusTextBlock", "focusBlock", "isTextBlock", "getFocusChild", "getChildKeyFromSelectionPoint", "children", "find", "span", "getFocusSpan", "<PERSON><PERSON><PERSON><PERSON>", "isSpan", "getSelectionStartPoint", "backward", "anchor", "getPreviousInlineObject", "focusTextBlock", "selectionStartPoint", "selectionStartPointC<PERSON>d<PERSON>ey", "isKeySegment", "inlineObject", "child", "getSelectedValue", "startPoint", "endPoint", "getSelectionEndPoint", "startBlockKey", "endBlockKey", "startBlockIndex", "endBlockIndex", "slicedValue", "slice", "sliceBlocks", "blocks", "getSelectionText", "reduce", "text", "block", "isSelectionCollapsed", "JSON", "stringify", "offset", "isSelectionExpanded"], "mappings": ";;AAQO,MAAMA,gBAERC,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASC,QAAQC;AACpB;AAGF,QAAMC,MAAMC,8BAA8BJ,SAASC,QAAQC,UAAUG,KAAK,GACpEC,QAAQH,MAAMH,SAASO,cAAcC,IAAIL,GAAG,IAAIM,QAEhDC,OACJJ,UAAUG,SAAYT,SAASC,QAAQU,MAAMC,GAAGN,KAAK,IAAIG;AAE3D,SAAOC,QAAQP,MAAM;AAAA,IAACO;AAAAA,IAAMG,MAAM,CAAC;AAAA,MAACC,MAAMX;AAAAA,IAAAA,CAAI;AAAA,EAAA,IAAKM;AACrD,GCbaM,oBAERf,CAAAA,aAAa;AAChB,QAAMgB,aAAajB,cAAcC,QAAQ;AAEzC,SAAOgB,cAAcC,YAAYjB,SAASC,SAASe,WAAWN,IAAI,IAC9D;AAAA,IAACA,MAAMM,WAAWN;AAAAA,IAAMG,MAAMG,WAAWH;AAAAA,EAAAA,IACzCJ;AACN,GCRaS,gBAMRlB,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASC,QAAQC;AACpB;AAGF,QAAMc,aAAaD,kBAAkBf,QAAQ;AAE7C,MAAI,CAACgB;AACH;AAGF,QAAMb,MAAMgB,8BAA8BnB,SAASC,QAAQC,UAAUG,KAAK,GAEpEK,OAAOP,MACTa,WAAWN,KAAKU,SAASC,KAAMC,UAASA,KAAKR,SAASX,GAAG,IACzDM;AAEJ,SAAOC,QAAQP,MACX;AAAA,IAACO;AAAAA,IAAMG,MAAM,CAAC,GAAGG,WAAWH,MAAM,YAAY;AAAA,MAACC,MAAMX;AAAAA,IAAAA,CAAI;AAAA,EAAA,IACzDM;AACN,GC1Bac,eAERvB,CAAAA,aAAa;AAChB,QAAMwB,aAAaN,cAAclB,QAAQ;AAEzC,SAAOwB,cAAcC,OAAOzB,SAASC,SAASuB,WAAWd,IAAI,IACzD;AAAA,IAACA,MAAMc,WAAWd;AAAAA,IAAMG,MAAMW,WAAWX;AAAAA,EAAAA,IACzCJ;AACN,GCXaiB,yBAER1B,CAAAA,aAAa;AAChB,MAAKA,SAASC,QAAQC;AAItB,WAAOF,SAASC,QAAQC,UAAUyB,WAC9B3B,SAASC,QAAQC,UAAUG,QAC3BL,SAASC,QAAQC,UAAU0B;AACjC,GCNaC,0BAMR7B,CAAAA,aAAa;AAChB,QAAM8B,iBAAiBf,kBAAkBf,QAAQ,GAC3C+B,sBAAsBL,uBAAuB1B,QAAQ,GACrDgC,8BACJD,uBAAuBE,aAAaF,oBAAoBlB,KAAK,CAAC,CAAC,IAC3DkB,oBAAoBlB,KAAK,CAAC,EAAEC,OAC5BL;AAEN,MAAI,CAACqB,kBAAkB,CAACE;AACtB;AAGF,MAAIE;AAOJ,aAAWC,SAASL,eAAepB,KAAKU,UAAU;AAChD,QAAIe,MAAMrB,SAASkB;AACjB;AAGGP,aAAOzB,SAASC,SAASkC,KAAK,MACjCD,eAAe;AAAA,MACbxB,MAAMyB;AAAAA,MACNtB,MAAM,CAAC,GAAGiB,eAAejB,MAAM,YAAY;AAAA,QAACC,MAAMqB,MAAMrB;AAAAA,MAAAA,CAAK;AAAA,IAAA;AAAA,EAGnE;AAEA,SAAOoB;AACT,GCrCaE,mBACXpC,CAAAA,aACG;AACH,QAAME,YAAYF,SAASC,QAAQC;AAEnC,MAAI,CAACA;AACH,WAAO,CAAA;AAGT,QAAMmC,aAAaX,yBAAuBxB,SAAS,GAC7CoC,WAAWC,qBAAqBrC,SAAS,GACzCsC,gBAAgBpC,8BAA8BiC,UAAU,GACxDI,cAAcrC,8BAA8BkC,QAAQ;AAE1D,MAAI,CAACE,iBAAiB,CAACC;AACrB,WAAO,CAAA;AAGT,QAAMC,kBAAkB1C,SAASO,cAAcC,IAAIgC,aAAa,GAC1DG,gBAAgB3C,SAASO,cAAcC,IAAIiC,WAAW;AAE5D,MAAIC,oBAAoBjC,UAAakC,kBAAkBlC;AACrD,WAAO,CAAA;AAGT,QAAMmC,cAAc5C,SAASC,QAAQU,MAAMkC,MACzCH,iBACAC,gBAAgB,CAClB;AAEA,SAAOG,YAAY;AAAA,IACjB7C,SAASD,SAASC;AAAAA,IAClB8C,QAAQH;AAAAA,EAAAA,CACT;AACH,GCvCaI,mBAA4ChD,CAAAA,aACjCoC,iBAAiBpC,QAAQ,EAE1BiD,OAAO,CAACC,MAAMC,UAC5BlC,YAAYjB,SAASC,SAASkD,KAAK,IAKtCD,OACAC,MAAM/B,SAAS6B,OAAO,CAACC,OAAMf,UACvBV,OAAOzB,SAASC,SAASkC,KAAK,IACzBe,QAAOf,MAAMe,OAGfA,OACN,EAAE,IAXEA,MAaR,EAAE,GCpBME,uBAAiDpD,CAAAA,aACvDA,SAASC,QAAQC,YAKpBmD,KAAKC,UAAUtD,SAASC,QAAQC,UAAU0B,OAAOf,IAAI,MACnDwC,KAAKC,UAAUtD,SAASC,QAAQC,UAAUG,MAAMQ,IAAI,KACtDb,SAASC,QAAQC,WAAW0B,OAAO2B,WACjCvD,SAASC,QAAQC,WAAWG,MAAMkD,SAP7B,ICDEC,sBAAgDxD,CAAAA,aACpD,CAACoD,qBAAqBpD,QAAQ;"}