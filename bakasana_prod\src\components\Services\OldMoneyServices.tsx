'use client';
import { useInView } from 'react-intersection-observer';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface ServiceItem {
  title: string;
  description: string;
  features: string[];
  href: string;
  buttonText: string;
  accent: 'primary' | 'secondary' | 'tertiary';
}

const services: ServiceItem[] = [
  {
    title: 'Retreaty na Bali',
    description:
      'Odkryj duchowe serce Indonezji w otoczeniu tarasów ryżowych i starożytnych świątyń.',
    features: [
      'Ubud - centrum duchowe',
      'Luksusowe boutique hotele',
      'Warsztaty medytacji',
      'Ceremonie oczyszczające',
    ],
    href: '/retreaty/bali',
    buttonText: 'Odkryj <PERSON>',
    accent: 'primary',
  },
  {
    title: 'Retreaty na Sri Lance',
    description:
      'Perła Oceanu Indyjskiego oferuje harmonię gór, plaż i tradycyjnej ajurwed<PERSON>.',
    features: [
      'Ayurveda i wellness',
      'Plantacje herbaty',
      'Dzikie safari',
      'Historyczne miasta',
    ],
    href: '/retreaty/sri-lanka',
    buttonText: '<PERSON>zna<PERSON>',
    accent: 'secondary',
  },
  {
    title: 'Zajęcia Online',
    description:
      'Praktykuj jogę premium z domu pod przewodnictwem doświadczonej instruktorki.',
    features: [
      'Zajęcia na żywo',
      'Indywidualne sesje',
      'Biblioteka nagrań',
      'Społeczność praktyków',
    ],
    href: '/zajecia-online',
    buttonText: 'Rozpocznij Praktykę',
    accent: 'tertiary',
  },
];

const OldMoneyServices: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section className='py-section bg-silk'>
      <div className='container mx-auto px-hero-padding'>
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className='text-center mb-20'
        >
          <h2 className='font-cormorant text-display-xl text-charcoal mb-md'>
            Nasze Usługi
          </h2>
          <div className='w-20 h-px bg-enterprise-brown mx-auto mb-md' />
          <p className='text-body-lg text-ash max-w-2xl mx-auto'>
            Oferujemy premium doświadczenia jogi łączące tradycyjną mądrość z
            nowoczesnym komfortem w najpiękniejszych zakątkach świata.
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial='hidden'
          animate={inView ? 'visible' : 'hidden'}
          className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-xl'
        >
          {services.map((service, index) => (
            <motion.div key={index} variants={itemVariants} className='group'>
              <div className='bg-white h-full transition-all duration-500 hover:shadow-premium-shadow hover:-translate-y-2 border border-sage/10 hover:border-enterprise-brown/20'>
                {/* Service Image Placeholder */}
                <div className='relative h-64 bg-gradient-to-br from-linen via-sanctuary to-silk overflow-hidden'>
                  <div className='absolute inset-0 bg-enterprise-brown/5 group-hover:bg-enterprise-brown/10 transition-all duration-500' />
                  <div className='absolute bottom-4 left-4 right-4'>
                    <div
                      className={`inline-block px-3 py-1 text-micro uppercase tracking-[2px] font-medium ${
                        service.accent === 'primary'
                          ? 'bg-enterprise-brown text-white'
                          : service.accent === 'secondary'
                            ? 'bg-terra text-white'
                            : 'bg-sand text-white'
                      }`}
                    >
                      Premium Experience
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className='p-8'>
                  <h3 className='font-cormorant text-heading text-charcoal mb-sm group-hover:text-enterprise-brown transition-colors duration-300'>
                    {service.title}
                  </h3>

                  <p className='text-body text-ash mb-md leading-relaxed'>
                    {service.description}
                  </p>

                  {/* Features List */}
                  <ul className='space-y-3 mb-lg'>
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className='flex items-start'>
                        <div className='w-1.5 h-1.5 bg-enterprise-brown mt-2 mr-3 flex-shrink-0' />
                        <span className='text-caption text-ash'>{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <Link
                    href={service.href}
                    className={`group/btn relative overflow-hidden inline-flex items-center justify-center w-full py-3 px-hero-padding text-small uppercase tracking-[1px] font-medium transition-all duration-500 ${
                      service.accent === 'primary'
                        ? 'border border-enterprise-brown text-enterprise-brown hover:bg-enterprise-brown hover:text-white'
                        : service.accent === 'secondary'
                          ? 'border border-terra text-terra hover:bg-terra hover:text-white'
                          : 'border border-sand text-sand hover:bg-sand hover:text-white'
                    }`}
                  >
                    <span className='relative z-10 flex items-center'>
                      {service.buttonText}
                      <svg
                        className='w-4 h-4 ml-2 transform group-hover/btn:translate-x-1 transition-transform duration-300'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M17 8l4 4m0 0l-4 4m4-4H3'
                        />
                      </svg>
                    </span>
                    <div
                      className={`absolute inset-0 transform scale-x-0 group-hover/btn:scale-x-100 transition-transform duration-500 origin-left ${
                        service.accent === 'primary'
                          ? 'bg-enterprise-brown'
                          : service.accent === 'secondary'
                            ? 'bg-terra'
                            : 'bg-sand'
                      }`}
                    />
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className='text-center mt-20'
        >
          <p className='text-body-lg text-ash mb-lg'>
            Nie wiesz, która opcja jest dla Ciebie najlepsza?
          </p>
          <Link
            href='/kontakt'
            className='inline-flex items-center justify-center px-12 py-4 border-2 border-enterprise-brown text-enterprise-brown text-small uppercase tracking-[2px] font-medium transition-all duration-500 hover:bg-enterprise-brown hover:text-white hover:-translate-y-1 hover:shadow-lg'
          >
            Umów Bezpłatną Konsultację
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default OldMoneyServices;
