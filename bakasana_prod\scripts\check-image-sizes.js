const fs = require('fs');
const path = require('path');

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function getFileSize(filePath) {
  try {
    return fs.statSync(filePath).size;
  } catch (error) {
    return 0;
  }
}

function findImages(dir, extensions = ['.jpg', '.jpeg', '.png']) {
  const images = [];
  
  try {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory() && !['optimized', 'webp', 'avif'].includes(file)) {
        images.push(...findImages(filePath, extensions));
      } else if (extensions.some(ext => file.toLowerCase().endsWith(ext))) {
        images.push(filePath);
      }
    });
  } catch (error) {
    // Directory doesn't exist
  }

  return images;
}

function analyzeImageOptimization() {
  console.log('📊 ANALIZA OPTYMALIZACJI OBRAZKÓW');
  console.log('='.repeat(50));
  
  const baseDir = path.join(__dirname, '../public/images');
  const optimizedDir = path.join(baseDir, 'optimized');
  const webpDir = path.join(baseDir, 'webp');
  const avifDir = path.join(baseDir, 'avif');
  
  const originalImages = findImages(baseDir);
  
  let totalOriginal = 0;
  let totalOptimized = 0;
  let totalWebp = 0;
  let totalAvif = 0;
  
  console.log('\n📸 Porównanie rozmiarów plików:\n');
  console.log('Plik'.padEnd(40) + 'Oryginalny'.padEnd(12) + 'Optimized'.padEnd(12) + 'WebP'.padEnd(12) + 'AVIF'.padEnd(12) + 'Oszczędności');
  console.log('-'.repeat(100));
  
  originalImages.forEach(imagePath => {
    const relativePath = path.relative(baseDir, imagePath);
    const filename = path.basename(imagePath, path.extname(imagePath));
    const dir = path.dirname(relativePath);
    
    // Original size
    const originalSize = getFileSize(imagePath);
    totalOriginal += originalSize;
    
    // Optimized size
    const optimizedPath = path.join(optimizedDir, relativePath);
    const optimizedSize = getFileSize(optimizedPath);
    if (optimizedSize > 0) totalOptimized += optimizedSize;
    
    // WebP size
    const webpPath = path.join(webpDir, dir, filename + '.webp');
    const webpSize = getFileSize(webpPath);
    if (webpSize > 0) totalWebp += webpSize;
    
    // AVIF size
    const avifPath = path.join(avifDir, dir, filename + '.avif');
    const avifSize = getFileSize(avifPath);
    if (avifSize > 0) totalAvif += avifSize;
    
    // Calculate savings
    const bestSize = Math.min(
      optimizedSize || originalSize,
      webpSize || originalSize,
      avifSize || originalSize
    );
    const savings = originalSize > 0 ? Math.round(((originalSize - bestSize) / originalSize) * 100) : 0;
    
    console.log(
      relativePath.padEnd(40) +
      formatBytes(originalSize).padEnd(12) +
      (optimizedSize > 0 ? formatBytes(optimizedSize) : '-').padEnd(12) +
      (webpSize > 0 ? formatBytes(webpSize) : '-').padEnd(12) +
      (avifSize > 0 ? formatBytes(avifSize) : '-').padEnd(12) +
      `${savings}%`
    );
  });
  
  console.log('-'.repeat(100));
  console.log(
    'RAZEM'.padEnd(40) +
    formatBytes(totalOriginal).padEnd(12) +
    formatBytes(totalOptimized).padEnd(12) +
    formatBytes(totalWebp).padEnd(12) +
    formatBytes(totalAvif).padEnd(12)
  );
  
  // Calculate total savings
  const optimizedSavings = totalOriginal > 0 ? Math.round(((totalOriginal - totalOptimized) / totalOriginal) * 100) : 0;
  const webpSavings = totalOriginal > 0 ? Math.round(((totalOriginal - totalWebp) / totalOriginal) * 100) : 0;
  const avifSavings = totalOriginal > 0 ? Math.round(((totalOriginal - totalAvif) / totalOriginal) * 100) : 0;
  
  console.log('\n🎯 PODSUMOWANIE OSZCZĘDNOŚCI:');
  console.log('='.repeat(35));
  console.log(`📁 Rozmiar oryginalny: ${formatBytes(totalOriginal)}`);
  console.log(`🗜️ Po kompresji: ${formatBytes(totalOptimized)} (${optimizedSavings}% mniej)`);
  console.log(`🌐 Format WebP: ${formatBytes(totalWebp)} (${webpSavings}% mniej)`);
  console.log(`🚀 Format AVIF: ${formatBytes(totalAvif)} (${avifSavings}% mniej)`);
  
  const bestTotal = Math.min(totalOptimized || totalOriginal, totalWebp || totalOriginal, totalAvif || totalOriginal);
  const bestSavings = totalOriginal > 0 ? Math.round(((totalOriginal - bestTotal) / totalOriginal) * 100) : 0;
  
  console.log(`\n🏆 NAJLEPSZY WYNIK: ${formatBytes(bestTotal)} (${bestSavings}% oszczędności!)`);
  
  // Performance impact
  console.log('\n⚡ WPŁYW NA PERFORMANCE:');
  console.log('='.repeat(30));
  console.log(`📈 Oczekiwany boost Lighthouse: +${Math.min(bestSavings, 30)} punktów`);
  console.log(`🚀 Szybkość ładowania: ${bestSavings > 50 ? 'Znacznie szybciej' : bestSavings > 30 ? 'Szybciej' : 'Nieco szybciej'}`);
  console.log(`💾 Oszczędność transferu: ${formatBytes(totalOriginal - bestTotal)}`);
  
  // Recommendations
  console.log('\n💡 REKOMENDACJE:');
  console.log('='.repeat(20));
  if (avifSavings > webpSavings) {
    console.log('✅ Użyj AVIF jako główny format (najlepsze oszczędności)');
    console.log('✅ WebP jako fallback dla starszych przeglądarek');
  } else if (webpSavings > optimizedSavings) {
    console.log('✅ Użyj WebP jako główny format');
    console.log('✅ Skompresowane JPEG/PNG jako fallback');
  } else {
    console.log('✅ Użyj skompresowanych obrazków');
  }
  console.log('✅ Zaimplementuj <picture> element z fallback');
  console.log('✅ Dodaj lazy loading dla obrazków poniżej fold');
}

if (require.main === module) {
  analyzeImageOptimization();
}

module.exports = { analyzeImageOptimization };
