{"version": 3, "file": "loadEnv.js", "sources": ["../../src/debug.ts", "../../src/util/resolveRootDir.ts", "../../../../../node_modules/.pnpm/dotenv@16.6.1/node_modules/dotenv/lib/main.js", "../../../../../node_modules/.pnpm/dotenv-expand@9.0.0/node_modules/dotenv-expand/lib/main.js", "../../src/util/loadEnv.ts"], "sourcesContent": ["import debugIt from 'debug'\n\nexport const debug = debugIt('sanity:cli')\n", "/* eslint-disable no-sync */\nimport fs from 'node:fs'\nimport path from 'node:path'\n\nimport {debug} from '../debug'\n\n/**\n * Resolve project root directory, falling back to cwd if it cannot be found\n */\nexport function resolveRootDir(cwd: string): string {\n  try {\n    return resolveProjectRoot(cwd) || cwd\n  } catch (err) {\n    throw new Error(`<PERSON>rro<PERSON> occurred trying to resolve project root:\\n${err.message}`)\n  }\n}\n\nfunction hasSanityConfig(basePath: string, configName: string): boolean {\n  const buildConfigs = [\n    fileExists(path.join(basePath, `${configName}.js`)),\n    fileExists(path.join(basePath, `${configName}.ts`)),\n    isSanityV2StudioRoot(basePath),\n  ]\n\n  return buildConfigs.some(Boolean)\n}\n\nfunction resolveProjectRoot(basePath: string, iterations = 0): string | false {\n  const configName = 'sanity.config'\n  if (hasSanityConfig(basePath, configName)) {\n    return basePath\n  }\n\n  const parentDir = path.resolve(basePath, '..')\n  if (parentDir === basePath || iterations > 30) {\n    // Reached root (or max depth), give up\n    return false\n  }\n\n  return resolveProjectRoot(parentDir, iterations + 1)\n}\n\nfunction isSanityV2StudioRoot(basePath: string): boolean {\n  try {\n    const content = fs.readFileSync(path.join(basePath, 'sanity.json'), 'utf8')\n    const sanityJson = JSON.parse(content)\n    const isRoot = Boolean(sanityJson?.root)\n    if (isRoot) {\n      debug('Found Sanity v2 studio root at %s', basePath)\n    }\n    return isRoot\n  } catch (err) {\n    return false\n  }\n}\n\nfunction fileExists(filePath: string): boolean {\n  return fs.existsSync(filePath)\n}\n", "const fs = require('fs')\nconst path = require('path')\nconst os = require('os')\nconst crypto = require('crypto')\nconst packageJson = require('../package.json')\n\nconst version = packageJson.version\n\nconst LINE = /(?:^|^)\\s*(?:export\\s+)?([\\w.-]+)(?:\\s*=\\s*?|:\\s+?)(\\s*'(?:\\\\'|[^'])*'|\\s*\"(?:\\\\\"|[^\"])*\"|\\s*`(?:\\\\`|[^`])*`|[^#\\r\\n]+)?\\s*(?:#.*)?(?:$|$)/mg\n\n// Parse src into an Object\nfunction parse (src) {\n  const obj = {}\n\n  // Convert buffer to string\n  let lines = src.toString()\n\n  // Convert line breaks to same format\n  lines = lines.replace(/\\r\\n?/mg, '\\n')\n\n  let match\n  while ((match = LINE.exec(lines)) != null) {\n    const key = match[1]\n\n    // Default undefined or null to empty string\n    let value = (match[2] || '')\n\n    // Remove whitespace\n    value = value.trim()\n\n    // Check if double quoted\n    const maybeQuote = value[0]\n\n    // Remove surrounding quotes\n    value = value.replace(/^(['\"`])([\\s\\S]*)\\1$/mg, '$2')\n\n    // Expand newlines if double quoted\n    if (maybeQuote === '\"') {\n      value = value.replace(/\\\\n/g, '\\n')\n      value = value.replace(/\\\\r/g, '\\r')\n    }\n\n    // Add to object\n    obj[key] = value\n  }\n\n  return obj\n}\n\nfunction _parseVault (options) {\n  options = options || {}\n\n  const vaultPath = _vaultPath(options)\n  options.path = vaultPath // parse .env.vault\n  const result = DotenvModule.configDotenv(options)\n  if (!result.parsed) {\n    const err = new Error(`MISSING_DATA: Cannot parse ${vaultPath} for an unknown reason`)\n    err.code = 'MISSING_DATA'\n    throw err\n  }\n\n  // handle scenario for comma separated keys - for use with key rotation\n  // example: DOTENV_KEY=\"dotenv://:<EMAIL>/vault/.env.vault?environment=prod,dotenv://:<EMAIL>/vault/.env.vault?environment=prod\"\n  const keys = _dotenvKey(options).split(',')\n  const length = keys.length\n\n  let decrypted\n  for (let i = 0; i < length; i++) {\n    try {\n      // Get full key\n      const key = keys[i].trim()\n\n      // Get instructions for decrypt\n      const attrs = _instructions(result, key)\n\n      // Decrypt\n      decrypted = DotenvModule.decrypt(attrs.ciphertext, attrs.key)\n\n      break\n    } catch (error) {\n      // last key\n      if (i + 1 >= length) {\n        throw error\n      }\n      // try next key\n    }\n  }\n\n  // Parse decrypted .env string\n  return DotenvModule.parse(decrypted)\n}\n\nfunction _warn (message) {\n  console.log(`[dotenv@${version}][WARN] ${message}`)\n}\n\nfunction _debug (message) {\n  console.log(`[dotenv@${version}][DEBUG] ${message}`)\n}\n\nfunction _log (message) {\n  console.log(`[dotenv@${version}] ${message}`)\n}\n\nfunction _dotenvKey (options) {\n  // prioritize developer directly setting options.DOTENV_KEY\n  if (options && options.DOTENV_KEY && options.DOTENV_KEY.length > 0) {\n    return options.DOTENV_KEY\n  }\n\n  // secondary infra already contains a DOTENV_KEY environment variable\n  if (process.env.DOTENV_KEY && process.env.DOTENV_KEY.length > 0) {\n    return process.env.DOTENV_KEY\n  }\n\n  // fallback to empty string\n  return ''\n}\n\nfunction _instructions (result, dotenvKey) {\n  // Parse DOTENV_KEY. Format is a URI\n  let uri\n  try {\n    uri = new URL(dotenvKey)\n  } catch (error) {\n    if (error.code === 'ERR_INVALID_URL') {\n      const err = new Error('INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    }\n\n    throw error\n  }\n\n  // Get decrypt key\n  const key = uri.password\n  if (!key) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing key part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get environment\n  const environment = uri.searchParams.get('environment')\n  if (!environment) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing environment part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get ciphertext payload\n  const environmentKey = `DOTENV_VAULT_${environment.toUpperCase()}`\n  const ciphertext = result.parsed[environmentKey] // DOTENV_VAULT_PRODUCTION\n  if (!ciphertext) {\n    const err = new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${environmentKey} in your .env.vault file.`)\n    err.code = 'NOT_FOUND_DOTENV_ENVIRONMENT'\n    throw err\n  }\n\n  return { ciphertext, key }\n}\n\nfunction _vaultPath (options) {\n  let possibleVaultPath = null\n\n  if (options && options.path && options.path.length > 0) {\n    if (Array.isArray(options.path)) {\n      for (const filepath of options.path) {\n        if (fs.existsSync(filepath)) {\n          possibleVaultPath = filepath.endsWith('.vault') ? filepath : `${filepath}.vault`\n        }\n      }\n    } else {\n      possibleVaultPath = options.path.endsWith('.vault') ? options.path : `${options.path}.vault`\n    }\n  } else {\n    possibleVaultPath = path.resolve(process.cwd(), '.env.vault')\n  }\n\n  if (fs.existsSync(possibleVaultPath)) {\n    return possibleVaultPath\n  }\n\n  return null\n}\n\nfunction _resolveHome (envPath) {\n  return envPath[0] === '~' ? path.join(os.homedir(), envPath.slice(1)) : envPath\n}\n\nfunction _configVault (options) {\n  const debug = Boolean(options && options.debug)\n  const quiet = options && 'quiet' in options ? options.quiet : true\n\n  if (debug || !quiet) {\n    _log('Loading env from encrypted .env.vault')\n  }\n\n  const parsed = DotenvModule._parseVault(options)\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsed, options)\n\n  return { parsed }\n}\n\nfunction configDotenv (options) {\n  const dotenvPath = path.resolve(process.cwd(), '.env')\n  let encoding = 'utf8'\n  const debug = Boolean(options && options.debug)\n  const quiet = options && 'quiet' in options ? options.quiet : true\n\n  if (options && options.encoding) {\n    encoding = options.encoding\n  } else {\n    if (debug) {\n      _debug('No encoding is specified. UTF-8 is used by default')\n    }\n  }\n\n  let optionPaths = [dotenvPath] // default, look for .env\n  if (options && options.path) {\n    if (!Array.isArray(options.path)) {\n      optionPaths = [_resolveHome(options.path)]\n    } else {\n      optionPaths = [] // reset default\n      for (const filepath of options.path) {\n        optionPaths.push(_resolveHome(filepath))\n      }\n    }\n  }\n\n  // Build the parsed data in a temporary object (because we need to return it).  Once we have the final\n  // parsed data, we will combine it with process.env (or options.processEnv if provided).\n  let lastError\n  const parsedAll = {}\n  for (const path of optionPaths) {\n    try {\n      // Specifying an encoding returns a string instead of a buffer\n      const parsed = DotenvModule.parse(fs.readFileSync(path, { encoding }))\n\n      DotenvModule.populate(parsedAll, parsed, options)\n    } catch (e) {\n      if (debug) {\n        _debug(`Failed to load ${path} ${e.message}`)\n      }\n      lastError = e\n    }\n  }\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsedAll, options)\n\n  if (debug || !quiet) {\n    const keysCount = Object.keys(parsedAll).length\n    const shortPaths = []\n    for (const filePath of optionPaths) {\n      try {\n        const relative = path.relative(process.cwd(), filePath)\n        shortPaths.push(relative)\n      } catch (e) {\n        if (debug) {\n          _debug(`Failed to load ${filePath} ${e.message}`)\n        }\n        lastError = e\n      }\n    }\n\n    _log(`injecting env (${keysCount}) from ${shortPaths.join(',')}`)\n  }\n\n  if (lastError) {\n    return { parsed: parsedAll, error: lastError }\n  } else {\n    return { parsed: parsedAll }\n  }\n}\n\n// Populates process.env from .env file\nfunction config (options) {\n  // fallback to original dotenv if DOTENV_KEY is not set\n  if (_dotenvKey(options).length === 0) {\n    return DotenvModule.configDotenv(options)\n  }\n\n  const vaultPath = _vaultPath(options)\n\n  // dotenvKey exists but .env.vault file does not exist\n  if (!vaultPath) {\n    _warn(`You set DOTENV_KEY but you are missing a .env.vault file at ${vaultPath}. Did you forget to build it?`)\n\n    return DotenvModule.configDotenv(options)\n  }\n\n  return DotenvModule._configVault(options)\n}\n\nfunction decrypt (encrypted, keyStr) {\n  const key = Buffer.from(keyStr.slice(-64), 'hex')\n  let ciphertext = Buffer.from(encrypted, 'base64')\n\n  const nonce = ciphertext.subarray(0, 12)\n  const authTag = ciphertext.subarray(-16)\n  ciphertext = ciphertext.subarray(12, -16)\n\n  try {\n    const aesgcm = crypto.createDecipheriv('aes-256-gcm', key, nonce)\n    aesgcm.setAuthTag(authTag)\n    return `${aesgcm.update(ciphertext)}${aesgcm.final()}`\n  } catch (error) {\n    const isRange = error instanceof RangeError\n    const invalidKeyLength = error.message === 'Invalid key length'\n    const decryptionFailed = error.message === 'Unsupported state or unable to authenticate data'\n\n    if (isRange || invalidKeyLength) {\n      const err = new Error('INVALID_DOTENV_KEY: It must be 64 characters long (or more)')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    } else if (decryptionFailed) {\n      const err = new Error('DECRYPTION_FAILED: Please check your DOTENV_KEY')\n      err.code = 'DECRYPTION_FAILED'\n      throw err\n    } else {\n      throw error\n    }\n  }\n}\n\n// Populate process.env with parsed values\nfunction populate (processEnv, parsed, options = {}) {\n  const debug = Boolean(options && options.debug)\n  const override = Boolean(options && options.override)\n\n  if (typeof parsed !== 'object') {\n    const err = new Error('OBJECT_REQUIRED: Please check the processEnv argument being passed to populate')\n    err.code = 'OBJECT_REQUIRED'\n    throw err\n  }\n\n  // Set process.env\n  for (const key of Object.keys(parsed)) {\n    if (Object.prototype.hasOwnProperty.call(processEnv, key)) {\n      if (override === true) {\n        processEnv[key] = parsed[key]\n      }\n\n      if (debug) {\n        if (override === true) {\n          _debug(`\"${key}\" is already defined and WAS overwritten`)\n        } else {\n          _debug(`\"${key}\" is already defined and was NOT overwritten`)\n        }\n      }\n    } else {\n      processEnv[key] = parsed[key]\n    }\n  }\n}\n\nconst DotenvModule = {\n  configDotenv,\n  _configVault,\n  _parseVault,\n  config,\n  decrypt,\n  parse,\n  populate\n}\n\nmodule.exports.configDotenv = DotenvModule.configDotenv\nmodule.exports._configVault = DotenvModule._configVault\nmodule.exports._parseVault = DotenvModule._parseVault\nmodule.exports.config = DotenvModule.config\nmodule.exports.decrypt = DotenvModule.decrypt\nmodule.exports.parse = DotenvModule.parse\nmodule.exports.populate = DotenvModule.populate\n\nmodule.exports = DotenvModule\n", "'use strict'\n\nfunction _interpolate (envValue, environment, config) {\n  const matches = envValue.match(/(.?\\${*[\\w]*(?::-[\\w/]*)?}*)/g) || []\n\n  return matches.reduce(function (newEnv, match, index) {\n    const parts = /(.?)\\${*([\\w]*(?::-[\\w/]*)?)?}*/g.exec(match)\n    if (!parts || parts.length === 0) {\n      return newEnv\n    }\n\n    const prefix = parts[1]\n\n    let value, replacePart\n\n    if (prefix === '\\\\') {\n      replacePart = parts[0]\n      value = replacePart.replace('\\\\$', '$')\n    } else {\n      const keyParts = parts[2].split(':-')\n      const key = keyParts[0]\n      replacePart = parts[0].substring(prefix.length)\n      // process.env value 'wins' over .env file's value\n      value = Object.prototype.hasOwnProperty.call(environment, key)\n        ? environment[key]\n        : (config.parsed[key] || keyParts[1] || '')\n\n      // If the value is found, remove nested expansions.\n      if (keyParts.length > 1 && value) {\n        const replaceNested = matches[index + 1]\n        matches[index + 1] = ''\n\n        newEnv = newEnv.replace(replaceNested, '')\n      }\n      // Resolve recursive interpolations\n      value = _interpolate(value, environment, config)\n    }\n\n    return newEnv.replace(replacePart, value)\n  }, envValue)\n}\n\nfunction expand (config) {\n  // if ignoring process.env, use a blank object\n  const environment = config.ignoreProcessEnv ? {} : process.env\n\n  for (const configKey in config.parsed) {\n    const value = Object.prototype.hasOwnProperty.call(environment, configKey) ? environment[configKey] : config.parsed[configKey]\n\n    config.parsed[configKey] = _interpolate(value, environment, config)\n  }\n\n  for (const processKey in config.parsed) {\n    environment[processKey] = config.parsed[processKey]\n  }\n\n  return config\n}\n\nmodule.exports.expand = expand\n", "/**\n * This is an \"inlined\" version of Vite's `loadEnv` function,\n * simplified somewhat to only support our use case.\n *\n * Ideally we'd just use `loadEnv` from Vite, but importing it\n * causes bundling issues due to node APIs and downstream dependencies.\n *\n * Vite is MIT licensed, copyright (c) Yu<PERSON> (Evan) You and Vite contributors.\n */\n\n/* eslint-disable no-process-env */\nimport fs from 'node:fs'\nimport path from 'node:path'\n\nimport {parse} from 'dotenv'\nimport {expand} from 'dotenv-expand'\n\nexport function loadEnv(\n  mode: string,\n  envDir: string,\n  prefixes: string[] = ['VITE_'],\n): Record<string, string> {\n  if (mode === 'local') {\n    throw new Error(\n      `\"local\" cannot be used as a mode name because it conflicts with ` +\n        `the .local postfix for .env files.`,\n    )\n  }\n\n  const env: Record<string, string> = {}\n  const envFiles = [\n    /** default file */ `.env`,\n    /** local file */ `.env.local`,\n    /** mode file */ `.env.${mode}`,\n    /** mode local file */ `.env.${mode}.local`,\n  ]\n\n  const parsed = Object.fromEntries(\n    envFiles.flatMap((file) => {\n      const envPath = lookupFile(envDir, [file], {\n        rootDir: envDir,\n      })\n      if (!envPath) return []\n      return Object.entries(parse(fs.readFileSync(envPath)))\n    }),\n  )\n\n  // test NODE_ENV override before expand as otherwise process.env.NODE_ENV would override this\n  if (parsed.NODE_ENV && process.env.VITE_USER_NODE_ENV === undefined) {\n    process.env.VITE_USER_NODE_ENV = parsed.NODE_ENV\n  }\n  // support BROWSER and BROWSER_ARGS env variables\n  if (parsed.BROWSER && process.env.BROWSER === undefined) {\n    process.env.BROWSER = parsed.BROWSER\n  }\n  if (parsed.BROWSER_ARGS && process.env.BROWSER_ARGS === undefined) {\n    process.env.BROWSER_ARGS = parsed.BROWSER_ARGS\n  }\n\n  try {\n    // let environment variables use each other\n    expand({parsed})\n  } catch (e) {\n    // custom error handling until https://github.com/motdotla/dotenv-expand/issues/65 is fixed upstream\n    // check for message \"TypeError: Cannot read properties of undefined (reading 'split')\"\n    if (e.message.includes('split')) {\n      throw new Error('dotenv-expand failed to expand env vars. Maybe you need to escape `$`?')\n    }\n    throw e\n  }\n\n  // only keys that start with prefix are exposed to client\n  for (const [key, value] of Object.entries(parsed)) {\n    if (prefixes.some((prefix) => key.startsWith(prefix))) {\n      env[key] = value\n    }\n  }\n\n  // check if there are actual env variables starting with VITE_*\n  // these are typically provided inline and should be prioritized\n  for (const key in process.env) {\n    if (prefixes.some((prefix) => key.startsWith(prefix))) {\n      env[key] = process.env[key] as string\n    }\n  }\n\n  return env\n}\n\nfunction lookupFile(\n  dir: string,\n  formats: string[],\n  options?: {\n    rootDir?: string\n  },\n): string | undefined {\n  for (const format of formats) {\n    const fullPath = path.join(dir, format)\n    // eslint-disable-next-line no-sync\n    if (fs.existsSync(fullPath) && fs.statSync(fullPath).isFile()) {\n      return fullPath\n    }\n  }\n  const parentDir = path.dirname(dir)\n  if (parentDir !== dir && (!options?.rootDir || parentDir.startsWith(options?.rootDir))) {\n    return lookupFile(parentDir, formats, options)\n  }\n\n  return undefined\n}\n"], "names": ["debugIt", "path", "fs", "require$$0", "require$$1", "require$$2", "require$$3", "version", "debug", "mainModule", "parse", "expand"], "mappings": ";;;;;;AAEa,MAAA,QAAQA,yBAAQ,YAAY;ACOlC,SAAS,eAAe,KAAqB;AAC9C,MAAA;AACK,WAAA,mBAAmB,GAAG,KAAK;AAAA,WAC3B,KAAK;AACZ,UAAM,IAAI,MAAM;AAAA,EAAmD,IAAI,OAAO,EAAE;AAAA,EAAA;AAEpF;AAEA,SAAS,gBAAgB,UAAkB,YAA6B;AACjD,SAAA;AAAA,IACnB,WAAWC,cAAK,QAAA,KAAK,UAAU,GAAG,UAAU,KAAK,CAAC;AAAA,IAClD,WAAWA,cAAK,QAAA,KAAK,UAAU,GAAG,UAAU,KAAK,CAAC;AAAA,IAClD,qBAAqB,QAAQ;AAAA,EAAA,EAGX,KAAK,OAAO;AAClC;AAEA,SAAS,mBAAmB,UAAkB,aAAa,GAAmB;AAExE,MAAA,gBAAgB,UADD,eACqB;AAC/B,WAAA;AAGT,QAAM,YAAYA,cAAA,QAAK,QAAQ,UAAU,IAAI;AACzC,SAAA,cAAc,YAAY,aAAa,KAElC,KAGF,mBAAmB,WAAW,aAAa,CAAC;AACrD;AAEA,SAAS,qBAAqB,UAA2B;AACnD,MAAA;AACF,UAAM,UAAUC,YAAAA,QAAG,aAAaD,cAAAA,QAAK,KAAK,UAAU,aAAa,GAAG,MAAM,GAEpE,SAAS,CAAA,CADI,KAAK,MAAM,OAAO,GACF;AACnC,WAAI,UACF,MAAM,qCAAqC,QAAQ,GAE9C;AAAA,EAAA,QACK;AACL,WAAA;AAAA,EAAA;AAEX;AAEA,SAAS,WAAW,UAA2B;AACtC,SAAAC,YAAA,QAAG,WAAW,QAAQ;AAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1DA,QAAMA,MAAKC,sBAAAA,SACLF,QAAOG,oBAAAA,SACP,KAAKC,sBAAAA,SACL,SAASC,oBAAAA,SAGTC,WAFc,WAEQ,SAEtB,OAAO;AAGb,WAAS,MAAO,KAAK;AACnB,UAAM,MAAM,CAAA;AAGZ,QAAI,QAAQ,IAAI,SAAQ;AAGxB,YAAQ,MAAM,QAAQ,WAAW;AAAA,CAAI;AAErC,QAAI;AACJ,YAAQ,QAAQ,KAAK,KAAK,KAAK,MAAM,QAAM;AACzC,YAAM,MAAM,MAAM,CAAC;AAGnB,UAAI,QAAS,MAAM,CAAC,KAAK;AAGzB,cAAQ,MAAM,KAAI;AAGlB,YAAM,aAAa,MAAM,CAAC;AAG1B,cAAQ,MAAM,QAAQ,0BAA0B,IAAI,GAGhD,eAAe,QACjB,QAAQ,MAAM,QAAQ,QAAQ;AAAA,CAAI,GAClC,QAAQ,MAAM,QAAQ,QAAQ,IAAI,IAIpC,IAAI,GAAG,IAAI;AAAA,IACf;AAEE,WAAO;AAAA,EACT;AAEA,WAAS,YAAa,SAAS;AAC7B,cAAU,WAAW,CAAA;AAErB,UAAM,YAAY,WAAW,OAAO;AACpC,YAAQ,OAAO;AACf,UAAM,SAAS,aAAa,aAAa,OAAO;AAChD,QAAI,CAAC,OAAO,QAAQ;AAClB,YAAM,MAAM,IAAI,MAAM,8BAA8B,SAAS,wBAAwB;AACrF,gBAAI,OAAO,gBACL;AAAA,IACV;AAIE,UAAM,OAAO,WAAW,OAAO,EAAE,MAAM,GAAG,GACpC,SAAS,KAAK;AAEpB,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,QAAQ;AAC1B,UAAI;AAEF,cAAM,MAAM,KAAK,CAAC,EAAE,KAAI,GAGlB,QAAQ,cAAc,QAAQ,GAAG;AAGvC,oBAAY,aAAa,QAAQ,MAAM,YAAY,MAAM,GAAG;AAE5D;AAAA,MACD,SAAQ,OAAO;AAEd,YAAI,IAAI,KAAK;AACX,gBAAM;AAAA,MAGd;AAIE,WAAO,aAAa,MAAM,SAAS;AAAA,EACrC;AAEA,WAAS,MAAO,SAAS;AACvB,YAAQ,IAAI,WAAWA,QAAO,WAAW,OAAO,EAAE;AAAA,EACpD;AAEA,WAAS,OAAQ,SAAS;AACxB,YAAQ,IAAI,WAAWA,QAAO,YAAY,OAAO,EAAE;AAAA,EACrD;AAEA,WAAS,KAAM,SAAS;AACtB,YAAQ,IAAI,WAAWA,QAAO,KAAK,OAAO,EAAE;AAAA,EAC9C;AAEA,WAAS,WAAY,SAAS;AAE5B,WAAI,WAAW,QAAQ,cAAc,QAAQ,WAAW,SAAS,IACxD,QAAQ,aAIb,QAAQ,IAAI,cAAc,QAAQ,IAAI,WAAW,SAAS,IACrD,QAAQ,IAAI,aAId;AAAA,EACT;AAEA,WAAS,cAAe,QAAQ,WAAW;AAEzC,QAAI;AACJ,QAAI;AACF,YAAM,IAAI,IAAI,SAAS;AAAA,IACxB,SAAQ,OAAO;AACd,UAAI,MAAM,SAAS,mBAAmB;AACpC,cAAM,MAAM,IAAI,MAAM,4IAA4I;AAClK,kBAAI,OAAO,sBACL;AAAA,MACZ;AAEI,YAAM;AAAA,IACV;AAGE,UAAM,MAAM,IAAI;AAChB,QAAI,CAAC,KAAK;AACR,YAAM,MAAM,IAAI,MAAM,sCAAsC;AAC5D,gBAAI,OAAO,sBACL;AAAA,IACV;AAGE,UAAM,cAAc,IAAI,aAAa,IAAI,aAAa;AACtD,QAAI,CAAC,aAAa;AAChB,YAAM,MAAM,IAAI,MAAM,8CAA8C;AACpE,gBAAI,OAAO,sBACL;AAAA,IACV;AAGE,UAAM,iBAAiB,gBAAgB,YAAY,YAAW,CAAE,IAC1D,aAAa,OAAO,OAAO,cAAc;AAC/C,QAAI,CAAC,YAAY;AACf,YAAM,MAAM,IAAI,MAAM,2DAA2D,cAAc,2BAA2B;AAC1H,gBAAI,OAAO,gCACL;AAAA,IACV;AAEE,WAAO,EAAE,YAAY,IAAG;AAAA,EAC1B;AAEA,WAAS,WAAY,SAAS;AAC5B,QAAI,oBAAoB;AAExB,QAAI,WAAW,QAAQ,QAAQ,QAAQ,KAAK,SAAS;AACnD,UAAI,MAAM,QAAQ,QAAQ,IAAI;AAC5B,mBAAW,YAAY,QAAQ;AAC7B,UAAIL,IAAG,WAAW,QAAQ,MACxB,oBAAoB,SAAS,SAAS,QAAQ,IAAI,WAAW,GAAG,QAAQ;AAAA;AAI5E,4BAAoB,QAAQ,KAAK,SAAS,QAAQ,IAAI,QAAQ,OAAO,GAAG,QAAQ,IAAI;AAAA;AAGtF,0BAAoBD,MAAK,QAAQ,QAAQ,IAAK,GAAE,YAAY;AAG9D,WAAIC,IAAG,WAAW,iBAAiB,IAC1B,oBAGF;AAAA,EACT;AAEA,WAAS,aAAc,SAAS;AAC9B,WAAO,QAAQ,CAAC,MAAM,MAAMD,MAAK,KAAK,GAAG,QAAS,GAAE,QAAQ,MAAM,CAAC,CAAC,IAAI;AAAA,EAC1E;AAEA,WAAS,aAAc,SAAS;AAC9B,UAAMO,SAAQ,GAAQ,WAAW,QAAQ,QACnC,QAAQ,WAAW,WAAW,UAAU,QAAQ,QAAQ;AAE9D,KAAIA,UAAS,CAAC,UACZ,KAAK,uCAAuC;AAG9C,UAAM,SAAS,aAAa,YAAY,OAAO;AAE/C,QAAI,aAAa,QAAQ;AACzB,WAAI,WAAW,QAAQ,cAAc,SACnC,aAAa,QAAQ,aAGvB,aAAa,SAAS,YAAY,QAAQ,OAAO,GAE1C,EAAE,OAAM;AAAA,EACjB;AAEA,WAAS,aAAc,SAAS;AAC9B,UAAM,aAAaP,MAAK,QAAQ,QAAQ,IAAK,GAAE,MAAM;AACrD,QAAI,WAAW;AACf,UAAMO,SAAQ,GAAQ,WAAW,QAAQ,QACnC,QAAQ,WAAW,WAAW,UAAU,QAAQ,QAAQ;AAE9D,IAAI,WAAW,QAAQ,WACrB,WAAW,QAAQ,WAEfA,UACF,OAAO,oDAAoD;AAI/D,QAAI,cAAc,CAAC,UAAU;AAC7B,QAAI,WAAW,QAAQ;AACrB,UAAI,CAAC,MAAM,QAAQ,QAAQ,IAAI;AAC7B,sBAAc,CAAC,aAAa,QAAQ,IAAI,CAAC;AAAA,WACpC;AACL,sBAAc,CAAE;AAChB,mBAAW,YAAY,QAAQ;AAC7B,sBAAY,KAAK,aAAa,QAAQ,CAAC;AAAA,MAE/C;AAKE,QAAI;AACJ,UAAM,YAAY,CAAA;AAClB,eAAWP,SAAQ;AACjB,UAAI;AAEF,cAAM,SAAS,aAAa,MAAMC,IAAG,aAAaD,OAAM,EAAE,UAAU,CAAC;AAErE,qBAAa,SAAS,WAAW,QAAQ,OAAO;AAAA,MACjD,SAAQ,GAAG;AACV,QAAIO,UACF,OAAO,kBAAkBP,KAAI,IAAI,EAAE,OAAO,EAAE,GAE9C,YAAY;AAAA,MAClB;AAGE,QAAI,aAAa,QAAQ;AAOzB,QANI,WAAW,QAAQ,cAAc,SACnC,aAAa,QAAQ,aAGvB,aAAa,SAAS,YAAY,WAAW,OAAO,GAEhDO,UAAS,CAAC,OAAO;AACnB,YAAM,YAAY,OAAO,KAAK,SAAS,EAAE,QACnC,aAAa,CAAA;AACnB,iBAAW,YAAY;AACrB,YAAI;AACF,gBAAM,WAAWP,MAAK,SAAS,QAAQ,IAAK,GAAE,QAAQ;AACtD,qBAAW,KAAK,QAAQ;AAAA,QACzB,SAAQ,GAAG;AACV,UAAIO,UACF,OAAO,kBAAkB,QAAQ,IAAI,EAAE,OAAO,EAAE,GAElD,YAAY;AAAA,QACpB;AAGI,WAAK,kBAAkB,SAAS,UAAU,WAAW,KAAK,GAAG,CAAC,EAAE;AAAA,IACpE;AAEE,WAAI,YACK,EAAE,QAAQ,WAAW,OAAO,UAAS,IAErC,EAAE,QAAQ,UAAS;AAAA,EAE9B;AAGA,WAAS,OAAQ,SAAS;AAExB,QAAI,WAAW,OAAO,EAAE,WAAW;AACjC,aAAO,aAAa,aAAa,OAAO;AAG1C,UAAM,YAAY,WAAW,OAAO;AAGpC,WAAK,YAME,aAAa,aAAa,OAAO,KALtC,MAAM,+DAA+D,SAAS,+BAA+B,GAEtG,aAAa,aAAa,OAAO;AAAA,EAI5C;AAEA,WAAS,QAAS,WAAW,QAAQ;AACnC,UAAM,MAAM,OAAO,KAAK,OAAO,MAAM,GAAG,GAAG,KAAK;AAChD,QAAI,aAAa,OAAO,KAAK,WAAW,QAAQ;AAEhD,UAAM,QAAQ,WAAW,SAAS,GAAG,EAAE,GACjC,UAAU,WAAW,SAAS,GAAG;AACvC,iBAAa,WAAW,SAAS,IAAI,GAAG;AAExC,QAAI;AACF,YAAM,SAAS,OAAO,iBAAiB,eAAe,KAAK,KAAK;AAChE,oBAAO,WAAW,OAAO,GAClB,GAAG,OAAO,OAAO,UAAU,CAAC,GAAG,OAAO,OAAO;AAAA,IACrD,SAAQ,OAAO;AACd,YAAM,UAAU,iBAAiB,YAC3B,mBAAmB,MAAM,YAAY,sBACrC,mBAAmB,MAAM,YAAY;AAE3C,UAAI,WAAW,kBAAkB;AAC/B,cAAM,MAAM,IAAI,MAAM,6DAA6D;AACnF,kBAAI,OAAO,sBACL;AAAA,MACP,WAAU,kBAAkB;AAC3B,cAAM,MAAM,IAAI,MAAM,iDAAiD;AACvE,kBAAI,OAAO,qBACL;AAAA,MACZ;AACM,cAAM;AAAA,IAEZ;AAAA,EACA;AAGA,WAAS,SAAU,YAAY,QAAQ,UAAU,CAAA,GAAI;AACnD,UAAMA,SAAQ,GAAQ,WAAW,QAAQ,QACnC,WAAW,GAAQ,WAAW,QAAQ;AAE5C,QAAI,OAAO,UAAW,UAAU;AAC9B,YAAM,MAAM,IAAI,MAAM,gFAAgF;AACtG,gBAAI,OAAO,mBACL;AAAA,IACV;AAGE,eAAW,OAAO,OAAO,KAAK,MAAM;AAClC,MAAI,OAAO,UAAU,eAAe,KAAK,YAAY,GAAG,KAClD,aAAa,OACf,WAAW,GAAG,IAAI,OAAO,GAAG,IAG1BA,UAEA,OADE,aAAa,KACR,IAAI,GAAG,6CAEP,IAAI,GAAG,8CAF0C,KAM5D,WAAW,GAAG,IAAI,OAAO,GAAG;AAAA,EAGlC;AAEA,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAE2BC,gBAAA,QAAA,eAAG,aAAa,cAChBA,OAAA,QAAA,eAAG,aAAa,cACjBA,OAAA,QAAA,cAAG,aAAa,aACrBA,OAAA,QAAA,SAAG,aAAa,QACfA,OAAA,QAAA,UAAG,aAAa,SAClBA,OAAA,QAAA,QAAG,aAAa,OACbA,OAAA,QAAA,WAAG,aAAa,UAEvCA,OAAA,UAAiB;;;;;;AC/XjB,WAAS,aAAc,UAAU,aAAa,QAAQ;AACpD,UAAM,UAAU,SAAS,MAAM,+BAA+B,KAAK,CAAA;AAEnE,WAAO,QAAQ,OAAO,SAAU,QAAQ,OAAO,OAAO;AACpD,YAAM,QAAQ,mCAAmC,KAAK,KAAK;AAC3D,UAAI,CAAC,SAAS,MAAM,WAAW;AAC7B,eAAO;AAGT,YAAM,SAAS,MAAM,CAAC;AAEtB,UAAI,OAAO;AAEX,UAAI,WAAW;AACb,sBAAc,MAAM,CAAC,GACrB,QAAQ,YAAY,QAAQ,OAAO,GAAG;AAAA,WACjC;AACL,cAAM,WAAW,MAAM,CAAC,EAAE,MAAM,IAAI,GAC9B,MAAM,SAAS,CAAC;AAQtB,YAPA,cAAc,MAAM,CAAC,EAAE,UAAU,OAAO,MAAM,GAE9C,QAAQ,OAAO,UAAU,eAAe,KAAK,aAAa,GAAG,IACzD,YAAY,GAAG,IACd,OAAO,OAAO,GAAG,KAAK,SAAS,CAAC,KAAK,IAGtC,SAAS,SAAS,KAAK,OAAO;AAChC,gBAAM,gBAAgB,QAAQ,QAAQ,CAAC;AACvC,kBAAQ,QAAQ,CAAC,IAAI,IAErB,SAAS,OAAO,QAAQ,eAAe,EAAE;AAAA,QACjD;AAEM,gBAAQ,aAAa,OAAO,aAAa,MAAM;AAAA,MACrD;AAEI,aAAO,OAAO,QAAQ,aAAa,KAAK;AAAA,IAC5C,GAAK,QAAQ;AAAA,EACb;AAEA,WAAS,OAAQ,QAAQ;AAEvB,UAAM,cAAc,OAAO,mBAAmB,CAAA,IAAK,QAAQ;AAE3D,eAAW,aAAa,OAAO,QAAQ;AACrC,YAAM,QAAQ,OAAO,UAAU,eAAe,KAAK,aAAa,SAAS,IAAI,YAAY,SAAS,IAAI,OAAO,OAAO,SAAS;AAE7H,aAAO,OAAO,SAAS,IAAI,aAAa,OAAO,aAAa,MAAM;AAAA,IACtE;AAEE,eAAW,cAAc,OAAO;AAC9B,kBAAY,UAAU,IAAI,OAAO,OAAO,UAAU;AAGpD,WAAO;AAAA,EACT;AAEA,cAAA,SAAwB;;;AC1CjB,SAAS,QACd,MACA,QACA,WAAqB,CAAC,OAAO,GACL;AACxB,MAAI,SAAS;AACX,UAAM,IAAI;AAAA,MACR;AAAA,IAEF;AAGI,QAAA,MAA8B,CAAC,GAC/B,WAAW;AAAA;AAAA,IACK;AAAA;AAAA,IACF;AAAA;AAAA,IACD,QAAQ,IAAI;AAAA;AAAA,IACN,QAAQ,IAAI;AAAA,EAAA,GAG/B,SAAS,OAAO;AAAA,IACpB,SAAS,QAAQ,CAAC,SAAS;AACzB,YAAM,UAAU,WAAW,QAAQ,CAAC,IAAI,GAAG;AAAA,QACzC,SAAS;AAAA,MAAA,CACV;AACI,aAAA,UACE,OAAO,QAAQC,cAAM,MAAAR,YAAA,QAAG,aAAa,OAAO,CAAC,CAAC,IADhC,CAAC;AAAA,IAEvB,CAAA;AAAA,EACH;AAGI,SAAO,YAAY,QAAQ,IAAI,uBAAuB,WACxD,QAAQ,IAAI,qBAAqB,OAAO,WAGtC,OAAO,WAAW,QAAQ,IAAI,YAAY,WAC5C,QAAQ,IAAI,UAAU,OAAO,UAE3B,OAAO,gBAAgB,QAAQ,IAAI,iBAAiB,WACtD,QAAQ,IAAI,eAAe,OAAO;AAGhC,MAAA;AAEKS,gBAAA,OAAA,EAAC,QAAO;AAAA,WACR,GAAG;AAGN,UAAA,EAAE,QAAQ,SAAS,OAAO,IACtB,IAAI,MAAM,wEAAwE,IAEpF;AAAA,EAAA;AAIR,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM;AAC1C,aAAS,KAAK,CAAC,WAAW,IAAI,WAAW,MAAM,CAAC,MAClD,IAAI,GAAG,IAAI;AAMf,aAAW,OAAO,QAAQ;AACpB,aAAS,KAAK,CAAC,WAAW,IAAI,WAAW,MAAM,CAAC,MAClD,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG;AAIvB,SAAA;AACT;AAEA,SAAS,WACP,KACA,SACA,SAGoB;AACpB,aAAW,UAAU,SAAS;AAC5B,UAAM,WAAWV,cAAA,QAAK,KAAK,KAAK,MAAM;AAElC,QAAAC,YAAA,QAAG,WAAW,QAAQ,KAAKA,oBAAG,SAAS,QAAQ,EAAE,OAAO;AACnD,aAAA;AAAA,EAAA;AAGL,QAAA,YAAYD,cAAAA,QAAK,QAAQ,GAAG;AAC9B,MAAA,cAAc,QAAQ,CAAC,SAAS,WAAW,UAAU,WAAW,SAAS,OAAO;AAC3E,WAAA,WAAW,WAAW,SAAS,OAAO;AAIjD;;;;;;;;", "x_google_ignoreList": [2, 3]}