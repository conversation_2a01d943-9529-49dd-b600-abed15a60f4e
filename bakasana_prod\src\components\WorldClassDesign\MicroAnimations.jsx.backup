'use client';

import React, { useEffect, useRef, useState } from 'react';

/**
 * ✨ MICRO ANIMATIONS - TOP 1% DESIGN FEATURE
 * Subtelne animacje na Apple/Linear.app level
 * Staggered reveals, magnetic effects, breathing animations
 */

// Breathing animation component
export const BreathingAnimation = ({ children, intensity = 0.02, duration = 4000, className = '' }) => {
  return (
    <div
      className={`breathing-animation ${className}`}
      style={{
        animation: `breathing ${duration}ms ease-in-out infinite`,
        '--intensity': intensity,
      }}
    >
      {children}
      <style jsx>{`
        @keyframes breathing {
          0%, 100% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(calc(1 + var(--intensity)));
            opacity: 0.95;
          }
        }
        
        @media (prefers-reduced-motion: reduce) {
          .breathing-animation {
            animation: none !important;
          }
        }
      `}</style>
    </div>
  );
};

// Magnetic hover effect
export const MagneticHover = ({ children, strength = 0.2, className = '' }) => {
  const elementRef = useRef(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleMouseMove = (e) => {
      const rect = element.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;
      
      const distance = Math.sqrt(x * x + y * y);
      const maxDistance = Math.max(rect.width, rect.height) / 2;
      
      if (distance < maxDistance * 1.5) {
        const translateX = x * strength;
        const translateY = y * strength;
        element.style.transform = `translate(${translateX}px, ${translateY}px)`;
      }
    };

    const handleMouseLeave = () => {
      element.style.transform = 'translate(0px, 0px)';
    };

    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [strength]);

  return (
    <div
      ref={elementRef}
      className={`magnetic-hover ${className}`}
      style={{
        transition: 'transform 0.2s cubic-bezier(0.22, 1, 0.36, 1)',
        willChange: 'transform',
      }}
    >
      {children}
    </div>
  );
};

// Parallax scroll effect
export const ParallaxScroll = ({ children, speed = 0.5, className = '' }) => {
  const elementRef = useRef(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const rect = element.getBoundingClientRect();
      const elementTop = rect.top + scrolled;
      const elementHeight = rect.height;
      const windowHeight = window.innerHeight;
      
      // Only apply parallax when element is in viewport
      if (scrolled + windowHeight > elementTop && scrolled < elementTop + elementHeight) {
        const yPos = -(scrolled - elementTop) * speed;
        element.style.transform = `translateY(${yPos}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial call

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [speed]);

  return (
    <div
      ref={elementRef}
      className={`parallax-scroll ${className}`}
      style={{
        willChange: 'transform',
      }}
    >
      {children}
    </div>
  );
};

// Smooth reveal on scroll
export const SmoothReveal = ({ 
  children, 
  delay = 0, 
  duration = 800, 
  offset = 50, 
  className = '',
  triggerOnce = true 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && (!triggerOnce || !hasTriggered)) {
          setTimeout(() => {
            setIsVisible(true);
            setHasTriggered(true);
          }, delay);
        } else if (!triggerOnce && !entry.isIntersecting) {
          setIsVisible(false);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [delay, triggerOnce, hasTriggered]);

  return (
    <div
      ref={elementRef}
      className={`smooth-reveal ${className}`}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateY(0)' : `translateY(${offset}px)`,
        transition: `opacity ${duration}ms cubic-bezier(0.22, 1, 0.36, 1), transform ${duration}ms cubic-bezier(0.22, 1, 0.36, 1)`,
        willChange: 'transform, opacity',
      }}
    >
      {children}
    </div>
  );
};

// Floating animation
export const FloatingAnimation = ({ 
  children, 
  amplitude = 10, 
  duration = 3000, 
  className = '',
  direction = 'vertical' 
}) => {
  return (
    <div
      className={`floating-animation ${className}`}
      style={{
        animation: `floating-${direction} ${duration}ms ease-in-out infinite`,
        '--amplitude': `${amplitude}px`,
      }}
    >
      {children}
      <style jsx>{`
        @keyframes floating-vertical {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(calc(-1 * var(--amplitude))); }
        }
        
        @keyframes floating-horizontal {
          0%, 100% { transform: translateX(0); }
          50% { transform: translateX(calc(-1 * var(--amplitude))); }
        }
        
        @media (prefers-reduced-motion: reduce) {
          .floating-animation {
            animation: none !important;
          }
        }
      `}</style>
    </div>
  );
};

// Pulse animation
export const PulseAnimation = ({ 
  children, 
  scale = 1.05, 
  duration = 2000, 
  className = '' 
}) => {
  return (
    <div
      className={`pulse-animation ${className}`}
      style={{
        animation: `pulse ${duration}ms ease-in-out infinite`,
        '--scale': scale,
      }}
    >
      {children}
      <style jsx>{`
        @keyframes pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(var(--scale)); opacity: 0.8; }
        }
        
        @media (prefers-reduced-motion: reduce) {
          .pulse-animation {
            animation: none !important;
          }
        }
      `}</style>
    </div>
  );
};

// Shimmer loading effect
export const ShimmerLoading = ({ 
  className = '',
  width = '100%',
  height = '20px',
  borderRadius = '4px' 
}) => {
  return (
    <div
      className={`shimmer-loading ${className}`}
      style={{
        width,
        height,
        borderRadius,
        background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
        backgroundSize: '200% 100%',
        animation: 'shimmer 1.5s infinite',
      }}
    >
      <style jsx>{`
        @keyframes shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }
        
        @media (prefers-reduced-motion: reduce) {
          .shimmer-loading {
            animation: none !important;
            background: #f0f0f0 !important;
          }
        }
      `}</style>
    </div>
  );
};

// Ripple effect
export const RippleEffect = ({ children, className = '' }) => {
  const [ripples, setRipples] = useState([]);
  const elementRef = useRef(null);

  const createRipple = (e) => {
    const element = elementRef.current;
    if (!element) return;

    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    const newRipple = {
      x,
      y,
      size,
      id: Date.now(),
    };

    setRipples(prev => [...prev, newRipple]);

    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, 600);
  };

  return (
    <div
      ref={elementRef}
      className={`ripple-container ${className}`}
      style={{
        position: 'relative',
        overflow: 'hidden',
        cursor: 'pointer',
      }}
      onClick={createRipple}
    >
      {children}
      {ripples.map(ripple => (
        <div
          key={ripple.id}
          className="ripple"
          style={{
            position: 'absolute',
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
            ,
            background: 'rgba(124, 152, 133, 0.3)',
            animation: 'ripple 0.6s ease-out',
            pointerEvents: 'none',
          }}
        />
      ))}
      <style jsx>{`
        @keyframes ripple {
          0% {
            transform: scale(0);
            opacity: 0.8;
          }
          100% {
            transform: scale(1);
            opacity: 0;
          }
        }
        
        @media (prefers-reduced-motion: reduce) {
          .ripple {
            animation: none !important;
            opacity: 0 !important;
          }
        }
      `}</style>
    </div>
  );
};

// Main MicroAnimations component
const MicroAnimations = {
  BreathingAnimation,
  MagneticHover,
  ParallaxScroll,
  SmoothReveal,
  FloatingAnimation,
  PulseAnimation,
  ShimmerLoading,
  RippleEffect,
};

export default MicroAnimations;