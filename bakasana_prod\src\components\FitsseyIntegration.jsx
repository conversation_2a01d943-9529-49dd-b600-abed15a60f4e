
'use client';
import React, { useState, useEffect } from 'react';

import { CardTitle, BodyText  } from '@/components/ui/UnifiedTypography';
import { Icon  } from '@/components/ui/IconSystem';
import UnifiedButton, { CTAButton, SecondaryButton } from '@/components/ui/UnifiedButton';

const FITSSEY_URL = 'https://app.fitssey.com/Flywithbakasana/frontoffice';





export default function FitsseyIntegration({
  buttonText = "Zapisz się przez Fitssey",
  variant = "primary",
  size = "md",
  className = "",
  showInfo = false,
  trackingEvent = "fitssey_redirect"
}) {
  const [isLoading, setIsLoading] = useState(false);

  const handleFitsseyRedirect = async () => {
    setIsLoading(true);
    
    try {
      // Track the click event for analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', trackingEvent, {
          event_category: 'engagement',
          event_label: 'fitssey_booking_system',
          value: 1
        });
      }

      // Add a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Open Fitssey in new tab
      window.open(FITSSEY_URL, '_blank', 'noopener,noreferrer');
      
    } catch (error) {
      console.error('Error redirecting to Fitssey:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const ButtonComponent = variant === "primary" ? CTAButton : SecondaryButton;

  return (
    <div className={`fitssey-integration ${className}`}>
      <ButtonComponent
        onClick={handleFitsseyRedirect}
        disabled={isLoading}
        className={`
          ${size === 'sm' ? 'px-4 py-2 text-sm' : 
            size === 'lg' ? 'px-8 py-4 text-lg' : 
            'px-6 py-3'}
          ${isLoading ? 'opacity-75 cursor-wait' : ''}
        `}
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
            Przekierowywanie...
          </>
        ) : (
          <>
            {buttonText}
            <Icon name="external-link" size="sm" className="ml-2" />
          </>
        )}
      </ButtonComponent>

      {showInfo && (
        <div className="mt-4 p-4 bg-enterprise-brown/5 rounded-lg">
          <div className="flex items-start gap-3">
            <Icon name="info" size="md" className="text-enterprise-brown mt-1 flex-shrink-0" />
            <div>
              <CardTitle className="text-sm mb-2">
                System zapisów Fitssey
              </CardTitle>
              <BodyText className="text-xs text-charcoal-light">
                Używamy profesjonalnego systemu Fitssey do zarządzania zapisami i płatnościami. 
                Po kliknięciu zostaniesz przekierowany na bezpieczną stronę zapisów.
              </BodyText>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Komponent dla floating button
export function FitsseyFloatingButton({
  position = "bottom-right",
  message = "Zapisz się na zajęcia",
  className = ""
}) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6'
  };

  if (!isVisible) return null;

  return (
    <div className={`
      fixed ${positionClasses[position]} z-50 
      transform transition-all duration-500 ease-out
      ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}
      ${className}
    `}>
      <div className="group">
        {/* Tooltip */}
        <div className="
          absolute bottom-full right-0 mb-2 px-3 py-2 
          bg-charcoal text-white text-sm rounded-lg
          opacity-0 group-hover:opacity-100 transition-opacity duration-300
          whitespace-nowrap pointer-events-none
        ">
          {message}
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-charcoal" />
        </div>

        {/* Button */}
        <FitsseyIntegration
          buttonText=""
          variant="primary"
          size="lg"
          trackingEvent="fitssey_floating_button"
          className="rounded-full w-16 h-16 flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow duration-300"
        />
      </div>
    </div>
  );
}

// Komponent dla inline widget
export function FitsseyWidget({
  title = "Zapisy na zajęcia",
  description = "Kliknij poniżej, aby przejść do systemu zapisów",
  className = ""
}) {
  return (
    <div className={`fitssey-widget bg-white rounded-lg shadow-subtle p-6 ${className}`}>
      <div className="text-center">
        <CardTitle className="mb-2 text-enterprise-brown">
          {title}
        </CardTitle>
        <BodyText className="text-charcoal-light mb-4">
          {description}
        </BodyText>
        <FitsseyIntegration
          buttonText="Otwórz system zapisów"
          variant="primary"
          size="lg"
          showInfo={true}
          trackingEvent="fitssey_widget"
        />
      </div>
    </div>
  );
}

// Hook dla śledzenia interakcji z Fitssey
export function useFitsseyTracking() {
  const trackFitsseyInteraction = (action, label = '') => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'fitssey_interaction', {
        event_category: 'booking_system',
        event_label: label || action,
        custom_parameter_1: action
      });
    }
  };

  return { trackFitsseyInteraction };
}

// Export default już istnieje wyżej
