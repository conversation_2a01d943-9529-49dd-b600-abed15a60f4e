{"name": "@sanity/descriptors", "version": "1.1.1", "description": "", "keywords": [], "repository": {"url": "https://github.com/sanity-io/descriptors"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"source": "./src/index.ts", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "pkg build --strict --clean --check", "format": "prettier --write --cache --ignore-unknown .", "lint": "eslint . --ext .cjs,.js,.ts,.tsx", "prepublishOnly": "pkg build --strict --clean --check", "test": "vitest", "ts:check": "tsc --noEmit"}, "lint-staged": {"*": ["prettier --write --cache --ignore-unknown"]}, "browserslist": "extends @sanity/browserslist-config", "prettier": "@sanity/prettier-config", "dependencies": {"sha256-uint8array": "^0.10.7"}, "devDependencies": {"@sanity/browserslist-config": "^1.0.5", "@sanity/pkg-utils": "^6.13.4", "@sanity/prettier-config": "^1.0.3", "@sanity/semantic-release-preset": "^5.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-simple-import-sort": "^12.1.1", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "prettier-plugin-packagejson": "^2.5.12", "semantic-release": "^24.2.5", "typescript": "^5.8.3", "vitest": "^3.1.3"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977", "engines": {"node": ">=18.0.0"}, "publishConfig": {"access": "public"}}