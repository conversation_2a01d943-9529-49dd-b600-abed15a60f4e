#!/usr/bin/env node

/**
 * Structured Data Validator for Schema.org compliance
 * Validates JSON-LD structured data in Next.js project
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class StructuredDataValidator {
  constructor() {
    this.schemas = [];
    this.errors = [];
    this.warnings = [];
    this.suggestions = [];
  }

  log(message, color = 'white') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  // Extract JSON-LD from JSX files
  extractStructuredData(content, fileName) {
    const jsonLdRegex = /"application\/ld\+json"[\s\S]*?dangerouslySetInnerHTML[\s\S]*?__html:\s*JSON\.stringify\(([\s\S]*?)\)/g;
    const matches = [...content.matchAll(jsonLdRegex)];
    
    matches.forEach((match, index) => {
      try {
        // Extract the object being stringified
        const objectStr = match[1];
        // This is a simplified extraction - in real scenarios you'd need a proper parser
        this.log(`  Found structured data block ${index + 1} in ${fileName}`, 'cyan');
        this.analyzeStructuredDataBlock(objectStr, fileName, index + 1);
      } catch (error) {
        this.errors.push(`${fileName}: Error parsing structured data block ${index + 1}: ${error.message}`);
      }
    });

    // Also check for direct schema objects
    const schemaRegex = /"@context":\s*"https:\/\/schema\.org"/g;
    const schemaMatches = [...content.matchAll(schemaRegex)];
    
    if (schemaMatches.length > 0) {
      this.log(`  Found ${schemaMatches.length} schema.org references in ${fileName}`, 'green');
    }
  }

  analyzeStructuredDataBlock(objectStr, fileName, blockIndex) {
    // Check for required schema.org properties
    if (!objectStr.includes('"@context"')) {
      this.errors.push(`${fileName} block ${blockIndex}: Missing @context property`);
    }

    if (!objectStr.includes('"@type"')) {
      this.errors.push(`${fileName} block ${blockIndex}: Missing @type property`);
    }

    // Check for common schema types and their required properties
    this.validateSchemaType(objectStr, fileName, blockIndex);
  }

  validateSchemaType(objectStr, fileName, blockIndex) {
    // Organization schema validation
    if (objectStr.includes('"@type": "Organization"')) {
      this.validateOrganization(objectStr, fileName, blockIndex);
    }

    // LocalBusiness schema validation
    if (objectStr.includes('"@type": "LocalBusiness"') || objectStr.includes('"@type": "YogaStudio"')) {
      this.validateLocalBusiness(objectStr, fileName, blockIndex);
    }

    // Person schema validation
    if (objectStr.includes('"@type": "Person"')) {
      this.validatePerson(objectStr, fileName, blockIndex);
    }

    // Service schema validation
    if (objectStr.includes('"@type": "Service"')) {
      this.validateService(objectStr, fileName, blockIndex);
    }

    // Event schema validation
    if (objectStr.includes('"@type": "Event"')) {
      this.validateEvent(objectStr, fileName, blockIndex);
    }

    // Product schema validation
    if (objectStr.includes('"@type": "Product"')) {
      this.validateProduct(objectStr, fileName, blockIndex);
    }

    // TravelAgency schema validation
    if (objectStr.includes('"@type": "TravelAgency"')) {
      this.validateTravelAgency(objectStr, fileName, blockIndex);
    }

    // WebSite schema validation
    if (objectStr.includes('"@type": "WebSite"')) {
      this.validateWebSite(objectStr, fileName, blockIndex);
    }
  }

  validateOrganization(objectStr, fileName, blockIndex) {
    const required = ['name', 'url'];
    const recommended = ['logo', 'description', 'contactPoint', 'address', 'sameAs'];
    
    this.checkRequiredProperties(objectStr, required, fileName, blockIndex, 'Organization');
    this.checkRecommendedProperties(objectStr, recommended, fileName, blockIndex, 'Organization');
  }

  validateLocalBusiness(objectStr, fileName, blockIndex) {
    const required = ['name', 'address'];
    const recommended = ['telephone', 'openingHours', 'priceRange', 'image', 'description'];
    
    this.checkRequiredProperties(objectStr, required, fileName, blockIndex, 'LocalBusiness');
    this.checkRecommendedProperties(objectStr, recommended, fileName, blockIndex, 'LocalBusiness');
  }

  validatePerson(objectStr, fileName, blockIndex) {
    const required = ['name'];
    const recommended = ['jobTitle', 'description', 'image', 'url', 'sameAs'];
    
    this.checkRequiredProperties(objectStr, required, fileName, blockIndex, 'Person');
    this.checkRecommendedProperties(objectStr, recommended, fileName, blockIndex, 'Person');
  }

  validateService(objectStr, fileName, blockIndex) {
    const required = ['name', 'provider'];
    const recommended = ['description', 'areaServed', 'serviceType', 'offers'];
    
    this.checkRequiredProperties(objectStr, required, fileName, blockIndex, 'Service');
    this.checkRecommendedProperties(objectStr, recommended, fileName, blockIndex, 'Service');
  }

  validateEvent(objectStr, fileName, blockIndex) {
    const required = ['name', 'startDate', 'location'];
    const recommended = ['description', 'endDate', 'organizer', 'offers', 'image'];
    
    this.checkRequiredProperties(objectStr, required, fileName, blockIndex, 'Event');
    this.checkRecommendedProperties(objectStr, recommended, fileName, blockIndex, 'Event');
  }

  validateProduct(objectStr, fileName, blockIndex) {
    const required = ['name'];
    const recommended = ['description', 'image', 'offers', 'brand', 'aggregateRating'];
    
    this.checkRequiredProperties(objectStr, required, fileName, blockIndex, 'Product');
    this.checkRecommendedProperties(objectStr, recommended, fileName, blockIndex, 'Product');
  }

  validateTravelAgency(objectStr, fileName, blockIndex) {
    const required = ['name'];
    const recommended = ['description', 'url', 'address', 'telephone', 'offers'];
    
    this.checkRequiredProperties(objectStr, required, fileName, blockIndex, 'TravelAgency');
    this.checkRecommendedProperties(objectStr, recommended, fileName, blockIndex, 'TravelAgency');
  }

  validateWebSite(objectStr, fileName, blockIndex) {
    const required = ['name', 'url'];
    const recommended = ['description', 'publisher', 'potentialAction'];
    
    this.checkRequiredProperties(objectStr, required, fileName, blockIndex, 'WebSite');
    this.checkRecommendedProperties(objectStr, recommended, fileName, blockIndex, 'WebSite');
  }

  checkRequiredProperties(objectStr, properties, fileName, blockIndex, schemaType) {
    properties.forEach(prop => {
      if (!objectStr.includes(`"${prop}"`)) {
        this.errors.push(`${fileName} block ${blockIndex} (${schemaType}): Missing required property "${prop}"`);
      }
    });
  }

  checkRecommendedProperties(objectStr, properties, fileName, blockIndex, schemaType) {
    properties.forEach(prop => {
      if (!objectStr.includes(`"${prop}"`)) {
        this.suggestions.push(`${fileName} block ${blockIndex} (${schemaType}): Consider adding recommended property "${prop}"`);
      }
    });
  }

  // Analyze all files in the project
  analyzeProject() {
    this.log('🔍 Starting Structured Data validation...', 'blue');
    
    const srcDir = path.join(process.cwd(), 'src');
    
    if (!fs.existsSync(srcDir)) {
      this.errors.push('src directory not found');
      return;
    }

    // Find all JSX files
    const findJSXFiles = (dir) => {
      const files = [];
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.includes('node_modules')) {
          files.push(...findJSXFiles(fullPath));
        } else if (item.endsWith('.jsx') || item.endsWith('.js')) {
          files.push(fullPath);
        }
      }
      
      return files;
    };

    const jsxFiles = findJSXFiles(srcDir);
    
    this.log(`Found ${jsxFiles.length} JSX/JS files to analyze`, 'green');
    
    // Analyze each file
    jsxFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const fileName = path.basename(file);
      
      if (content.includes('application/ld+json') || content.includes('@type')) {
        this.log(`\n📄 Analyzing structured data in: ${fileName}`, 'cyan');
        this.extractStructuredData(content, fileName);
      }
    });

    // Generate report
    this.generateReport();
  }

  generateReport() {
    this.log('\n📊 STRUCTURED DATA VALIDATION REPORT', 'magenta');
    this.log('='.repeat(50), 'magenta');

    // Errors
    if (this.errors.length > 0) {
      this.log(`\n❌ ERRORS (${this.errors.length}):`, 'red');
      this.errors.forEach(error => this.log(`  • ${error}`, 'red'));
    }

    // Warnings
    if (this.warnings.length > 0) {
      this.log(`\n⚠️  WARNINGS (${this.warnings.length}):`, 'yellow');
      this.warnings.forEach(warning => this.log(`  • ${warning}`, 'yellow'));
    }

    // Suggestions
    if (this.suggestions.length > 0) {
      this.log(`\n💡 SUGGESTIONS (${this.suggestions.length}):`, 'cyan');
      this.suggestions.forEach(suggestion => this.log(`  • ${suggestion}`, 'cyan'));
    }

    // Summary
    this.log('\n📈 SUMMARY:', 'blue');
    this.log(`  Errors: ${this.errors.length}`, this.errors.length > 0 ? 'red' : 'green');
    this.log(`  Warnings: ${this.warnings.length}`, this.warnings.length > 0 ? 'yellow' : 'green');
    this.log(`  Suggestions: ${this.suggestions.length}`, 'cyan');

    // Recommendations
    this.log('\n🎯 RECOMMENDATIONS:', 'blue');
    this.log('  • Use Google\'s Rich Results Test: https://search.google.com/test/rich-results', 'cyan');
    this.log('  • Validate with Schema.org validator: https://validator.schema.org/', 'cyan');
    this.log('  • Test structured data in Google Search Console', 'cyan');

    // Save report
    this.saveReport();
  }

  saveReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        errors: this.errors.length,
        warnings: this.warnings.length,
        suggestions: this.suggestions.length
      },
      details: {
        errors: this.errors,
        warnings: this.warnings,
        suggestions: this.suggestions
      }
    };

    const reportPath = path.join(process.cwd(), 'structured-data-validation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log(`\n💾 Report saved to: ${reportPath}`, 'green');
  }
}

// Run the validation
if (require.main === module) {
  const validator = new StructuredDataValidator();
  validator.analyzeProject();
}

module.exports = StructuredDataValidator;
