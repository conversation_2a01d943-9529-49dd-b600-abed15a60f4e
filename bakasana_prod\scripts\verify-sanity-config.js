#!/usr/bin/env node

/**
 * 🔍 SANITY CONFIGURATION VERIFIER
 * Weryfikuje konfigurację Sanity i sprawdza połączenie
 */

const { createClient } = require('@sanity/client');
const dotenv = require('dotenv');
const path = require('path');

// Załaduj zmienne środowiskowe
dotenv.config({ path: path.join(__dirname, '../.env.local') });

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function verifySanityConfig() {
  log('\n🔍 WERYFIKACJA KONFIGURACJI SANITY CMS\n', 'bold');

  // 1. Sprawdź zmienne środowiskowe
  log('📋 1. Sprawdzanie zmiennych środowiskowych...', 'cyan');
  
  const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID;
  const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'production';
  const apiToken = process.env.SANITY_API_TOKEN;

  if (!projectId) {
    log('❌ NEXT_PUBLIC_SANITY_PROJECT_ID nie jest ustawione!', 'red');
    log('📌 Ustaw: NEXT_PUBLIC_SANITY_PROJECT_ID=twoje-id-projektu', 'yellow');
    return false;
  } else {
    log(`✅ Project ID: ${projectId}`, 'green');
  }

  if (!dataset) {
    log('❌ Dataset nie jest ustawiony!', 'red');
    return false;
  } else {
    log(`✅ Dataset: ${dataset}`, 'green');
  }

  if (!apiToken) {
    log('⚠️  SANITY_API_TOKEN nie jest ustawiony (tylko odczyt)', 'yellow');
  } else {
    log('✅ API Token jest ustawiony', 'green');
  }

  // 2. Utwórz klienta i sprawdź połączenie
  log('\n🔌 2. Testowanie połączenia z Sanity...', 'cyan');

  const client = createClient({
    projectId,
    dataset,
    apiVersion: '2024-12-28',
    useCdn: false, // Wyłącz CDN dla testów
    token: apiToken,
    timeout: 10000
  });

  try {
    // Test podstawowego połączenia
    const result = await client.fetch('*[_type == "sanity.imageAsset"] | order(_updatedAt desc) [0...1]');
    log('✅ Połączenie z Sanity działa poprawnie!', 'green');
    
    // 3. Sprawdź dostępne typy dokumentów
    log('\n📄 3. Sprawdzanie schematów...', 'cyan');
    
    const schemas = await client.fetch('array::unique(*[]._type)');
    if (schemas && schemas.length > 0) {
      log(`✅ Znalezione schematy: ${schemas.join(', ')}`, 'green');
      
      // Sprawdź kluczowe schematy
      const requiredSchemas = ['retreat', 'testimonial', 'faq', 'blogPost'];
      const missingSchemas = requiredSchemas.filter(schema => !schemas.includes(schema));
      
      if (missingSchemas.length > 0) {
        log(`⚠️  Brakujące schematy: ${missingSchemas.join(', ')}`, 'yellow');
      } else {
        log('✅ Wszystkie wymagane schematy są dostępne', 'green');
      }
    } else {
      log('⚠️  Brak dokumentów w bazie danych', 'yellow');
    }

    // 4. Test zapytań
    log('\n🔍 4. Testowanie zapytań...', 'cyan');
    
    const retreatCount = await client.fetch('count(*[_type == "retreat"])');
    log(`📊 Retreaty: ${retreatCount}`, retreatCount > 0 ? 'green' : 'yellow');
    
    const testimonialCount = await client.fetch('count(*[_type == "testimonial"])');
    log(`📊 Opinie: ${testimonialCount}`, testimonialCount > 0 ? 'green' : 'yellow');
    
    const faqCount = await client.fetch('count(*[_type == "faq"])');
    log(`📊 FAQ: ${faqCount}`, faqCount > 0 ? 'green' : 'yellow');

    // 5. Sprawdź uprawnienia
    if (apiToken) {
      log('\n🔐 5. Sprawdzanie uprawnień API...', 'cyan');
      try {
        await client.fetch('*[_type == "sanity.imageAsset"] | order(_updatedAt desc) [0...1]');
        log('✅ Token ma uprawnienia odczytu', 'green');
      } catch (error) {
        if (error.statusCode === 401) {
          log('❌ Token nie ma uprawnień - sprawdź konfigurację w Sanity', 'red');
        } else {
          log(`⚠️  Problem z tokenem: ${error.message}`, 'yellow');
        }
      }
    }

    log('\n🎉 WERYFIKACJA ZAKOŃCZONA POMYŚLNIE!', 'bold');
    log('✅ Sanity CMS jest poprawnie skonfigurowane', 'green');
    
    return true;

  } catch (error) {
    log('\n❌ BŁĄD POŁĄCZENIA Z SANITY!', 'red');
    
    if (error.statusCode === 401) {
      log('🔐 Problem z autoryzacją:', 'red');
      log('   - Sprawdź SANITY_API_TOKEN', 'yellow');
      log('   - Upewnij się, że token ma odpowiednie uprawnienia', 'yellow');
    } else if (error.statusCode === 404) {
      log('🔍 Projekt nie znaleziony:', 'red');
      log('   - Sprawdź NEXT_PUBLIC_SANITY_PROJECT_ID', 'yellow');
      log('   - Upewnij się, że projekt istnieje w Sanity', 'yellow');
    } else if (error.code === 'ENOTFOUND') {
      log('🌐 Problem z połączeniem internetowym', 'red');
    } else {
      log(`🐛 Nieoczekiwany błąd: ${error.message}`, 'red');
    }

    log('\n📋 LISTA KONTROLNA:', 'blue');
    log('1. Sprawdź .env.local', 'blue');
    log('2. Przejdź do sanity.io/manage', 'blue');
    log('3. Sprawdź ID projektu i dataset', 'blue');
    log('4. Wygeneruj nowy API token jeśli potrzeba', 'blue');
    
    return false;
  }
}

// Uruchom weryfikację
if (require.main === module) {
  verifySanityConfig()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Krytyczny błąd:', error);
      process.exit(1);
    });
}

module.exports = { verifySanityConfig };