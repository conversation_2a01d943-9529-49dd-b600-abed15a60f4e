{"version": 3, "file": "util.child-selection-point-to-block-offset.js", "sources": ["../../src/utils/util.block-offset-to-block-selection-point.ts", "../../src/utils/util.block-offset-to-selection-point.ts", "../../src/utils/util.block-offsets-to-selection.ts", "../../src/utils/util.child-selection-point-to-block-offset.ts"], "sourcesContent": ["import type {EditorContext} from '../editor/editor-snapshot'\nimport type {BlockOffset} from '../types/block-offset'\nimport type {EditorSelectionPoint} from '../types/editor'\n\n/**\n * @public\n */\nexport function blockOffsetToBlockSelectionPoint({\n  context,\n  blockOffset,\n}: {\n  context: Pick<EditorContext, 'value'>\n  blockOffset: BlockOffset\n}): EditorSelectionPoint | undefined {\n  let selectionPoint: EditorSelectionPoint | undefined\n\n  for (const block of context.value) {\n    if (block._key === blockOffset.path[0]._key) {\n      selectionPoint = {\n        path: [{_key: block._key}],\n        offset: blockOffset.offset,\n      }\n      break\n    }\n  }\n\n  return selectionPoint\n}\n", "import type {EditorContext} from '../editor/editor-snapshot'\nimport type {BlockOffset} from '../types/block-offset'\nimport type {EditorSelectionPoint} from '../types/editor'\nimport {blockOffsetToSpanSelectionPoint} from './util.block-offset'\nimport {blockOffsetToBlockSelectionPoint} from './util.block-offset-to-block-selection-point'\n\n/**\n * @public\n */\nexport function blockOffsetToSelectionPoint({\n  context,\n  blockOffset,\n  direction,\n}: {\n  context: Pick<EditorContext, 'schema' | 'value'>\n  blockOffset: BlockOffset\n  direction: 'forward' | 'backward'\n}): EditorSelectionPoint | undefined {\n  const spanSelectionPoint = blockOffsetToSpanSelectionPoint({\n    context,\n    blockOffset,\n    direction,\n  })\n\n  if (!spanSelectionPoint) {\n    return blockOffsetToBlockSelectionPoint({\n      context,\n      blockOffset,\n    })\n  }\n\n  return spanSelectionPoint\n}\n", "import type {EditorSelection} from '..'\nimport type {EditorContext} from '../editor/editor-snapshot'\nimport type {BlockOffset} from '../types/block-offset'\nimport {blockOffsetToSelectionPoint} from './util.block-offset-to-selection-point'\n\n/**\n * @public\n */\nexport function blockOffsetsToSelection({\n  context,\n  offsets,\n  backward,\n}: {\n  context: Pick<EditorContext, 'schema' | 'value'>\n  offsets: {anchor: BlockOffset; focus: BlockOffset}\n  backward?: boolean\n}): EditorSelection {\n  const anchor = blockOffsetToSelectionPoint({\n    context,\n    blockOffset: offsets.anchor,\n    direction: backward ? 'backward' : 'forward',\n  })\n  const focus = blockOffsetToSelectionPoint({\n    context,\n    blockOffset: offsets.focus,\n    direction: backward ? 'forward' : 'backward',\n  })\n\n  if (!anchor || !focus) {\n    return null\n  }\n\n  return {\n    anchor,\n    focus,\n    backward,\n  }\n}\n", "import type {EditorContext} from '../editor/editor-snapshot'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {\n  getBlockKeyFromSelectionPoint,\n  getChildKeyFromSelectionPoint,\n} from '../selection/selection-point'\nimport type {BlockOffset} from '../types/block-offset'\nimport type {EditorSelectionPoint} from '../types/editor'\n\n/**\n * @public\n */\nexport function childSelectionPointToBlockOffset({\n  context,\n  selectionPoint,\n}: {\n  context: Pick<EditorContext, 'schema' | 'value'>\n  selectionPoint: EditorSelectionPoint\n}): BlockOffset | undefined {\n  let offset = 0\n\n  const blockKey = getBlockKeyFromSelectionPoint(selectionPoint)\n  const childKey = getChildKeyFromSelectionPoint(selectionPoint)\n\n  if (!blockKey || !childKey) {\n    return undefined\n  }\n\n  for (const block of context.value) {\n    if (block._key !== blockKey) {\n      continue\n    }\n\n    if (!isTextBlock(context, block)) {\n      continue\n    }\n\n    for (const child of block.children) {\n      if (child._key === childKey) {\n        return {\n          path: [{_key: block._key}],\n          offset: offset + selectionPoint.offset,\n        }\n      }\n\n      if (isSpan(context, child)) {\n        offset += child.text.length\n      }\n    }\n  }\n}\n"], "names": ["blockOffsetToBlockSelectionPoint", "context", "blockOffset", "selectionPoint", "block", "value", "_key", "path", "offset", "blockOffsetToSelectionPoint", "direction", "blockOffsetToSpanSelectionPoint", "blockOffsetsToSelection", "offsets", "backward", "anchor", "focus", "childSelectionPointToBlockOffset", "blockKey", "getBlockKeyFromSelectionPoint", "<PERSON><PERSON><PERSON>", "getChildKeyFromSelectionPoint", "isTextBlock", "child", "children", "isSpan", "text", "length"], "mappings": ";AAOO,SAASA,iCAAiC;AAAA,EAC/CC;AAAAA,EACAC;AAIF,GAAqC;AACnC,MAAIC;AAEJ,aAAWC,SAASH,QAAQI;AAC1B,QAAID,MAAME,SAASJ,YAAYK,KAAK,CAAC,EAAED,MAAM;AAC3CH,uBAAiB;AAAA,QACfI,MAAM,CAAC;AAAA,UAACD,MAAMF,MAAME;AAAAA,QAAAA,CAAK;AAAA,QACzBE,QAAQN,YAAYM;AAAAA,MAAAA;AAEtB;AAAA,IACF;AAGF,SAAOL;AACT;AClBO,SAASM,4BAA4B;AAAA,EAC1CR;AAAAA,EACAC;AAAAA,EACAQ;AAKF,GAAqC;AAOnC,SAN2BC,gCAAgC;AAAA,IACzDV;AAAAA,IACAC;AAAAA,IACAQ;AAAAA,EAAAA,CACD,KAGQV,iCAAiC;AAAA,IACtCC;AAAAA,IACAC;AAAAA,EAAAA,CACD;AAIL;ACxBO,SAASU,wBAAwB;AAAA,EACtCX;AAAAA,EACAY;AAAAA,EACAC;AAKF,GAAoB;AAClB,QAAMC,SAASN,4BAA4B;AAAA,IACzCR;AAAAA,IACAC,aAAaW,QAAQE;AAAAA,IACrBL,WAAWI,WAAW,aAAa;AAAA,EAAA,CACpC,GACKE,QAAQP,4BAA4B;AAAA,IACxCR;AAAAA,IACAC,aAAaW,QAAQG;AAAAA,IACrBN,WAAWI,WAAW,YAAY;AAAA,EAAA,CACnC;AAED,SAAI,CAACC,UAAU,CAACC,QACP,OAGF;AAAA,IACLD;AAAAA,IACAC;AAAAA,IACAF;AAAAA,EAAAA;AAEJ;ACzBO,SAASG,iCAAiC;AAAA,EAC/ChB;AAAAA,EACAE;AAIF,GAA4B;AAC1B,MAAIK,SAAS;AAEb,QAAMU,WAAWC,8BAA8BhB,cAAc,GACvDiB,WAAWC,8BAA8BlB,cAAc;AAE7D,MAAI,EAAA,CAACe,YAAY,CAACE;AAIlB,eAAWhB,SAASH,QAAQI;AAC1B,UAAID,MAAME,SAASY,YAIdI,YAAYrB,SAASG,KAAK;AAI/B,mBAAWmB,SAASnB,MAAMoB,UAAU;AAClC,cAAID,MAAMjB,SAASc;AACjB,mBAAO;AAAA,cACLb,MAAM,CAAC;AAAA,gBAACD,MAAMF,MAAME;AAAAA,cAAAA,CAAK;AAAA,cACzBE,QAAQA,SAASL,eAAeK;AAAAA,YAAAA;AAIhCiB,iBAAOxB,SAASsB,KAAK,MACvBf,UAAUe,MAAMG,KAAKC;AAAAA,QAEzB;AAAA;AAEJ;"}