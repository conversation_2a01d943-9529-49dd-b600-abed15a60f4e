#!/usr/bin/env node

/**
 * Production Environment Setup Script
 * Comprehensive setup for all environment variables needed for Bakasana Travel Blog
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🚀 BAKASANA - Production Environment Setup');
console.log('==========================================');

// Generate secure JWT secret
const generateJWTSecret = () => {
  return crypto.randomBytes(32).toString('hex');
};

// Generate secure admin password
const generateAdminPassword = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 16; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
};

// Environment variables template with secure defaults
const envTemplate = {
  // Site Configuration
  NEXT_PUBLIC_SITE_NAME: 'bakasana-travel.blog',
  NEXT_PUBLIC_SITE_DESCRIPTION: 'Odkryj piękno Bali z nami',
  NEXT_PUBLIC_BASE_URL: 'https://bakasana-travel.blog',
  NEXT_PUBLIC_SITE_URL: 'https://bakasana-travel.blog',
  NEXT_PUBLIC_SITE_IMAGE_URL: '/og-image.jpg',
  NODE_ENV: 'production',
  ANALYZE: 'false',

  // Security (Auto-generated)
  JWT_SECRET: generateJWTSecret(),
  ADMIN_PASSWORD: generateAdminPassword(),

  // Analytics & Monitoring
  NEXT_PUBLIC_GA_ID: 'G-M780DCS04D',
  NEXT_PUBLIC_GA_MEASUREMENT_ID: 'G-M780DCS04D',

  // SEO Verification (Placeholders - need real values)
  NEXT_PUBLIC_GOOGLE_VERIFICATION: 'your-google-verification-code',
  NEXT_PUBLIC_YANDEX_VERIFICATION: 'your-yandex-verification-code',
  NEXT_PUBLIC_BING_VERIFICATION: 'your-bing-verification-code',
  NEXT_PUBLIC_PINTEREST_VERIFICATION: 'your-pinterest-verification-code',
  NEXT_PUBLIC_FACEBOOK_VERIFICATION: 'your-facebook-verification-code',

  // Sanity CMS (Need real values)
  NEXT_PUBLIC_SANITY_PROJECT_ID: 'your-project-id',
  NEXT_PUBLIC_SANITY_DATASET: 'production',
  SANITY_API_TOKEN: 'your-sanity-token',
  NEXT_PUBLIC_SANITY_STUDIO_URL: 'https://bakasana-travel.sanity.studio',

  // Email Services (Need real API keys)
  CONVERTKIT_API_KEY: 'ck_your_real_api_key_here',
  CONVERTKIT_FORM_ID: '**********************',
  RESEND_API_KEY: 're_your_real_resend_key_here',
  SMTP_HOST: 'smtp.resend.com',
  SMTP_PORT: '587',
  SMTP_USER: 'resend',
  SMTP_PASS: 're_your_real_resend_key_here',
  WEB3FORMS_ACCESS_KEY: 'your_web3forms_access_key',

  // Optional Analytics
  NEXT_PUBLIC_MIXPANEL_TOKEN: 'your_mixpanel_token',
  NEXT_PUBLIC_HOTJAR_ID: 'your_hotjar_site_id',
  NEXT_PUBLIC_SENTRY_DSN: 'https://<EMAIL>/project-id',
  NEXT_PUBLIC_FB_PIXEL_ID: 'your_facebook_pixel_id',

  // Optional Services
  REDIS_URL: 'redis://localhost:6379',
  NEXT_PUBLIC_MAPBOX_TOKEN: 'your_mapbox_token',
  NEXT_PUBLIC_VAPID_PUBLIC_KEY: 'your_vapid_public_key',
  VAPID_PRIVATE_KEY: 'your_vapid_private_key',
};

// Create Vercel environment variables format
function createVercelEnvFormat() {
  console.log('\n📋 VERCEL ENVIRONMENT VARIABLES');
  console.log('Copy these to Vercel Dashboard → Settings → Environment Variables:');
  console.log('='.repeat(70));

  Object.entries(envTemplate).forEach(([key, value]) => {
    console.log(`${key}=${value}`);
  });

  console.log('\n🔐 SECURITY NOTES:');
  console.log(`✅ JWT_SECRET: ${envTemplate.JWT_SECRET} (SECURE - 32 bytes)`);
  console.log(`✅ ADMIN_PASSWORD: ${envTemplate.ADMIN_PASSWORD} (SECURE - 16 chars)`);
}

// Create .env.local file
function createLocalEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  let envContent = '# BAKASANA - Production Environment Variables\n';
  envContent += '# Generated automatically with secure values\n\n';

  Object.entries(envTemplate).forEach(([key, value]) => {
    envContent += `${key}=${value}\n`;
  });

  fs.writeFileSync(envPath, envContent);
  console.log(`\n✅ Created ${envPath} with secure values`);
}

// Validation function
function validateEnvironment() {
  console.log('\n🔍 VALIDATING ENVIRONMENT...');
  
  const criticalVars = ['JWT_SECRET', 'ADMIN_PASSWORD', 'NEXT_PUBLIC_SANITY_PROJECT_ID'];
  const warnings = [];
  const errors = [];

  criticalVars.forEach(varName => {
    const value = envTemplate[varName];
    if (!value || value.includes('your-') || value.includes('placeholder')) {
      errors.push(`❌ ${varName}: Needs real value`);
    } else if (varName === 'JWT_SECRET' && value.length < 32) {
      warnings.push(`⚠️  ${varName}: Should be 32+ characters`);
    } else {
      console.log(`✅ ${varName}: OK`);
    }
  });

  if (errors.length > 0) {
    console.log('\n🚨 CRITICAL ISSUES:');
    errors.forEach(error => console.log(error));
  }

  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS:');
    warnings.forEach(warning => console.log(warning));
  }

  return { errors, warnings };
}

// Main execution
function main() {
  const { errors, warnings } = validateEnvironment();
  
  createVercelEnvFormat();
  createLocalEnvFile();

  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Copy environment variables to Vercel Dashboard');
  console.log('2. Set up Sanity CMS and get real Project ID');
  console.log('3. Configure email services (ConvertKit, Resend)');
  console.log('4. Add SEO verification codes');
  console.log('5. Run: npm run env:validate');
  console.log('6. Deploy to Vercel');

  if (errors.length === 0) {
    console.log('\n✅ Environment setup complete!');
  } else {
    console.log('\n⚠️  Please fix critical issues before deployment');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  generateJWTSecret,
  generateAdminPassword,
  envTemplate,
  createVercelEnvFormat,
  validateEnvironment
};
