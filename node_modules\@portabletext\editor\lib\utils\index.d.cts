import { Block<PERSON>ffs<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Editor<PERSON>ontex<PERSON>, EditorSelection, EditorSelectionPoint } from "../_chunks-dts/behavior.types.action.cjs";
import * as _sanity_types9 from "@sanity/types";
import { KeyedSegment, PortableTextBlock, PortableTextChild, PortableTextSpan, PortableTextTextBlock } from "@sanity/types";
/**
 * @public
 */
declare function blockOffsetToSpanSelectionPoint({
  context,
  blockOffset,
  direction
}: {
  context: Pick<EditorContext, 'schema' | 'value'>;
  blockOffset: BlockOffset;
  direction: 'forward' | 'backward';
}): {
  path: ChildPath;
  offset: number;
} | undefined;
/**
 * @public
 */
declare function spanSelectionPointToBlockOffset({
  context,
  selectionPoint
}: {
  context: Pick<EditorContext, 'schema' | 'value'>;
  selectionPoint: EditorSelectionPoint;
}): BlockOffset | undefined;
/**
 * @public
 */
declare function blockOffsetToBlockSelectionPoint({
  context,
  blockOffset
}: {
  context: Pick<EditorContext, 'value'>;
  blockOffset: BlockOffset;
}): EditorSelectionPoint | undefined;
/**
 * @public
 */
declare function blockOffsetToSelectionPoint({
  context,
  blockOffset,
  direction
}: {
  context: Pick<EditorContext, 'schema' | 'value'>;
  blockOffset: BlockOffset;
  direction: 'forward' | 'backward';
}): EditorSelectionPoint | undefined;
/**
 * @public
 */
declare function blockOffsetsToSelection({
  context,
  offsets,
  backward
}: {
  context: Pick<EditorContext, 'schema' | 'value'>;
  offsets: {
    anchor: BlockOffset;
    focus: BlockOffset;
  };
  backward?: boolean;
}): EditorSelection;
/**
 * @public
 */
declare function childSelectionPointToBlockOffset({
  context,
  selectionPoint
}: {
  context: Pick<EditorContext, 'schema' | 'value'>;
  selectionPoint: EditorSelectionPoint;
}): BlockOffset | undefined;
/**
 * @public
 */
declare function getBlockEndPoint({
  context,
  block
}: {
  context: Pick<EditorContext, 'schema'>;
  block: {
    node: PortableTextBlock;
    path: BlockPath;
  };
}): EditorSelectionPoint;
/**
 * @public
 */
declare function getBlockStartPoint({
  context,
  block
}: {
  context: Pick<EditorContext, 'schema'>;
  block: {
    node: PortableTextBlock;
    path: BlockPath;
  };
}): EditorSelectionPoint;
/**
 * @public
 */
declare function getSelectionEndPoint<TEditorSelection extends NonNullable<EditorSelection> | null, TEditorSelectionPoint extends EditorSelectionPoint | null = (TEditorSelection extends NonNullable<EditorSelection> ? EditorSelectionPoint : null)>(selection: TEditorSelection): TEditorSelectionPoint;
/**
 * @public
 */
declare function getSelectionStartPoint<TEditorSelection extends NonNullable<EditorSelection> | null, TEditorSelectionPoint extends EditorSelectionPoint | null = (TEditorSelection extends NonNullable<EditorSelection> ? EditorSelectionPoint : null)>(selection: TEditorSelection): TEditorSelectionPoint;
/**
 * @public
 */
declare function getTextBlockText(block: PortableTextTextBlock): string;
/**
 * @public
 */
declare function isEmptyTextBlock(context: Pick<EditorContext, 'schema'>, block: PortableTextBlock): boolean;
/**
 * @public
 */
declare function isEqualSelectionPoints(a: EditorSelectionPoint, b: EditorSelectionPoint): boolean;
/**
 * @public
 */
declare function isEqualSelections(a: EditorSelection, b: EditorSelection): boolean;
/**
 * @public
 */
declare function isKeyedSegment(segment: unknown): segment is KeyedSegment;
/**
 * @public
 */
declare function isSelectionCollapsed(selection: EditorSelection): boolean;
/**
 * @public
 */
declare function isSpan(context: Pick<EditorContext, 'schema'>, child: PortableTextChild): child is PortableTextSpan;
/**
 * @public
 */
declare function isTextBlock(context: Pick<EditorContext, 'schema'>, block: unknown): block is PortableTextTextBlock;
/**
 * @beta
 */
declare function mergeTextBlocks({
  context,
  targetBlock,
  incomingBlock
}: {
  context: Pick<EditorContext, 'keyGenerator' | 'schema'>;
  targetBlock: PortableTextTextBlock;
  incomingBlock: PortableTextTextBlock;
}): PortableTextTextBlock<_sanity_types9.PortableTextObject | _sanity_types9.PortableTextSpan>;
/**
 * @public
 */
declare function reverseSelection<TEditorSelection extends NonNullable<EditorSelection> | null>(selection: TEditorSelection): TEditorSelection;
/**
 * @public
 */
declare function selectionPointToBlockOffset({
  context,
  selectionPoint
}: {
  context: Pick<EditorContext, 'schema' | 'value'>;
  selectionPoint: EditorSelectionPoint;
}): BlockOffset | undefined;
/**
 * @public
 */
declare function sliceBlocks({
  context,
  blocks
}: {
  context: Pick<EditorContext, 'schema' | 'selection'>;
  blocks: Array<PortableTextBlock>;
}): Array<PortableTextBlock>;
/**
 * @beta
 */
declare function splitTextBlock({
  context,
  block,
  point
}: {
  context: Pick<EditorContext, 'schema'>;
  block: PortableTextTextBlock;
  point: EditorSelectionPoint;
}): {
  before: PortableTextTextBlock;
  after: PortableTextTextBlock;
} | undefined;
export { blockOffsetToBlockSelectionPoint, blockOffsetToSelectionPoint, blockOffsetToSpanSelectionPoint, blockOffsetsToSelection, childSelectionPointToBlockOffset, getBlockEndPoint, getBlockStartPoint, getSelectionEndPoint, getSelectionStartPoint, getTextBlockText, isEmptyTextBlock, isEqualSelectionPoints, isEqualSelections, isKeyedSegment, isSelectionCollapsed, isSpan, isTextBlock, mergeTextBlocks, reverseSelection, selectionPointToBlockOffset, sliceBlocks, spanSelectionPointToBlockOffset, splitTextBlock };