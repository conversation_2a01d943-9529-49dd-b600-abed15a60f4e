'use client';
import React from 'react';

import { motion } from 'framer-motion';

import { HeroTitle, BodyText } from '@/components/ui/UnifiedTypography';
import { UnifiedButton } from '@/components/ui/UnifiedButton';

// Empty State Component
export function EmptyState({ title, description, action, icon }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-12"
    >
      {icon && <div className="text-4xl mb-4">{icon}</div>}
      <h3 className="text-xl font-semibold text-charcoal mb-2">{title}</h3>
      <p className="text-charcoal/70 mb-6">{description}</p>
      {action}
    </motion.div>
  );
}

// Loading State Component
export function LoadingState({ type = 'default' }) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="flex items-center justify-center py-12"
    >
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-terra-lotus"></div>
      <span className="ml-3 text-charcoal/70">Ładowanie...</span>
    </motion.div>
  );
}

// Network Error Component
export function NetworkError({ onRetry }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-12"
    >
      <div className="text-4xl mb-4">🌐</div>
      <h3 className="text-xl font-semibold text-charcoal mb-2">Błąd połączenia</h3>
      <p className="text-charcoal/70 mb-6">Sprawdź połączenie internetowe i spróbuj ponownie</p>
      <UnifiedButton onClick={onRetry} variant="primary">
        Spróbuj ponownie
      </UnifiedButton>
    </motion.div>
  );
}

// Offline State Component
export function OfflineState() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-12"
    >
      <div className="text-4xl mb-4">📱</div>
      <h3 className="text-xl font-semibold text-charcoal mb-2">Tryb offline</h3>
      <p className="text-charcoal/70">Niektóre funkcje mogą być niedostępne</p>
    </motion.div>
  );
}

// Main Error Boundary Class
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to monitoring service
    if (typeof window !== 'undefined') {
      console.error(
        'Error Boundary caught an error:',
        error,
        errorInfo
      );

      // Send to error tracking service
      if (window.gtag) {
        window.gtag('event', 'exception', {
          description: error.toString(),
          fatal: false,
        });
      }
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
    }));
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
          retryCount={this.state.retryCount}
          fallback={this.props.fallback}
        />
      );
    }

    return this.props.children;
  }
}

// Error Fallback Component
function ErrorFallback({ error, onRetry, retryCount, fallback }) {
  if (fallback) {
    return fallback;
  }

  return (
    <div className="min-h-screen bg-sanctuary flex items-center justify-center px-container-sm">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full text-center"
      >
        {/* Om Symbol */}
        <motion.div
          initial={{ scale: 0.8, rotate: -10 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-6xl text-terra-lotus mb-lg"
        >
          ॐ
        </motion.div>

        {/* Error Message */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <HeroTitle className="text-3xl text-charcoal mb-sm">
            Ups... Coś poszło nie tak
          </HeroTitle>

          <BodyText className="text-lg text-charcoal/70 mb-lg leading-relaxed">
            Przepraszamy, ale wystąpił nieoczekiwany błąd. Nasz zespół został powiadomiony i pracuje nad rozwiązaniem.
          </BodyText>

          {/* Actions */}
          <div className="space-y-sm">
            <UnifiedButton
              onClick={onRetry}
              variant="primary"
              className="w-full"
              disabled={retryCount >= 3}
            >
              {retryCount >= 3 ? 'Zbyt wiele prób' : 'Odśwież stronę'}
            </UnifiedButton>

            <UnifiedButton
              href="/"
              variant="secondary"
              className="w-full"
            >
              Wróć na stronę główną
            </UnifiedButton>

            <UnifiedButton
              href="/kontakt"
              variant="ghost"
              className="w-full"
            >
              Skontaktuj się z nami
            </UnifiedButton>
          </div>

          {/* Debug Info (only in development) */}
          {process.env.NODE_ENV === 'development' && error && (
            <motion.details
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              className="mt-lg text-left bg-warm-sanctuary p-4 rounded"
            >
              <summary className="cursor-pointer text-sm font-medium text-charcoal/60 mb-2">
                Szczegóły błędu (tylko w trybie deweloperskim)
              </summary>
              <pre className="text-xs text-charcoal/50 overflow-auto">
                {error.toString()}
              </pre>
            </motion.details>
          )}
        </motion.div>
      </motion.div>
    </div>
  );
}

// Export default ErrorBoundary
export default ErrorBoundary;
