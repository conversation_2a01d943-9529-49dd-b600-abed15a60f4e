import { getBlockKeyFromSelectionPoint, getChildKeyFromSelectionPoint, isSpan, getSelectionStartPoint, getSelectionEndPoint } from "./util.slice-blocks.js";
import { childSelectionPointToBlockOffset } from "./util.child-selection-point-to-block-offset.js";
function selectionPointToBlockOffset({
  context,
  selectionPoint
}) {
  const blockKey = getBlockKeyFromSelectionPoint(selectionPoint);
  return selectionPoint.path.length === 1 && blockKey !== void 0 ? {
    path: [{
      _key: blockKey
    }],
    offset: selectionPoint.offset
  } : childSelectionPointToBlockOffset({
    context,
    selectionPoint
  });
}
function sliceTextBlock({
  context,
  block
}) {
  const startPoint = getSelectionStartPoint(context.selection), endPoint = getSelectionEndPoint(context.selection);
  if (!startPoint || !endPoint)
    return block;
  const startBlockKey = getBlockKeyFromSelectionPoint(startPoint), endBlockKey = getBlockKeyFromSelectionPoint(endPoint);
  if (startBlockKey !== endBlockKey || startBlockKey !== block._key)
    return block;
  const startChildKey = getChildKeyFromSelectionPoint(startPoint), endChildKey = getChildKeyFromSelectionPoint(endPoint);
  if (!startChildKey || !endChildKey)
    return block;
  let startChildFound = !1;
  const children = [];
  for (const child of block.children) {
    if (child._key === startChildKey) {
      if (startChildFound = !0, isSpan(context, child)) {
        const text = child._key === endChildKey ? child.text.slice(startPoint.offset, endPoint.offset) : child.text.slice(startPoint.offset);
        children.push({
          ...child,
          text
        });
      } else
        children.push(child);
      if (startChildKey === endChildKey)
        break;
      continue;
    }
    if (child._key === endChildKey) {
      isSpan(context, child) ? children.push({
        ...child,
        text: child.text.slice(0, endPoint.offset)
      }) : children.push(child);
      break;
    }
    startChildFound && children.push(child);
  }
  return {
    ...block,
    children
  };
}
export {
  selectionPointToBlockOffset,
  sliceTextBlock
};
//# sourceMappingURL=util.slice-text-block.js.map
