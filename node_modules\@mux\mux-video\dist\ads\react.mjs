"use client";import R from"@mux/mux-video/ads";import W from"react";var N=new Set(["style","children","ref","key","suppressContentEditableWarning","suppressHydrationWarning","dangerouslySetInnerHTML"]),S={className:"class",htmlFor:"for"};function O(t){return t.toLowerCase()}function M(t){if(typeof t=="boolean")return t?"":void 0;if(typeof t!="function"&&!(typeof t=="object"&&t!==null))return t}function k({react:t,tagName:d,elementClass:r,events:a,displayName:f,toAttributeName:_=O,toAttributeValue:I=M}){let m=Number.parseInt(t.version)>=19,g=t.forwardRef((w,l)=>{var h,E,v,L,P;let i=t.useRef(null),y=t.useRef(new Map),b={},T={},u={},p={};for(let[o,e]of Object.entries(w)){if(N.has(o)){u[o]=e;continue}let n=_((h=S[o])!=null?h:o);if(o in r.prototype&&!(o in((v=(E=globalThis.HTMLElement)==null?void 0:E.prototype)!=null?v:{}))&&!((L=r.observedAttributes)!=null&&L.some(s=>s===n))){p[o]=e;continue}if(o.startsWith("on")){b[o]=e;continue}let c=I(e);if(n&&c!=null&&(T[n]=String(c),m||(u[n]=c)),n&&m){let s=M(e);c!==s?u[n]=c:u[n]=e}}if(typeof window!="undefined"){for(let o in b){let e=b[o],n=o.endsWith("Capture"),c=((P=a==null?void 0:a[o])!=null?P:o.slice(2).toLowerCase()).slice(0,n?-7:void 0);t.useLayoutEffect(()=>{let s=i==null?void 0:i.current;if(!(!s||typeof e!="function"))return s.addEventListener(c,e,n),()=>{s.removeEventListener(c,e,n)}},[i==null?void 0:i.current,e])}t.useLayoutEffect(()=>{if(i.current===null)return;let o=new Map;for(let e in p)A(i.current,e,p[e]),y.current.delete(e),o.set(e,p[e]);for(let[e,n]of y.current)A(i.current,e,void 0);y.current=o})}if(typeof window=="undefined"&&(r!=null&&r.getTemplateHTML)&&(r!=null&&r.shadowRootOptions)){let{mode:o,delegatesFocus:e}=r.shadowRootOptions,n=t.createElement("template",{shadowrootmode:o,shadowrootdelegatesfocus:e,dangerouslySetInnerHTML:{__html:r.getTemplateHTML(T,w)}});u.children=[n,u.children]}return t.createElement(d,{...u,ref:t.useCallback(o=>{i.current=o,typeof l=="function"?l(o):l!==null&&(l.current=o)},[l])})});return g.displayName=f!=null?f:r.name,g}function A(t,d,r){var a,f;t[d]=r,r==null&&d in((f=(a=globalThis.HTMLElement)==null?void 0:a.prototype)!=null?f:{})&&t.removeAttribute(d)}var $=k({react:W,tagName:"mux-video",elementClass:R,toAttributeName:x}),H={autoPlay:"autoplay",controlsList:"controlslist",crossOrigin:"crossorigin",playsInline:"playsinline",disablePictureInPicture:"disablepictureinpicture",disableRemotePlayback:"disableremoteplayback"};function x(t){return H[t]?H[t]:t.replace(/([A-Z])/g,"-$1").toLowerCase()}export{$ as default};
/*! Bundled license information:

ce-la-react/dist/ce-la-react.js:
  (**
   * @license
   * Copyright 2018 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *
   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.
   *)
*/
