'use client';

import React, { useEffect, useRef, useState } from 'react';

/**
 * 🎬 STAGGERED REVEAL - TOP 1% DESIGN FEATURE
 * Elementy pojawiają się z 50ms opóźnieniem między sobą
 * Inspirowane przez Linear.app, Stripe, Apple
 */
const StaggeredReveal = ({
  children,
  delay = 50,
  duration = 600,
  offset = 20,
  className = '',
  triggerOnce = true,
  threshold = 0.1,
  ...props
}) => {
  const containerRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && (!triggerOnce || !hasTriggered)) {
          setIsVisible(true);
          setHasTriggered(true);
        } else if (!triggerOnce && !entry.isIntersecting) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin: '50px',
      }
    );

    observer.observe(container);

    return () => observer.disconnect();
  }, [triggerOnce, hasTriggered, threshold]);

  // Convert children to array for staggered animation
  const childrenArray = React.Children.toArray(children);

  return (
    <div
      ref={containerRef}
      className={`staggered-reveal ${className}`}
      {...props}
    >
      {childrenArray.map((child, index) => (
        <div
          key={index}
          className={`staggered-item ${isVisible ? 'visible' : ''}`}
          style={{
            '--delay': `${index * delay}ms`,
            '--duration': `${duration}ms`,
            '--offset': `${offset}px`,
            opacity: isVisible ? 1 : 0,
            transform: isVisible ? 'translateY(0)' : `translateY(${offset}px)`,
            transition: `opacity ${duration}ms cubic-bezier(0.22, 1, 0.36, 1) var(--delay), 
                        transform ${duration}ms cubic-bezier(0.22, 1, 0.36, 1) var(--delay)`,
          }}
        >
          {child}
        </div>
      ))}

      <style jsx>{`
        .staggered-reveal {
          overflow: hidden;
        }

        .staggered-item {
          will-change: transform, opacity;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          .staggered-item {
            transition: none !important;
            opacity: 1 !important;
            transform: none !important;
          }
        }
      `}</style>
    </div>
  );
};

export default React.memo(StaggeredReveal);
