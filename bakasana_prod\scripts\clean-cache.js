/**
 * Skrypt do czyszczenia pamięci podręcznej Next.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Katalogi do wyczyszczenia
const directories = ['.next', '.vercel/output', 'node_modules/.cache'];

// Funkcja do usuwania katalogu
function removeDirectory(dir) {
  const fullPath = path.join(process.cwd(), dir);

  try {
    if (fs.existsSync(fullPath)) {
      console.log(`Usuwanie katalogu: ${fullPath}`);

      // Użyj różnych komend w zależności od systemu operacyjnego
      if (process.platform === 'win32') {
        // Windows
        execSync(
          `powershell -Command "if (Test-Path '${fullPath}') { Remove-Item -Path '${fullPath}' -Recurse -Force }"`
        );
      } else {
        // Linux/Mac
        execSync(`rm -rf "${fullPath}"`);
      }

      console.log(`Katalog ${dir} został usunięty.`);
    } else {
      console.log(`Katalog ${dir} nie istnieje.`);
    }
  } catch (error) {
    console.error(`Błąd podczas usuwania katalogu ${dir}:`, error);
  }
}

// Główna funkcja
function cleanCache() {
  console.log('Rozpoczynam czyszczenie pamięci podręcznej...');

  // Usuń wszystkie katalogi
  directories.forEach(removeDirectory);

  console.log('Czyszczenie pamięci podręcznej zakończone.');
}

// Uruchom funkcję
cleanCache();
