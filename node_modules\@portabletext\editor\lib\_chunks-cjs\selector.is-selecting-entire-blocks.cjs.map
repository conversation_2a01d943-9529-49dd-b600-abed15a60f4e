{"version": 3, "file": "selector.is-selecting-entire-blocks.cjs", "sources": ["../../src/utils/util.is-selection-expanded.ts", "../../src/selectors/selector.get-selection-end-block.ts", "../../src/selectors/selector.get-selection-end-point.ts", "../../src/selectors/selector.get-next-span.ts", "../../src/selectors/selector.get-selection-start-block.ts", "../../src/selectors/selector.get-previous-span.ts", "../../src/selectors/selector.get-selected-spans.ts", "../../src/selectors/selector.get-mark-state.ts", "../../src/selectors/selector.get-selected-blocks.ts", "../../src/selectors/selector.get-active-annotations.ts", "../../src/selectors/selector.get-active-list-item.ts", "../../src/selectors/selector.get-active-style.ts", "../../src/selectors/selector.get-next-inline-object.ts", "../../src/selectors/selector.get-caret-word-selection.ts", "../../src/selectors/selector.get-first-block.ts", "../../src/selectors/selector.get-focus-block-object.ts", "../../src/selectors/selector.get-focus-inline-object.ts", "../../src/selectors/selector.get-focus-list-block.ts", "../../src/selectors/selector.get-last-block.ts", "../../src/selectors/selector.get-next-block.ts", "../../src/selectors/selector.get-previous-block.ts", "../../src/selectors/selector.get-selected-text-blocks.ts", "../../src/selectors/selector.get-trimmed-selection.ts", "../../src/selectors/selector.get-active-annotation-marks.ts", "../../src/selectors/selector.is-active-annotation.ts", "../../src/selectors/selector.get-active-decorators.ts", "../../src/selectors/selector.is-active-decorator.ts", "../../src/selectors/selector.is-active-list-item.ts", "../../src/selectors/selector.is-active-style.ts", "../../src/selectors/selector.is-at-the-end-of-block.ts", "../../src/selectors/selector.is-at-the-start-of-block.ts", "../../src/selectors/selector.is-point-after-selection.ts", "../../src/selectors/selector.is-point-before-selection.ts", "../../src/selectors/selector.is-overlapping-selection.ts", "../../src/selectors/selector.is-selecting-entire-blocks.ts"], "sourcesContent": ["import type {EditorSelection} from '../types/editor'\nimport {isSelectionCollapsed} from './util.is-selection-collapsed'\n\n/**\n * @public\n */\nexport function isSelectionExpanded(selection: EditorSelection) {\n  if (!selection) {\n    return false\n  }\n\n  return !isSelectionCollapsed(selection)\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockPath} from '../types/paths'\nimport {getSelectionEndPoint} from '../utils/util.get-selection-end-point'\nimport {getFocusBlock} from './selector.get-focus-block'\n\n/**\n * @public\n */\nexport const getSelectionEndBlock: EditorSelector<\n  | {\n      node: PortableTextBlock\n      path: BlockPath\n    }\n  | undefined\n> = (snapshot) => {\n  const endPoint = getSelectionEndPoint(snapshot.context.selection)\n\n  if (!endPoint) {\n    return undefined\n  }\n\n  return getFocusBlock({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: {\n        anchor: endPoint,\n        focus: endPoint,\n      },\n    },\n  })\n}\n", "import type {EditorSelectionPoint} from '..'\nimport type {EditorSelector} from '../editor/editor-selector'\n\n/**\n * @public\n */\nexport const getSelectionEndPoint: EditorSelector<\n  EditorSelectionPoint | undefined\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  return snapshot.context.selection.backward\n    ? snapshot.context.selection.anchor\n    : snapshot.context.selection.focus\n}\n", "import type {KeyedSegment, PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {getChildKeyFromSelectionPoint} from '../selection/selection-point'\nimport {getSelectionEndBlock} from './selector.get-selection-end-block'\nimport {getSelectionEndPoint} from './selector.get-selection-end-point'\n\n/**\n * @public\n */\nexport const getNextSpan: EditorSelector<\n  | {\n      node: PortableTextSpan\n      path: [KeyedSegment, 'children', KeyedSegment]\n    }\n  | undefined\n> = (snapshot) => {\n  const selectionEndBlock = getSelectionEndBlock(snapshot)\n  const selectionEndPoint = getSelectionEndPoint(snapshot)\n\n  if (!selectionEndBlock || !selectionEndPoint) {\n    return undefined\n  }\n\n  if (!isTextBlock(snapshot.context, selectionEndBlock.node)) {\n    return undefined\n  }\n\n  const selectionEndPointChildKey =\n    getChildKeyFromSelectionPoint(selectionEndPoint)\n\n  let endPointChildFound = false\n  let nextSpan:\n    | {\n        node: PortableTextSpan\n        path: [KeyedSegment, 'children', KeyedSegment]\n      }\n    | undefined\n\n  for (const child of selectionEndBlock.node.children) {\n    if (child._key === selectionEndPointChildKey) {\n      endPointChildFound = true\n      continue\n    }\n\n    if (isSpan(snapshot.context, child) && endPointChildFound) {\n      nextSpan = {\n        node: child,\n        path: [...selectionEndBlock.path, 'children', {_key: child._key}],\n      }\n      break\n    }\n  }\n\n  return nextSpan\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockPath} from '../types/paths'\nimport {getSelectionStartPoint} from '../utils/util.get-selection-start-point'\nimport {getFocusBlock} from './selector.get-focus-block'\n\n/**\n * @public\n */\nexport const getSelectionStartBlock: EditorSelector<\n  | {\n      node: PortableTextBlock\n      path: BlockPath\n    }\n  | undefined\n> = (snapshot) => {\n  const startPoint = getSelectionStartPoint(snapshot.context.selection)\n\n  if (!startPoint) {\n    return undefined\n  }\n\n  return getFocusBlock({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: {\n        anchor: startPoint,\n        focus: startPoint,\n      },\n    },\n  })\n}\n", "import type {KeyedSegment, PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {getChildKeyFromSelectionPoint} from '../selection/selection-point'\nimport {getSelectionStartBlock} from './selector.get-selection-start-block'\nimport {getSelectionStartPoint} from './selector.get-selection-start-point'\n\n/**\n * @public\n */\nexport const getPreviousSpan: EditorSelector<\n  | {\n      node: PortableTextSpan\n      path: [KeyedSegment, 'children', KeyedSegment]\n    }\n  | undefined\n> = (snapshot) => {\n  const selectionStartBlock = getSelectionStartBlock(snapshot)\n  const selectionStartPoint = getSelectionStartPoint(snapshot)\n\n  if (!selectionStartBlock || !selectionStartPoint) {\n    return undefined\n  }\n\n  if (!isTextBlock(snapshot.context, selectionStartBlock.node)) {\n    return undefined\n  }\n\n  const selectionStartPointChildKey =\n    getChildKeyFromSelectionPoint(selectionStartPoint)\n\n  let previousSpan:\n    | {\n        node: PortableTextSpan\n        path: [KeyedSegment, 'children', KeyedSegment]\n      }\n    | undefined\n\n  for (const child of selectionStartBlock.node.children) {\n    if (child._key === selectionStartPointChildKey) {\n      break\n    }\n\n    if (isSpan(snapshot.context, child)) {\n      previousSpan = {\n        node: child,\n        path: [...selectionStartBlock.path, 'children', {_key: child._key}],\n      }\n    }\n  }\n\n  return previousSpan\n}\n", "import type {PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {\n  getBlockKeyFromSelectionPoint,\n  getChildKeyFromSelectionPoint,\n} from '../selection/selection-point'\nimport type {ChildPath} from '../types/paths'\nimport {getSelectionEndPoint} from './selector.get-selection-end-point'\nimport {getSelectionStartPoint} from './selector.get-selection-start-point'\n\n/**\n * @public\n */\nexport const getSelectedSpans: EditorSelector<\n  Array<{\n    node: PortableTextSpan\n    path: ChildPath\n  }>\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return []\n  }\n\n  const selectedSpans: Array<{\n    node: PortableTextSpan\n    path: ChildPath\n  }> = []\n\n  const startPoint = getSelectionStartPoint(snapshot)\n  const endPoint = getSelectionEndPoint(snapshot)\n\n  if (!startPoint || !endPoint) {\n    return selectedSpans\n  }\n\n  const startBlockKey = getBlockKeyFromSelectionPoint(startPoint)\n  const endBlockKey = getBlockKeyFromSelectionPoint(endPoint)\n  const startSpanKey = getChildKeyFromSelectionPoint(startPoint)\n  const endSpanKey = getChildKeyFromSelectionPoint(endPoint)\n\n  if (!startBlockKey || !endBlockKey) {\n    return selectedSpans\n  }\n\n  const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey)\n  const endBlockIndex = snapshot.blockIndexMap.get(endBlockKey)\n\n  if (startBlockIndex === undefined || endBlockIndex === undefined) {\n    return selectedSpans\n  }\n\n  const slicedValue = snapshot.context.value.slice(\n    startBlockIndex,\n    endBlockIndex + 1,\n  )\n\n  let startBlockFound = false\n\n  for (const block of slicedValue) {\n    if (block._key === startBlockKey) {\n      startBlockFound = true\n    }\n\n    if (!isTextBlock(snapshot.context, block)) {\n      continue\n    }\n\n    if (block._key === startBlockKey) {\n      for (const child of block.children) {\n        if (!isSpan(snapshot.context, child)) {\n          continue\n        }\n\n        if (startSpanKey && child._key === startSpanKey) {\n          if (startPoint.offset < child.text.length) {\n            selectedSpans.push({\n              node: child,\n              path: [{_key: block._key}, 'children', {_key: child._key}],\n            })\n          }\n\n          if (startSpanKey === endSpanKey) {\n            break\n          }\n\n          continue\n        }\n\n        if (endSpanKey && child._key === endSpanKey) {\n          if (endPoint.offset > 0) {\n            selectedSpans.push({\n              node: child,\n              path: [{_key: block._key}, 'children', {_key: child._key}],\n            })\n          }\n          break\n        }\n\n        if (selectedSpans.length > 0) {\n          selectedSpans.push({\n            node: child,\n            path: [{_key: block._key}, 'children', {_key: child._key}],\n          })\n        }\n      }\n\n      if (startBlockKey === endBlockKey) {\n        break\n      }\n\n      continue\n    }\n\n    if (block._key === endBlockKey) {\n      for (const child of block.children) {\n        if (!isSpan(snapshot.context, child)) {\n          continue\n        }\n\n        if (endSpanKey && child._key === endSpanKey) {\n          if (endPoint.offset > 0) {\n            selectedSpans.push({\n              node: child,\n              path: [{_key: block._key}, 'children', {_key: child._key}],\n            })\n          }\n          break\n        }\n\n        selectedSpans.push({\n          node: child,\n          path: [{_key: block._key}, 'children', {_key: child._key}],\n        })\n      }\n\n      break\n    }\n\n    if (startBlockFound) {\n      for (const child of block.children) {\n        if (!isSpan(snapshot.context, child)) {\n          continue\n        }\n\n        selectedSpans.push({\n          node: child,\n          path: [{_key: block._key}, 'children', {_key: child._key}],\n        })\n      }\n    }\n  }\n\n  return selectedSpans\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {isSelectionExpanded} from '../utils/util.is-selection-expanded'\nimport {getFocusSpan} from './selector.get-focus-span'\nimport {getFocusTextBlock} from './selector.get-focus-text-block'\nimport {getNextSpan} from './selector.get-next-span'\nimport {getPreviousSpan} from './selector.get-previous-span'\nimport {getSelectedSpans} from './selector.get-selected-spans'\n\nexport type MarkState = {\n  state: 'changed' | 'unchanged'\n  marks: Array<string>\n}\n\n/**\n * Given that text is inserted at the current position, what marks should\n * be applied?\n */\nexport const getMarkState: EditorSelector<MarkState | undefined> = (\n  snapshot,\n) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  const focusTextBlock = getFocusTextBlock(snapshot)\n  const focusSpan = getFocusSpan(snapshot)\n\n  if (!focusTextBlock || !focusSpan) {\n    return undefined\n  }\n\n  if (isSelectionExpanded(snapshot.context.selection)) {\n    const selectedSpans = getSelectedSpans(snapshot)\n\n    let index = 0\n    let marks: Array<string> = []\n\n    for (const span of selectedSpans) {\n      if (index === 0) {\n        marks = span.node.marks ?? []\n      } else {\n        if (span.node.marks?.length === 0) {\n          marks = []\n          continue\n        }\n\n        marks = marks.filter((mark) =>\n          (span.node.marks ?? []).some((spanMark) => spanMark === mark),\n        )\n      }\n\n      index++\n    }\n\n    return {\n      state: 'unchanged',\n      marks,\n    }\n  }\n\n  const decorators = snapshot.context.schema.decorators.map(\n    (decorator) => decorator.name,\n  )\n  const marks = focusSpan.node.marks ?? []\n  const marksWithoutAnnotations = marks.filter((mark) =>\n    decorators.includes(mark),\n  )\n\n  const spanHasAnnotations = marks.length > marksWithoutAnnotations.length\n\n  const spanIsEmpty = focusSpan.node.text.length === 0\n\n  const atTheBeginningOfSpan = snapshot.context.selection.anchor.offset === 0\n  const atTheEndOfSpan =\n    snapshot.context.selection.anchor.offset === focusSpan.node.text.length\n\n  const previousSpan = getPreviousSpan(snapshot)\n  const nextSpan = getNextSpan(snapshot)\n  const nextSpanAnnotations =\n    nextSpan?.node?.marks?.filter((mark) => !decorators.includes(mark)) ?? []\n  const spanAnnotations = marks.filter((mark) => !decorators.includes(mark))\n\n  const previousSpanHasAnnotations = previousSpan\n    ? previousSpan.node.marks?.some((mark) => !decorators.includes(mark))\n    : false\n  const previousSpanHasSameAnnotations = previousSpan\n    ? previousSpan.node.marks\n        ?.filter((mark) => !decorators.includes(mark))\n        .every((mark) => marks.includes(mark))\n    : false\n  const previousSpanHasSameAnnotation = previousSpan\n    ? previousSpan.node.marks?.some(\n        (mark) => !decorators.includes(mark) && marks.includes(mark),\n      )\n    : false\n\n  const previousSpanHasSameMarks = previousSpan\n    ? previousSpan.node.marks?.every((mark) => marks.includes(mark))\n    : false\n  const nextSpanSharesSomeAnnotations = spanAnnotations.some((mark) =>\n    nextSpanAnnotations?.includes(mark),\n  )\n\n  if (spanHasAnnotations && !spanIsEmpty) {\n    if (atTheBeginningOfSpan) {\n      if (previousSpanHasSameMarks) {\n        return {\n          state: 'changed',\n          marks: previousSpan?.node.marks ?? [],\n        }\n      } else if (previousSpanHasSameAnnotations) {\n        return {\n          state: 'changed',\n          marks: previousSpan?.node.marks ?? [],\n        }\n      } else if (previousSpanHasSameAnnotation) {\n        return {\n          state: 'unchanged',\n          marks: focusSpan.node.marks ?? [],\n        }\n      } else if (!previousSpan) {\n        return {\n          state: 'changed',\n          marks: [],\n        }\n      }\n    }\n\n    if (atTheEndOfSpan) {\n      if (\n        (nextSpan &&\n          nextSpanSharesSomeAnnotations &&\n          nextSpanAnnotations.length < spanAnnotations.length) ||\n        !nextSpanSharesSomeAnnotations\n      ) {\n        return {\n          state: 'changed',\n          marks: nextSpan?.node.marks ?? [],\n        }\n      }\n\n      if (!nextSpan) {\n        return {\n          state: 'changed',\n          marks: [],\n        }\n      }\n    }\n  }\n\n  if (atTheBeginningOfSpan && !spanIsEmpty && !!previousSpan) {\n    if (previousSpanHasAnnotations) {\n      return {\n        state: 'changed',\n        marks: [],\n      }\n    } else {\n      return {\n        state: 'changed',\n        marks: (previousSpan?.node.marks ?? []).filter((mark) =>\n          decorators.includes(mark),\n        ),\n      }\n    }\n  }\n\n  return {\n    state: 'unchanged',\n    marks: focusSpan.node.marks ?? [],\n  }\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {getBlockKeyFromSelectionPoint} from '../selection/selection-point'\nimport type {BlockPath} from '../types/paths'\nimport {getSelectionEndPoint} from '../utils/util.get-selection-end-point'\nimport {getSelectionStartPoint} from '../utils/util.get-selection-start-point'\n\n/**\n * @public\n */\nexport const getSelectedBlocks: EditorSelector<\n  Array<{node: PortableTextBlock; path: BlockPath}>\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return []\n  }\n\n  const selectedBlocks: Array<{node: PortableTextBlock; path: BlockPath}> = []\n  const startPoint = getSelectionStartPoint(snapshot.context.selection)\n  const endPoint = getSelectionEndPoint(snapshot.context.selection)\n  const startKey = getBlockKeyFromSelectionPoint(startPoint)\n  const endKey = getBlockKeyFromSelectionPoint(endPoint)\n\n  if (!startKey || !endKey) {\n    return selectedBlocks\n  }\n\n  const startBlockIndex = snapshot.blockIndexMap.get(startKey)\n  const endBlockIndex = snapshot.blockIndexMap.get(endKey)\n\n  if (startBlockIndex === undefined || endBlockIndex === undefined) {\n    return selectedBlocks\n  }\n\n  const slicedValue = snapshot.context.value.slice(\n    startBlockIndex,\n    endBlockIndex + 1,\n  )\n\n  for (const block of slicedValue) {\n    if (block._key === startKey) {\n      selectedBlocks.push({node: block, path: [{_key: block._key}]})\n\n      if (startKey === endKey) {\n        break\n      }\n      continue\n    }\n\n    if (block._key === endKey) {\n      selectedBlocks.push({node: block, path: [{_key: block._key}]})\n      break\n    }\n\n    if (selectedBlocks.length > 0) {\n      selectedBlocks.push({node: block, path: [{_key: block._key}]})\n    }\n  }\n\n  return selectedBlocks\n}\n", "import type {PortableTextObject} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport {getMarkState} from './selector.get-mark-state'\nimport {getSelectedBlocks} from './selector.get-selected-blocks'\n\n/**\n * @public\n */\nexport const getActiveAnnotations: EditorSelector<Array<PortableTextObject>> = (\n  snapshot,\n) => {\n  if (!snapshot.context.selection) {\n    return []\n  }\n\n  const selectedBlocks = getSelectedBlocks(snapshot)\n  const markState = getMarkState(snapshot)\n\n  const activeAnnotations = (markState?.marks ?? []).filter(\n    (mark) =>\n      !snapshot.context.schema.decorators\n        .map((decorator) => decorator.name)\n        .includes(mark),\n  )\n\n  const selectionMarkDefs = selectedBlocks.flatMap((block) =>\n    isTextBlock(snapshot.context, block.node)\n      ? (block.node.markDefs ?? [])\n      : [],\n  )\n\n  return selectionMarkDefs.filter((markDef) =>\n    activeAnnotations.includes(markDef._key),\n  )\n}\n", "import type {PortableTextListBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport {getSelectedBlocks} from './selector.get-selected-blocks'\n\n/**\n * @public\n */\nexport const getActiveListItem: EditorSelector<\n  PortableTextListBlock['listItem'] | undefined\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  const selectedBlocks = getSelectedBlocks(snapshot).map((block) => block.node)\n  const selectedTextBlocks = selectedBlocks.filter((block) =>\n    isTextBlock(snapshot.context, block),\n  )\n\n  const firstTextBlock = selectedTextBlocks.at(0)\n\n  if (!firstTextBlock) {\n    return undefined\n  }\n\n  const firstListItem = firstTextBlock.listItem\n\n  if (!firstListItem) {\n    return undefined\n  }\n\n  if (selectedTextBlocks.every((block) => block.listItem === firstListItem)) {\n    return firstListItem\n  }\n\n  return undefined\n}\n", "import type {PortableTextTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport {getSelectedBlocks} from './selector.get-selected-blocks'\n\n/**\n * @public\n */\nexport const getActiveStyle: EditorSelector<PortableTextTextBlock['style']> = (\n  snapshot,\n) => {\n  if (!snapshot.context.selection) {\n    return undefined\n  }\n\n  const selectedBlocks = getSelectedBlocks(snapshot).map((block) => block.node)\n  const selectedTextBlocks = selectedBlocks.filter((block) =>\n    isTextBlock(snapshot.context, block),\n  )\n\n  const firstTextBlock = selectedTextBlocks.at(0)\n\n  if (!firstTextBlock) {\n    return undefined\n  }\n\n  const firstStyle = firstTextBlock.style\n\n  if (!firstStyle) {\n    return undefined\n  }\n\n  if (selectedTextBlocks.every((block) => block.style === firstStyle)) {\n    return firstStyle\n  }\n\n  return undefined\n}\n", "import {isKeySegment, type PortableTextObject} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {ChildPath} from '../types/paths'\nimport {isSpan} from '../utils'\nimport {getFocusTextBlock} from './selector.get-focus-text-block'\nimport {getSelectionEndPoint} from './selector.get-selection-end-point'\n\n/**\n * @public\n */\nexport const getNextInlineObject: EditorSelector<\n  | {\n      node: PortableTextObject\n      path: ChildPath\n    }\n  | undefined\n> = (snapshot) => {\n  const focusTextBlock = getFocusTextBlock(snapshot)\n  const selectionEndPoint = getSelectionEndPoint(snapshot)\n  const selectionEndPointChildKey =\n    selectionEndPoint && isKeySegment(selectionEndPoint.path[2])\n      ? selectionEndPoint.path[2]._key\n      : undefined\n\n  if (!focusTextBlock || !selectionEndPointChildKey) {\n    return undefined\n  }\n\n  let endPointChildFound = false\n  let inlineObject:\n    | {\n        node: PortableTextObject\n        path: ChildPath\n      }\n    | undefined\n\n  for (const child of focusTextBlock.node.children) {\n    if (child._key === selectionEndPointChildKey) {\n      endPointChildFound = true\n      continue\n    }\n\n    if (!isSpan(snapshot.context, child) && endPointChildFound) {\n      inlineObject = {\n        node: child,\n        path: [...focusTextBlock.path, 'children', {_key: child._key}],\n      }\n      break\n    }\n  }\n\n  return inlineObject\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockOffset} from '../types/block-offset'\nimport type {EditorSelection} from '../types/editor'\nimport {\n  blockOffsetToSpanSelectionPoint,\n  spanSelectionPointToBlockOffset,\n} from '../utils/util.block-offset'\nimport {getBlockEndPoint} from '../utils/util.get-block-end-point'\nimport {getBlockStartPoint} from '../utils/util.get-block-start-point'\nimport {getFocusTextBlock} from './selector.get-focus-text-block'\nimport {getNextInlineObject} from './selector.get-next-inline-object'\nimport {getPreviousInlineObject} from './selector.get-previous-inline-object'\nimport {getSelectionStartPoint} from './selector.get-selection-start-point'\nimport {getSelectionText} from './selector.get-selection-text'\nimport {isSelectionCollapsed} from './selector.is-selection-collapsed'\nimport {isSelectionExpanded} from './selector.is-selection-expanded'\n\n/**\n * @public\n * Returns the selection of the of the word the caret is placed in.\n * Note: Only returns a word selection if the current selection is collapsed\n */\nexport const getCaretWordSelection: EditorSelector<EditorSelection> = (\n  snapshot,\n) => {\n  if (!snapshot.context.selection) {\n    return null\n  }\n\n  if (!isSelectionCollapsed(snapshot)) {\n    return null\n  }\n\n  const focusTextBlock = getFocusTextBlock(snapshot)\n  const selectionStartPoint = getSelectionStartPoint(snapshot)\n  const selectionStartOffset = selectionStartPoint\n    ? spanSelectionPointToBlockOffset({\n        context: snapshot.context,\n        selectionPoint: selectionStartPoint,\n      })\n    : undefined\n\n  if (!focusTextBlock || !selectionStartPoint || !selectionStartOffset) {\n    return null\n  }\n\n  const previousInlineObject = getPreviousInlineObject(snapshot)\n  const blockStartPoint = getBlockStartPoint({\n    context: snapshot.context,\n    block: focusTextBlock,\n  })\n  const textBefore = getSelectionText({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: {\n        anchor: previousInlineObject\n          ? {path: previousInlineObject.path, offset: 0}\n          : blockStartPoint,\n        focus: selectionStartPoint,\n      },\n    },\n  })\n  const textDirectlyBefore = textBefore.split(/\\s+/).at(-1)\n\n  const nextInlineObject = getNextInlineObject(snapshot)\n  const blockEndPoint = getBlockEndPoint({\n    context: snapshot.context,\n    block: focusTextBlock,\n  })\n  const textAfter = getSelectionText({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: {\n        anchor: selectionStartPoint,\n        focus: nextInlineObject\n          ? {path: nextInlineObject.path, offset: 0}\n          : blockEndPoint,\n      },\n    },\n  })\n  const textDirectlyAfter = textAfter.split(/\\s+/).at(0)\n\n  if (\n    (textDirectlyBefore === undefined || textDirectlyBefore === '') &&\n    (textDirectlyAfter === undefined || textDirectlyAfter === '')\n  ) {\n    return null\n  }\n\n  const caretWordStartOffset: BlockOffset = textDirectlyBefore\n    ? {\n        ...selectionStartOffset,\n        offset: selectionStartOffset.offset - textDirectlyBefore.length,\n      }\n    : selectionStartOffset\n  const caretWordEndOffset: BlockOffset = textDirectlyAfter\n    ? {\n        ...selectionStartOffset,\n        offset: selectionStartOffset.offset + textDirectlyAfter.length,\n      }\n    : selectionStartOffset\n\n  const caretWordStartSelectionPoint = blockOffsetToSpanSelectionPoint({\n    context: snapshot.context,\n    blockOffset: caretWordStartOffset,\n    direction: 'backward',\n  })\n  const caretWordEndSelectionPoint = blockOffsetToSpanSelectionPoint({\n    context: snapshot.context,\n    blockOffset: caretWordEndOffset,\n    direction: 'forward',\n  })\n\n  if (!caretWordStartSelectionPoint || !caretWordEndSelectionPoint) {\n    return null\n  }\n\n  const caretWordSelection = {\n    anchor: caretWordStartSelectionPoint,\n    focus: caretWordEndSelectionPoint,\n  }\n\n  return isSelectionExpanded({\n    ...snapshot,\n    context: {\n      ...snapshot.context,\n      selection: caretWordSelection,\n    },\n  })\n    ? caretWordSelection\n    : null\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockPath} from '../types/paths'\n\n/**\n * @public\n */\nexport const getFirstBlock: EditorSelector<\n  {node: PortableTextBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  const node = snapshot.context.value[0]\n\n  return node ? {node, path: [{_key: node._key}]} : undefined\n}\n", "import type {PortableTextObject} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport type {BlockPath} from '../types/paths'\nimport {getFocusBlock} from './selector.get-focus-block'\n\n/**\n * @public\n */\nexport const getFocusBlockObject: EditorSelector<\n  {node: PortableTextObject; path: BlockPath} | undefined\n> = (snapshot) => {\n  const focusBlock = getFocusBlock(snapshot)\n\n  return focusBlock && !isTextBlock(snapshot.context, focusBlock.node)\n    ? {node: focusBlock.node, path: focusBlock.path}\n    : undefined\n}\n", "import {isPortableTextSpan, type PortableTextObject} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {ChildPath} from '../types/paths'\nimport {getFocusChild} from './selector.get-focus-child'\n\n/**\n * @public\n */\nexport const getFocusInlineObject: EditorSelector<\n  {node: PortableTextObject; path: ChildPath} | undefined\n> = (snapshot) => {\n  const focusChild = getFocusChild(snapshot)\n\n  return focusChild && !isPortableTextSpan(focusChild.node)\n    ? {node: focusChild.node, path: focusChild.path}\n    : undefined\n}\n", "import type {PortableTextListBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isListBlock} from '../internal-utils/parse-blocks'\nimport type {BlockPath} from '../types/paths'\nimport {getFocusTextBlock} from './selector.get-focus-text-block'\n\n/**\n * @public\n */\nexport const getFocusListBlock: EditorSelector<\n  {node: PortableTextListBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  const focusTextBlock = getFocusTextBlock(snapshot)\n\n  return focusTextBlock && isListBlock(snapshot.context, focusTextBlock.node)\n    ? {node: focusTextBlock.node, path: focusTextBlock.path}\n    : undefined\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockPath} from '../types/paths'\n\n/**\n * @public\n */\nexport const getLastBlock: EditorSelector<\n  {node: PortableTextBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  const node = snapshot.context.value[snapshot.context.value.length - 1]\n    ? snapshot.context.value[snapshot.context.value.length - 1]\n    : undefined\n\n  return node ? {node, path: [{_key: node._key}]} : undefined\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockPath} from '../types/paths'\nimport {getSelectionEndBlock} from './selector.get-selection-end-block'\n\n/**\n * @public\n */\nexport const getNextBlock: EditorSelector<\n  {node: PortableTextBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  const selectionEndBlock = getSelectionEndBlock(snapshot)\n\n  if (!selectionEndBlock) {\n    return undefined\n  }\n\n  const index = snapshot.blockIndexMap.get(selectionEndBlock.node._key)\n\n  if (index === undefined || index === snapshot.context.value.length - 1) {\n    return undefined\n  }\n\n  const nextBlock = snapshot.context.value.at(index + 1)\n\n  return nextBlock\n    ? {node: nextBlock, path: [{_key: nextBlock._key}]}\n    : undefined\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockPath} from '../types/paths'\nimport {getSelectionStartBlock} from './selector.get-selection-start-block'\n\n/**\n * @public\n */\nexport const getPreviousBlock: EditorSelector<\n  {node: PortableTextBlock; path: BlockPath} | undefined\n> = (snapshot) => {\n  const selectionStartBlock = getSelectionStartBlock(snapshot)\n\n  if (!selectionStartBlock) {\n    return undefined\n  }\n\n  const index = snapshot.blockIndexMap.get(selectionStartBlock.node._key)\n\n  if (index === undefined || index === 0) {\n    return undefined\n  }\n\n  const previousBlock = snapshot.context.value.at(index - 1)\n\n  return previousBlock\n    ? {node: previousBlock, path: [{_key: previousBlock._key}]}\n    : undefined\n}\n", "import type {PortableTextTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport {getBlockKeyFromSelectionPoint} from '../selection/selection-point'\nimport type {BlockPath} from '../types/paths'\nimport {getSelectionEndPoint, getSelectionStartPoint} from '../utils'\n\n/**\n * @public\n */\nexport const getSelectedTextBlocks: EditorSelector<\n  Array<{node: PortableTextTextBlock; path: BlockPath}>\n> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return []\n  }\n\n  const selectedTextBlocks: Array<{\n    node: PortableTextTextBlock\n    path: BlockPath\n  }> = []\n\n  const startPoint = getSelectionStartPoint(snapshot.context.selection)\n  const endPoint = getSelectionEndPoint(snapshot.context.selection)\n  const startBlockKey = getBlockKeyFromSelectionPoint(startPoint)\n  const endBlockKey = getBlockKeyFromSelectionPoint(endPoint)\n\n  if (!startBlockKey || !endBlockKey) {\n    return selectedTextBlocks\n  }\n\n  const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey)\n  const endBlockIndex = snapshot.blockIndexMap.get(endBlockKey)\n\n  if (startBlockIndex === undefined || endBlockIndex === undefined) {\n    return selectedTextBlocks\n  }\n\n  const slicedValue = snapshot.context.value.slice(\n    startBlockIndex,\n    endBlockIndex + 1,\n  )\n\n  for (const block of slicedValue) {\n    if (block._key === startBlockKey) {\n      if (isTextBlock(snapshot.context, block)) {\n        selectedTextBlocks.push({node: block, path: [{_key: block._key}]})\n      }\n\n      if (startBlockKey === endBlockKey) {\n        break\n      }\n      continue\n    }\n\n    if (block._key === endBlockKey) {\n      if (isTextBlock(snapshot.context, block)) {\n        selectedTextBlocks.push({node: block, path: [{_key: block._key}]})\n      }\n\n      break\n    }\n\n    if (selectedTextBlocks.length > 0) {\n      if (isTextBlock(snapshot.context, block)) {\n        selectedTextBlocks.push({node: block, path: [{_key: block._key}]})\n      }\n    }\n  }\n\n  return selectedTextBlocks\n}\n", "import type {PortableTextSpan} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport {isSpan, isTextBlock} from '../internal-utils/parse-blocks'\nimport {\n  getBlockKeyFromSelectionPoint,\n  getChildKeyFromSelectionPoint,\n} from '../selection/selection-point'\nimport type {EditorSelection, EditorSelectionPoint} from '../types/editor'\nimport {\n  getSelectionEndPoint,\n  getSelectionStartPoint,\n  isEmptyTextBlock,\n} from '../utils'\nimport {getFocusTextBlock} from './selector.get-focus-text-block'\nimport {isSelectionCollapsed} from './selector.is-selection-collapsed'\n\n/**\n * @public\n */\nexport const getTrimmedSelection: EditorSelector<EditorSelection> = (\n  snapshot,\n) => {\n  if (!snapshot.context.selection) {\n    return snapshot.context.selection\n  }\n\n  const startPoint = getSelectionStartPoint(snapshot.context.selection)\n  const endPoint = getSelectionEndPoint(snapshot.context.selection)\n\n  const startBlockKey = getBlockKeyFromSelectionPoint(startPoint)\n  const startChildKey = getChildKeyFromSelectionPoint(startPoint)\n  const endBlockKey = getBlockKeyFromSelectionPoint(endPoint)\n  const endChildKey = getChildKeyFromSelectionPoint(endPoint)\n\n  if (!startBlockKey || !endBlockKey) {\n    return snapshot.context.selection\n  }\n\n  const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey)\n  const endBlockIndex = snapshot.blockIndexMap.get(endBlockKey)\n\n  if (startBlockIndex === undefined || endBlockIndex === undefined) {\n    return snapshot.context.selection\n  }\n\n  const slicedValue = snapshot.context.value.slice(\n    startBlockIndex,\n    endBlockIndex + 1,\n  )\n\n  let startBlockFound = false\n  let adjustedStartPoint: EditorSelectionPoint | undefined\n  let trimStartPoint = false\n  let adjustedEndPoint: EditorSelectionPoint | undefined\n  let trimEndPoint = false\n  let previousPotentialEndpoint:\n    | {blockKey: string; span: PortableTextSpan}\n    | undefined\n\n  for (const block of slicedValue) {\n    if (block._key === startBlockKey) {\n      startBlockFound = true\n\n      if (\n        isTextBlock(snapshot.context, block) &&\n        isEmptyTextBlock(snapshot.context, block)\n      ) {\n        continue\n      }\n    }\n\n    if (!startBlockFound) {\n      continue\n    }\n\n    if (!isTextBlock(snapshot.context, block)) {\n      continue\n    }\n\n    if (\n      block._key === endBlockKey &&\n      isEmptyTextBlock(snapshot.context, block)\n    ) {\n      break\n    }\n\n    for (const child of block.children) {\n      if (child._key === endChildKey) {\n        if (!isSpan(snapshot.context, child) || endPoint.offset === 0) {\n          adjustedEndPoint = previousPotentialEndpoint\n            ? {\n                path: [\n                  {_key: previousPotentialEndpoint.blockKey},\n                  'children',\n                  {_key: previousPotentialEndpoint.span._key},\n                ],\n                offset: previousPotentialEndpoint.span.text.length,\n              }\n            : undefined\n\n          trimEndPoint = true\n          break\n        }\n      }\n\n      if (trimStartPoint) {\n        const lonelySpan =\n          isSpan(snapshot.context, child) && block.children.length === 1\n\n        if (\n          (isSpan(snapshot.context, child) && child.text.length > 0) ||\n          lonelySpan\n        ) {\n          adjustedStartPoint = {\n            path: [{_key: block._key}, 'children', {_key: child._key}],\n            offset: 0,\n          }\n          previousPotentialEndpoint = {blockKey: block._key, span: child}\n          trimStartPoint = false\n        }\n\n        continue\n      }\n\n      if (child._key === startChildKey) {\n        if (!isSpan(snapshot.context, child)) {\n          trimStartPoint = true\n          continue\n        }\n\n        if (startPoint.offset === child.text.length) {\n          trimStartPoint = true\n          previousPotentialEndpoint =\n            child.text.length > 0\n              ? {blockKey: block._key, span: child}\n              : previousPotentialEndpoint\n          continue\n        }\n      }\n\n      previousPotentialEndpoint =\n        isSpan(snapshot.context, child) && child.text.length > 0\n          ? {blockKey: block._key, span: child}\n          : previousPotentialEndpoint\n    }\n\n    if (block._key === endBlockKey) {\n      break\n    }\n  }\n\n  const trimmedSelection = snapshot.context.selection.backward\n    ? {\n        anchor: trimEndPoint && adjustedEndPoint ? adjustedEndPoint : endPoint,\n        focus: adjustedStartPoint ?? startPoint,\n        backward: true,\n      }\n    : {\n        anchor: adjustedStartPoint ?? startPoint,\n        focus: trimEndPoint && adjustedEndPoint ? adjustedEndPoint : endPoint,\n      }\n\n  if (\n    isSelectionCollapsed({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection: trimmedSelection,\n      },\n    })\n  ) {\n    const focusTextBlock = getFocusTextBlock({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection: trimmedSelection,\n      },\n    })\n\n    if (\n      focusTextBlock &&\n      !isEmptyTextBlock(snapshot.context, focusTextBlock.node)\n    ) {\n      return null\n    }\n  }\n\n  return trimmedSelection\n}\n", "import type {EditorSnapshot} from '../editor/editor-snapshot'\nimport {getMarkState} from './selector.get-mark-state'\n\nexport function getActiveAnnotationsMarks(snapshot: EditorSnapshot) {\n  const schema = snapshot.context.schema\n  const markState = getMarkState(snapshot)\n\n  return (markState?.marks ?? []).filter(\n    (mark) =>\n      !schema.decorators.map((decorator) => decorator.name).includes(mark),\n  )\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport {getActiveAnnotationsMarks} from './selector.get-active-annotation-marks'\nimport {getSelectedBlocks} from './selector.get-selected-blocks'\n\n/**\n * @public\n */\nexport function isActiveAnnotation(\n  annotation: string,\n): EditorSelector<boolean> {\n  return (snapshot) => {\n    const selectedBlocks = getSelectedBlocks(snapshot)\n    const selectionMarkDefs = selectedBlocks.flatMap((block) =>\n      isTextBlock(snapshot.context, block.node)\n        ? (block.node.markDefs ?? [])\n        : [],\n    )\n    const activeAnnotations = getActiveAnnotationsMarks(snapshot)\n    const activeMarkDefs = selectionMarkDefs.filter(\n      (markDef) =>\n        markDef._type === annotation &&\n        activeAnnotations.includes(markDef._key),\n    )\n\n    return activeMarkDefs.length > 0\n  }\n}\n", "import type {EditorSnapshot} from '../editor/editor-snapshot'\nimport {getMarkState} from './selector.get-mark-state'\n\nexport function getActiveDecorators(snapshot: EditorSnapshot) {\n  const schema = snapshot.context.schema\n  const decoratorState = snapshot.decoratorState\n  const markState = getMarkState(snapshot)\n  const decorators = schema.decorators.map((decorator) => decorator.name)\n\n  const markStateDecorators = (markState?.marks ?? []).filter((mark) =>\n    decorators.includes(mark),\n  )\n\n  let activeDecorators: Array<string> = markStateDecorators\n\n  for (const decorator in decoratorState) {\n    if (decoratorState[decorator] === false) {\n      activeDecorators = activeDecorators.filter(\n        (activeDecorator) => activeDecorator !== decorator,\n      )\n    } else if (decoratorState[decorator] === true) {\n      if (!activeDecorators.includes(decorator)) {\n        activeDecorators.push(decorator)\n      }\n    }\n  }\n\n  return activeDecorators\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {getActiveDecorators} from './selector.get-active-decorators'\nimport {getSelectedSpans} from './selector.get-selected-spans'\nimport {isSelectionExpanded} from './selector.is-selection-expanded'\n\n/**\n * @public\n */\nexport function isActiveDecorator(decorator: string): EditorSelector<boolean> {\n  return (snapshot) => {\n    if (isSelectionExpanded(snapshot)) {\n      const selectedSpans = getSelectedSpans(snapshot)\n\n      return (\n        selectedSpans.length > 0 &&\n        selectedSpans.every((span) => span.node.marks?.includes(decorator))\n      )\n    }\n\n    const activeDecorators = getActiveDecorators(snapshot)\n\n    return activeDecorators.includes(decorator)\n  }\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {getActiveListItem} from './selector.get-active-list-item'\n\n/**\n * @public\n */\nexport function isActiveListItem(listItem: string): EditorSelector<boolean> {\n  return (snapshot) => {\n    const activeListItem = getActiveListItem(snapshot)\n\n    return activeListItem === listItem\n  }\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {getActiveStyle} from './selector.get-active-style'\n\n/**\n * @public\n */\nexport function isActiveStyle(style: string): EditorSelector<boolean> {\n  return (snapshot) => {\n    const activeStyle = getActiveStyle(snapshot)\n\n    return activeStyle === style\n  }\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockPath} from '../types/paths'\nimport * as utils from '../utils'\nimport {isSelectionCollapsed} from './selector.is-selection-collapsed'\n\n/**\n * @public\n */\nexport function isAtTheEndOfBlock(block: {\n  node: PortableTextBlock\n  path: BlockPath\n}): EditorSelector<boolean> {\n  return (snapshot) => {\n    if (!snapshot.context.selection || !isSelectionCollapsed(snapshot)) {\n      return false\n    }\n\n    const blockEndPoint = utils.getBlockEndPoint({\n      context: snapshot.context,\n      block,\n    })\n\n    return utils.isEqualSelectionPoints(\n      snapshot.context.selection.focus,\n      blockEndPoint,\n    )\n  }\n}\n", "import type {PortableTextBlock} from '@sanity/types'\nimport type {EditorSelector} from '../editor/editor-selector'\nimport type {BlockPath} from '../types/paths'\nimport * as utils from '../utils'\nimport {isSelectionCollapsed} from './selector.is-selection-collapsed'\n\n/**\n * @public\n */\nexport function isAtTheStartOfBlock(block: {\n  node: PortableTextBlock\n  path: BlockPath\n}): EditorSelector<boolean> {\n  return (snapshot) => {\n    if (!snapshot.context.selection || !isSelectionCollapsed(snapshot)) {\n      return false\n    }\n\n    const blockStartPoint = utils.getBlockStartPoint({\n      context: snapshot.context,\n      block,\n    })\n\n    return utils.isEqualSelectionPoints(\n      snapshot.context.selection.focus,\n      blockStartPoint,\n    )\n  }\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport {\n  getBlockKeyFromSelectionPoint,\n  getChildKeyFromSelectionPoint,\n} from '../selection/selection-point'\nimport type {EditorSelectionPoint} from '../types/editor'\nimport {getSelectionEndPoint} from '../utils'\n\n/**\n * @public\n */\nexport function isPointAfterSelection(\n  point: EditorSelectionPoint,\n): EditorSelector<boolean> {\n  return (snapshot) => {\n    if (!snapshot.context.selection) {\n      return false\n    }\n\n    const endPoint = getSelectionEndPoint(snapshot.context.selection)\n    const endBlockKey = getBlockKeyFromSelectionPoint(endPoint)\n    const endChildKey = getChildKeyFromSelectionPoint(endPoint)\n\n    const pointBlockKey = getBlockKeyFromSelectionPoint(point)\n    const pointChildKey = getChildKeyFromSelectionPoint(point)\n\n    if (!pointBlockKey || !endBlockKey) {\n      return false\n    }\n\n    const pointBlockIndex = snapshot.blockIndexMap.get(pointBlockKey)\n    const endBlockIndex = snapshot.blockIndexMap.get(endBlockKey)\n\n    if (pointBlockIndex === undefined || endBlockIndex === undefined) {\n      return false\n    }\n\n    if (pointBlockIndex > endBlockIndex) {\n      // The point block is after the end block.\n      return true\n    }\n\n    if (pointBlockIndex < endBlockIndex) {\n      // The point block is before the end block.\n      return false\n    }\n\n    // The point block is the same as the end block.\n    const pointBlock = snapshot.context.value.at(pointBlockIndex)\n\n    if (!pointBlock) {\n      // The point block is not in the value.\n      return false\n    }\n\n    if (!isTextBlock(snapshot.context, pointBlock)) {\n      // The point block is not a text block.\n      // Since the point block is the same as the end block, the point is not\n      // after the selection.\n      return false\n    }\n\n    let pointChildIndex: number | undefined\n    let endChildIndex: number | undefined\n\n    let childIndex = -1\n\n    // The point block is the same as the end block, so we need to find the\n    // child indices and compare them.\n    for (const child of pointBlock.children) {\n      childIndex++\n\n      if (child._key === pointChildKey && child._key === endChildKey) {\n        return point.offset > endPoint.offset\n      }\n\n      if (child._key === pointChildKey) {\n        pointChildIndex = childIndex\n      }\n\n      if (child._key === endChildKey) {\n        endChildIndex = childIndex\n      }\n\n      if (pointChildIndex !== undefined && endChildIndex !== undefined) {\n        break\n      }\n    }\n\n    if (pointChildIndex === undefined || endChildIndex === undefined) {\n      return false\n    }\n\n    return pointChildIndex > endChildIndex\n  }\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport {isTextBlock} from '../internal-utils/parse-blocks'\nimport {\n  getBlockKeyFromSelectionPoint,\n  getChildKeyFromSelectionPoint,\n} from '../selection/selection-point'\nimport type {EditorSelectionPoint} from '../types/editor'\nimport {getSelectionStartPoint} from '../utils'\n\n/**\n * @public\n */\nexport function isPointBeforeSelection(\n  point: EditorSelectionPoint,\n): EditorSelector<boolean> {\n  return (snapshot) => {\n    if (!snapshot.context.selection) {\n      return false\n    }\n\n    const startPoint = getSelectionStartPoint(snapshot.context.selection)\n    const startBlockKey = getBlockKeyFromSelectionPoint(startPoint)\n    const startChildKey = getChildKeyFromSelectionPoint(startPoint)\n\n    const pointBlockKey = getBlockKeyFromSelectionPoint(point)\n    const pointChildKey = getChildKeyFromSelectionPoint(point)\n\n    if (!pointBlockKey || !startBlockKey) {\n      return false\n    }\n\n    const startBlockIndex = snapshot.blockIndexMap.get(startBlockKey)\n    const pointBlockIndex = snapshot.blockIndexMap.get(pointBlockKey)\n\n    if (startBlockIndex === undefined || pointBlockIndex === undefined) {\n      return false\n    }\n\n    if (pointBlockIndex < startBlockIndex) {\n      // The point block is before the start block.\n      return true\n    }\n\n    if (pointBlockIndex > startBlockIndex) {\n      // The point block is after the start block.\n      return false\n    }\n\n    // The point block is the same as the start block.\n    const pointBlock = snapshot.context.value.at(pointBlockIndex)\n\n    if (!pointBlock) {\n      // The point block is not in the value.\n      return false\n    }\n\n    if (!isTextBlock(snapshot.context, pointBlock)) {\n      // The point block is not a text block.\n      // Since the point block is the same as the start block, the point is not\n      // before the selection.\n      return false\n    }\n\n    let pointChildIndex: number | undefined\n    let startChildIndex: number | undefined\n\n    let childIndex = -1\n\n    // The point block is the same as the start block, so we need to find the\n    // child indices and compare them.\n    for (const child of pointBlock.children) {\n      childIndex++\n\n      if (child._key === pointChildKey && child._key === startChildKey) {\n        return point.offset < startPoint.offset\n      }\n\n      if (child._key === pointChildKey) {\n        pointChildIndex = childIndex\n      }\n\n      if (child._key === startChildKey) {\n        startChildIndex = childIndex\n      }\n\n      if (pointChildIndex !== undefined && startChildIndex !== undefined) {\n        break\n      }\n    }\n\n    if (pointChildIndex === undefined || startChildIndex === undefined) {\n      return false\n    }\n\n    return pointChildIndex < startChildIndex\n  }\n}\n", "import type {EditorSelection} from '../types/editor'\nimport {isEqualSelectionPoints} from '../utils'\nimport type {EditorSelector} from './../editor/editor-selector'\nimport {getSelectionEndPoint} from './selector.get-selection-end-point'\nimport {getSelectionStartPoint} from './selector.get-selection-start-point'\nimport {isPointAfterSelection} from './selector.is-point-after-selection'\nimport {isPointBeforeSelection} from './selector.is-point-before-selection'\n\n/**\n * @public\n */\nexport function isOverlappingSelection(\n  selection: EditorSelection,\n): EditorSelector<boolean> {\n  return (snapshot) => {\n    if (!selection || !snapshot.context.selection) {\n      return false\n    }\n\n    const selectionStartPoint = getSelectionStartPoint({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection,\n      },\n    })\n    const selectionEndPoint = getSelectionEndPoint({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection,\n      },\n    })\n\n    const originalSelectionStartPoint = getSelectionStartPoint(snapshot)\n    const originalSelectionEndPoint = getSelectionEndPoint(snapshot)\n\n    if (\n      !selectionStartPoint ||\n      !selectionEndPoint ||\n      !originalSelectionStartPoint ||\n      !originalSelectionEndPoint\n    ) {\n      return false\n    }\n\n    const startPointBeforeSelection =\n      isPointBeforeSelection(selectionStartPoint)(snapshot)\n    const startPointAfterSelection =\n      isPointAfterSelection(selectionStartPoint)(snapshot)\n    const endPointBeforeSelection =\n      isPointBeforeSelection(selectionEndPoint)(snapshot)\n    const endPointAfterSelection =\n      isPointAfterSelection(selectionEndPoint)(snapshot)\n\n    const originalStartPointBeforeStartPoint = isPointBeforeSelection(\n      originalSelectionStartPoint,\n    )({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection: {\n          anchor: selectionStartPoint,\n          focus: selectionStartPoint,\n        },\n      },\n    })\n    const originalStartPointAfterStartPoint = isPointAfterSelection(\n      originalSelectionStartPoint,\n    )({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection: {\n          anchor: selectionStartPoint,\n          focus: selectionStartPoint,\n        },\n      },\n    })\n\n    const originalEndPointBeforeEndPoint = isPointBeforeSelection(\n      originalSelectionEndPoint,\n    )({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection: {\n          anchor: selectionEndPoint,\n          focus: selectionEndPoint,\n        },\n      },\n    })\n    const originalEndPointAfterEndPoint = isPointAfterSelection(\n      originalSelectionEndPoint,\n    )({\n      ...snapshot,\n      context: {\n        ...snapshot.context,\n        selection: {\n          anchor: selectionEndPoint,\n          focus: selectionEndPoint,\n        },\n      },\n    })\n\n    const endPointEqualToOriginalStartPoint = isEqualSelectionPoints(\n      selectionEndPoint,\n      originalSelectionStartPoint,\n    )\n    const startPointEqualToOriginalEndPoint = isEqualSelectionPoints(\n      selectionStartPoint,\n      originalSelectionEndPoint,\n    )\n\n    if (endPointBeforeSelection && !endPointEqualToOriginalStartPoint) {\n      return false\n    }\n\n    if (startPointAfterSelection && !startPointEqualToOriginalEndPoint) {\n      return false\n    }\n\n    if (\n      !originalStartPointBeforeStartPoint &&\n      originalStartPointAfterStartPoint &&\n      !originalEndPointBeforeEndPoint &&\n      originalEndPointAfterEndPoint\n    ) {\n      return !endPointEqualToOriginalStartPoint\n    }\n\n    if (\n      originalStartPointBeforeStartPoint &&\n      !originalStartPointAfterStartPoint &&\n      originalEndPointBeforeEndPoint &&\n      !originalEndPointAfterEndPoint\n    ) {\n      return !startPointEqualToOriginalEndPoint\n    }\n\n    if (\n      !startPointAfterSelection ||\n      !startPointBeforeSelection ||\n      !endPointAfterSelection ||\n      !endPointBeforeSelection\n    ) {\n      return true\n    }\n\n    return false\n  }\n}\n", "import type {EditorSelector} from '../editor/editor-selector'\nimport * as utils from '../utils'\nimport {getSelectionEndBlock} from './selector.get-selection-end-block'\nimport {getSelectionStartBlock} from './selector.get-selection-start-block'\n\n/**\n * @public\n */\nexport const isSelectingEntireBlocks: EditorSelector<boolean> = (snapshot) => {\n  if (!snapshot.context.selection) {\n    return false\n  }\n\n  const startPoint = snapshot.context.selection.backward\n    ? snapshot.context.selection.focus\n    : snapshot.context.selection.anchor\n  const endPoint = snapshot.context.selection.backward\n    ? snapshot.context.selection.anchor\n    : snapshot.context.selection.focus\n\n  const startBlock = getSelectionStartBlock(snapshot)\n  const endBlock = getSelectionEndBlock(snapshot)\n\n  if (!startBlock || !endBlock) {\n    return false\n  }\n\n  const startBlockStartPoint = utils.getBlockStartPoint({\n    context: snapshot.context,\n    block: startBlock,\n  })\n  const endBlockEndPoint = utils.getBlockEndPoint({\n    context: snapshot.context,\n    block: endBlock,\n  })\n\n  return (\n    utils.isEqualSelectionPoints(startBlockStartPoint, startPoint) &&\n    utils.isEqualSelectionPoints(endBlockEndPoint, endPoint)\n  )\n}\n"], "names": ["isSelectionExpanded", "selection", "isSelectionCollapsed", "getSelectionEndBlock", "snapshot", "endPoint", "getSelectionEndPoint", "context", "getFocusBlock", "anchor", "focus", "backward", "getNextSpan", "selectionEndBlock", "selectionEndPoint", "isTextBlock", "node", "selectionEndPoint<PERSON><PERSON><PERSON><PERSON>ey", "getChildKeyFromSelectionPoint", "endPointChildFound", "nextSpan", "child", "children", "_key", "isSpan", "path", "getSelectionStartBlock", "startPoint", "getSelectionStartPoint", "getPreviousSpan", "selectionStartBlock", "selectionStartPoint", "selectionStartPointC<PERSON>d<PERSON>ey", "previousSpan", "getSelectedSpans", "selectedSpans", "startBlockKey", "getBlockKeyFromSelectionPoint", "endBlockKey", "startSpanKey", "endSpanKey", "startBlockIndex", "blockIndexMap", "get", "endBlockIndex", "undefined", "slicedValue", "value", "slice", "startBlockFound", "block", "offset", "text", "length", "push", "getMarkState", "focusTextBlock", "getFocusTextBlock", "focusSpan", "getFocusSpan", "index", "marks", "span", "filter", "mark", "some", "spanMark", "state", "decorators", "schema", "map", "decorator", "name", "marksWithoutAnnotations", "includes", "spanHasAnnotations", "spanIsEmpty", "atTheBeginningOfSpan", "atTheEndOfSpan", "nextSpanAnnotations", "spanAnnotations", "previousSpanHasAnnotations", "previousSpanHasSameAnnotations", "every", "previousSpanHasSameAnnotation", "previousSpanHasSameMarks", "nextSpanSharesSomeAnnotations", "getSelectedBlocks", "selectedBlocks", "startKey", "<PERSON><PERSON><PERSON>", "getActiveAnnotations", "activeAnnotations", "flatMap", "markDefs", "markDef", "getActiveListItem", "selectedTextBlocks", "firstTextBlock", "at", "firstListItem", "listItem", "getActiveStyle", "firstStyle", "style", "getNextInlineObject", "isKeySegment", "inlineObject", "getCaretWordSelection", "selectionStartOffset", "spanSelectionPointToBlockOffset", "selectionPoint", "previousInlineObject", "getPreviousInlineObject", "blockStartPoint", "getBlockStartPoint", "textDirectlyBefore", "getSelectionText", "split", "nextInlineObject", "blockEndPoint", "getBlockEndPoint", "textDirectlyAfter", "caretWordStartOffset", "caretWordEndOffset", "caretWordStartSelectionPoint", "blockOffsetToSpanSelectionPoint", "blockOffset", "direction", "caretWordEndSelectionPoint", "caretWordSelection", "getFirstBlock", "getFocusBlockObject", "focusBlock", "getFocusInlineObject", "<PERSON><PERSON><PERSON><PERSON>", "getFocusChild", "isPortableTextSpan", "getFocusListBlock", "isListBlock", "getLastBlock", "getNextBlock", "nextBlock", "getPreviousBlock", "previousBlock", "getSelectedTextBlocks", "getTrimmedSelection", "start<PERSON>hild<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adjustedStartPoint", "trimStartPoint", "adjustedEndPoint", "trimEndPoint", "previousPotentialEndpoint", "isEmptyTextBlock", "blockKey", "lonelySpan", "trimmedSelection", "getActiveAnnotationsMarks", "isActiveAnnotation", "annotation", "selectionMarkDefs", "_type", "getActiveDecorators", "decoratorState", "markState", "activeDecorators", "activeDecorator", "isActiveDecorator", "isActiveListItem", "isActiveStyle", "isAtTheEndOfBlock", "utils", "isAtTheStartOfBlock", "isPointAfterSelection", "point", "pointBlockKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointBlockIndex", "pointBlock", "pointChildIndex", "endChildIndex", "childIndex", "isPointBeforeSelection", "startChildIndex", "isOverlappingSelection", "originalSelectionStartPoint", "originalSelectionEndPoint", "startPointBeforeSelection", "startPointAfterSelection", "endPointBeforeSelection", "endPointAfterSelection", "originalStartPointBeforeStartPoint", "originalStartPointAfterStartPoint", "originalEndPointBeforeEndPoint", "originalEndPointAfterEndPoint", "endPointEqualToOriginalStartPoint", "isEqualSelectionPoints", "startPointEqualToOriginalEndPoint", "isSelectingEntireBlocks", "startBlock", "endBlock", "startBlockStartPoint", "endBlockEndPoint"], "mappings": ";;AAMO,SAASA,oBAAoBC,WAA4B;AAC9D,SAAKA,YAIE,CAACC,+CAAqBD,SAAS,IAH7B;AAIX;ACHO,MAAME,uBAMRC,CAAAA,aAAa;AAChB,QAAMC,WAAWC,iBAAAA,qBAAqBF,SAASG,QAAQN,SAAS;AAEhE,MAAKI;AAIL,WAAOG,2CAAc;AAAA,MACnB,GAAGJ;AAAAA,MACHG,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN,WAAW;AAAA,UACTQ,QAAQJ;AAAAA,UACRK,OAAOL;AAAAA,QAAAA;AAAAA,MACT;AAAA,IACF,CACD;AACH,GC1BaC,uBAERF,CAAAA,aAAa;AAChB,MAAKA,SAASG,QAAQN;AAItB,WAAOG,SAASG,QAAQN,UAAUU,WAC9BP,SAASG,QAAQN,UAAUQ,SAC3BL,SAASG,QAAQN,UAAUS;AACjC,GCNaE,cAMRR,CAAAA,aAAa;AAChB,QAAMS,oBAAoBV,qBAAqBC,QAAQ,GACjDU,oBAAoBR,qBAAqBF,QAAQ;AAMvD,MAJI,CAACS,qBAAqB,CAACC,qBAIvB,CAACC,iBAAAA,YAAYX,SAASG,SAASM,kBAAkBG,IAAI;AACvD;AAGF,QAAMC,4BACJC,iBAAAA,8BAA8BJ,iBAAiB;AAEjD,MAAIK,qBAAqB,IACrBC;AAOJ,aAAWC,SAASR,kBAAkBG,KAAKM,UAAU;AACnD,QAAID,MAAME,SAASN,2BAA2B;AAC5CE,2BAAqB;AACrB;AAAA,IACF;AAEA,QAAIK,iBAAAA,OAAOpB,SAASG,SAASc,KAAK,KAAKF,oBAAoB;AACzDC,iBAAW;AAAA,QACTJ,MAAMK;AAAAA,QACNI,MAAM,CAAC,GAAGZ,kBAAkBY,MAAM,YAAY;AAAA,UAACF,MAAMF,MAAME;AAAAA,QAAAA,CAAK;AAAA,MAAA;AAElE;AAAA,IACF;AAAA,EACF;AAEA,SAAOH;AACT,GC9CaM,yBAMRtB,CAAAA,aAAa;AAChB,QAAMuB,aAAaC,iBAAAA,uBAAuBxB,SAASG,QAAQN,SAAS;AAEpE,MAAK0B;AAIL,WAAOnB,2CAAc;AAAA,MACnB,GAAGJ;AAAAA,MACHG,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN,WAAW;AAAA,UACTQ,QAAQkB;AAAAA,UACRjB,OAAOiB;AAAAA,QAAAA;AAAAA,MACT;AAAA,IACF,CACD;AACH,GCtBaE,kBAMRzB,CAAAA,aAAa;AAChB,QAAM0B,sBAAsBJ,uBAAuBtB,QAAQ,GACrD2B,sBAAsBH,6BAAAA,uBAAuBxB,QAAQ;AAM3D,MAJI,CAAC0B,uBAAuB,CAACC,uBAIzB,CAAChB,iBAAAA,YAAYX,SAASG,SAASuB,oBAAoBd,IAAI;AACzD;AAGF,QAAMgB,8BACJd,iBAAAA,8BAA8Ba,mBAAmB;AAEnD,MAAIE;AAOJ,aAAWZ,SAASS,oBAAoBd,KAAKM,UAAU;AACrD,QAAID,MAAME,SAASS;AACjB;AAGER,qBAAAA,OAAOpB,SAASG,SAASc,KAAK,MAChCY,eAAe;AAAA,MACbjB,MAAMK;AAAAA,MACNI,MAAM,CAAC,GAAGK,oBAAoBL,MAAM,YAAY;AAAA,QAACF,MAAMF,MAAME;AAAAA,MAAAA,CAAK;AAAA,IAAA;AAAA,EAGxE;AAEA,SAAOU;AACT,GCtCaC,mBAKR9B,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASG,QAAQN;AACpB,WAAO,CAAA;AAGT,QAAMkC,gBAGD,CAAA,GAECR,aAAaC,6BAAAA,uBAAuBxB,QAAQ,GAC5CC,WAAWC,qBAAqBF,QAAQ;AAE9C,MAAI,CAACuB,cAAc,CAACtB;AAClB,WAAO8B;AAGT,QAAMC,gBAAgBC,iBAAAA,8BAA8BV,UAAU,GACxDW,cAAcD,iBAAAA,8BAA8BhC,QAAQ,GACpDkC,eAAerB,iBAAAA,8BAA8BS,UAAU,GACvDa,aAAatB,iBAAAA,8BAA8Bb,QAAQ;AAEzD,MAAI,CAAC+B,iBAAiB,CAACE;AACrB,WAAOH;AAGT,QAAMM,kBAAkBrC,SAASsC,cAAcC,IAAIP,aAAa,GAC1DQ,gBAAgBxC,SAASsC,cAAcC,IAAIL,WAAW;AAE5D,MAAIG,oBAAoBI,UAAaD,kBAAkBC;AACrD,WAAOV;AAGT,QAAMW,cAAc1C,SAASG,QAAQwC,MAAMC,MACzCP,iBACAG,gBAAgB,CAClB;AAEA,MAAIK,kBAAkB;AAEtB,aAAWC,SAASJ;AAKlB,QAJII,MAAM3B,SAASa,kBACjBa,kBAAkB,KAGhB,EAAClC,6BAAYX,SAASG,SAAS2C,KAAK,GAIxC;AAAA,UAAIA,MAAM3B,SAASa,eAAe;AAChC,mBAAWf,SAAS6B,MAAM5B;AACxB,cAAKE,wBAAOpB,SAASG,SAASc,KAAK,GAInC;AAAA,gBAAIkB,gBAAgBlB,MAAME,SAASgB,cAAc;AAQ/C,kBAPIZ,WAAWwB,SAAS9B,MAAM+B,KAAKC,UACjClB,cAAcmB,KAAK;AAAA,gBACjBtC,MAAMK;AAAAA,gBACNI,MAAM,CAAC;AAAA,kBAACF,MAAM2B,MAAM3B;AAAAA,gBAAAA,GAAO,YAAY;AAAA,kBAACA,MAAMF,MAAME;AAAAA,gBAAAA,CAAK;AAAA,cAAA,CAC1D,GAGCgB,iBAAiBC;AACnB;AAGF;AAAA,YACF;AAEA,gBAAIA,cAAcnB,MAAME,SAASiB,YAAY;AACvCnC,uBAAS8C,SAAS,KACpBhB,cAAcmB,KAAK;AAAA,gBACjBtC,MAAMK;AAAAA,gBACNI,MAAM,CAAC;AAAA,kBAACF,MAAM2B,MAAM3B;AAAAA,gBAAAA,GAAO,YAAY;AAAA,kBAACA,MAAMF,MAAME;AAAAA,gBAAAA,CAAK;AAAA,cAAA,CAC1D;AAEH;AAAA,YACF;AAEIY,0BAAckB,SAAS,KACzBlB,cAAcmB,KAAK;AAAA,cACjBtC,MAAMK;AAAAA,cACNI,MAAM,CAAC;AAAA,gBAACF,MAAM2B,MAAM3B;AAAAA,cAAAA,GAAO,YAAY;AAAA,gBAACA,MAAMF,MAAME;AAAAA,cAAAA,CAAK;AAAA,YAAA,CAC1D;AAAA,UAAA;AAIL,YAAIa,kBAAkBE;AACpB;AAGF;AAAA,MACF;AAEA,UAAIY,MAAM3B,SAASe,aAAa;AAC9B,mBAAWjB,SAAS6B,MAAM5B;AACxB,cAAKE,wBAAOpB,SAASG,SAASc,KAAK,GAInC;AAAA,gBAAImB,cAAcnB,MAAME,SAASiB,YAAY;AACvCnC,uBAAS8C,SAAS,KACpBhB,cAAcmB,KAAK;AAAA,gBACjBtC,MAAMK;AAAAA,gBACNI,MAAM,CAAC;AAAA,kBAACF,MAAM2B,MAAM3B;AAAAA,gBAAAA,GAAO,YAAY;AAAA,kBAACA,MAAMF,MAAME;AAAAA,gBAAAA,CAAK;AAAA,cAAA,CAC1D;AAEH;AAAA,YACF;AAEAY,0BAAcmB,KAAK;AAAA,cACjBtC,MAAMK;AAAAA,cACNI,MAAM,CAAC;AAAA,gBAACF,MAAM2B,MAAM3B;AAAAA,cAAAA,GAAO,YAAY;AAAA,gBAACA,MAAMF,MAAME;AAAAA,cAAAA,CAAK;AAAA,YAAA,CAC1D;AAAA,UAAA;AAGH;AAAA,MACF;AAEA,UAAI0B;AACF,mBAAW5B,SAAS6B,MAAM5B;AACnBE,2BAAAA,OAAOpB,SAASG,SAASc,KAAK,KAInCc,cAAcmB,KAAK;AAAA,YACjBtC,MAAMK;AAAAA,YACNI,MAAM,CAAC;AAAA,cAACF,MAAM2B,MAAM3B;AAAAA,YAAAA,GAAO,YAAY;AAAA,cAACA,MAAMF,MAAME;AAAAA,YAAAA,CAAK;AAAA,UAAA,CAC1D;AAAA,IAAA;AAKP,SAAOY;AACT,GCzIaoB,eACXnD,CAAAA,aACG;AACH,MAAI,CAACA,SAASG,QAAQN;AACpB;AAGF,QAAMuD,iBAAiBC,6BAAAA,kBAAkBrD,QAAQ,GAC3CsD,YAAYC,6BAAAA,aAAavD,QAAQ;AAEvC,MAAI,CAACoD,kBAAkB,CAACE;AACtB;AAGF,MAAI1D,oBAAoBI,SAASG,QAAQN,SAAS,GAAG;AACnD,UAAMkC,gBAAgBD,iBAAiB9B,QAAQ;AAE/C,QAAIwD,QAAQ,GACRC,SAAuB,CAAA;AAE3B,eAAWC,QAAQ3B,eAAe;AAChC,UAAIyB,UAAU;AACZC,iBAAQC,KAAK9C,KAAK6C,SAAS,CAAA;AAAA,WACtB;AACL,YAAIC,KAAK9C,KAAK6C,OAAOR,WAAW,GAAG;AACjCQ,mBAAQ,CAAA;AACR;AAAA,QACF;AAEAA,iBAAQA,OAAME,OAAQC,CAAAA,UACnBF,KAAK9C,KAAK6C,SAAS,CAAA,GAAII,KAAMC,CAAAA,aAAaA,aAAaF,IAAI,CAC9D;AAAA,MACF;AAEAJ;AAAAA,IACF;AAEA,WAAO;AAAA,MACLO,OAAO;AAAA,MACPN,OAAAA;AAAAA,IAAAA;AAAAA,EAEJ;AAEA,QAAMO,aAAahE,SAASG,QAAQ8D,OAAOD,WAAWE,IACnDC,CAAAA,cAAcA,UAAUC,IAC3B,GACMX,QAAQH,UAAU1C,KAAK6C,SAAS,CAAA,GAChCY,0BAA0BZ,MAAME,OAAQC,CAAAA,SAC5CI,WAAWM,SAASV,IAAI,CAC1B,GAEMW,qBAAqBd,MAAMR,SAASoB,wBAAwBpB,QAE5DuB,cAAclB,UAAU1C,KAAKoC,KAAKC,WAAW,GAE7CwB,uBAAuBzE,SAASG,QAAQN,UAAUQ,OAAO0C,WAAW,GACpE2B,iBACJ1E,SAASG,QAAQN,UAAUQ,OAAO0C,WAAWO,UAAU1C,KAAKoC,KAAKC,QAE7DpB,eAAeJ,gBAAgBzB,QAAQ,GACvCgB,WAAWR,YAAYR,QAAQ,GAC/B2E,sBACJ3D,UAAUJ,MAAM6C,OAAOE,OAAQC,CAAAA,SAAS,CAACI,WAAWM,SAASV,IAAI,CAAC,KAAK,CAAA,GACnEgB,kBAAkBnB,MAAME,OAAQC,CAAAA,SAAS,CAACI,WAAWM,SAASV,IAAI,CAAC,GAEnEiB,6BAA6BhD,eAC/BA,aAAajB,KAAK6C,OAAOI,KAAMD,UAAS,CAACI,WAAWM,SAASV,IAAI,CAAC,IAClE,IACEkB,iCAAiCjD,eACnCA,aAAajB,KAAK6C,OACdE,OAAQC,CAAAA,SAAS,CAACI,WAAWM,SAASV,IAAI,CAAC,EAC5CmB,MAAOnB,CAAAA,SAASH,MAAMa,SAASV,IAAI,CAAC,IACvC,IACEoB,gCAAgCnD,eAClCA,aAAajB,KAAK6C,OAAOI,KACtBD,UAAS,CAACI,WAAWM,SAASV,IAAI,KAAKH,MAAMa,SAASV,IAAI,CAC7D,IACA,IAEEqB,2BAA2BpD,eAC7BA,aAAajB,KAAK6C,OAAOsB,MAAOnB,CAAAA,SAASH,MAAMa,SAASV,IAAI,CAAC,IAC7D,IACEsB,gCAAgCN,gBAAgBf,KAAMD,UAC1De,qBAAqBL,SAASV,IAAI,CACpC;AAEA,MAAIW,sBAAsB,CAACC,aAAa;AACtC,QAAIC,sBAAsB;AACxB,UAAIQ;AACF,eAAO;AAAA,UACLlB,OAAO;AAAA,UACPN,OAAO5B,cAAcjB,KAAK6C,SAAS,CAAA;AAAA,QAAA;AAEhC,UAAIqB;AACT,eAAO;AAAA,UACLf,OAAO;AAAA,UACPN,OAAO5B,cAAcjB,KAAK6C,SAAS,CAAA;AAAA,QAAA;AAEhC,UAAIuB;AACT,eAAO;AAAA,UACLjB,OAAO;AAAA,UACPN,OAAOH,UAAU1C,KAAK6C,SAAS,CAAA;AAAA,QAAA;AAE5B,UAAI,CAAC5B;AACV,eAAO;AAAA,UACLkC,OAAO;AAAA,UACPN,OAAO,CAAA;AAAA,QAAA;AAAA,IAGb;AAEA,QAAIiB,gBAAgB;AAClB,UACG1D,YACCkE,iCACAP,oBAAoB1B,SAAS2B,gBAAgB3B,UAC/C,CAACiC;AAED,eAAO;AAAA,UACLnB,OAAO;AAAA,UACPN,OAAOzC,UAAUJ,KAAK6C,SAAS,CAAA;AAAA,QAAA;AAInC,UAAI,CAACzC;AACH,eAAO;AAAA,UACL+C,OAAO;AAAA,UACPN,OAAO,CAAA;AAAA,QAAA;AAAA,IAGb;AAAA,EACF;AAEA,SAAIgB,wBAAwB,CAACD,eAAiB3C,eACxCgD,6BACK;AAAA,IACLd,OAAO;AAAA,IACPN,OAAO,CAAA;AAAA,EAAA,IAGF;AAAA,IACLM,OAAO;AAAA,IACPN,QAAQ5B,cAAcjB,KAAK6C,SAAS,CAAA,GAAIE,OAAQC,CAAAA,SAC9CI,WAAWM,SAASV,IAAI,CAC1B;AAAA,EAAA,IAKC;AAAA,IACLG,OAAO;AAAA,IACPN,OAAOH,UAAU1C,KAAK6C,SAAS,CAAA;AAAA,EAAA;AAEnC,GChKa0B,oBAERnF,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASG,QAAQN;AACpB,WAAO,CAAA;AAGT,QAAMuF,iBAAoE,CAAA,GACpE7D,aAAaC,iBAAAA,uBAAuBxB,SAASG,QAAQN,SAAS,GAC9DI,WAAWC,iBAAAA,qBAAqBF,SAASG,QAAQN,SAAS,GAC1DwF,WAAWpD,iBAAAA,8BAA8BV,UAAU,GACnD+D,SAASrD,iBAAAA,8BAA8BhC,QAAQ;AAErD,MAAI,CAACoF,YAAY,CAACC;AAChB,WAAOF;AAGT,QAAM/C,kBAAkBrC,SAASsC,cAAcC,IAAI8C,QAAQ,GACrD7C,gBAAgBxC,SAASsC,cAAcC,IAAI+C,MAAM;AAEvD,MAAIjD,oBAAoBI,UAAaD,kBAAkBC;AACrD,WAAO2C;AAGT,QAAM1C,cAAc1C,SAASG,QAAQwC,MAAMC,MACzCP,iBACAG,gBAAgB,CAClB;AAEA,aAAWM,SAASJ,aAAa;AAC/B,QAAII,MAAM3B,SAASkE,UAAU;AAG3B,UAFAD,eAAelC,KAAK;AAAA,QAACtC,MAAMkC;AAAAA,QAAOzB,MAAM,CAAC;AAAA,UAACF,MAAM2B,MAAM3B;AAAAA,QAAAA,CAAK;AAAA,MAAA,CAAE,GAEzDkE,aAAaC;AACf;AAEF;AAAA,IACF;AAEA,QAAIxC,MAAM3B,SAASmE,QAAQ;AACzBF,qBAAelC,KAAK;AAAA,QAACtC,MAAMkC;AAAAA,QAAOzB,MAAM,CAAC;AAAA,UAACF,MAAM2B,MAAM3B;AAAAA,QAAAA,CAAK;AAAA,MAAA,CAAE;AAC7D;AAAA,IACF;AAEIiE,mBAAenC,SAAS,KAC1BmC,eAAelC,KAAK;AAAA,MAACtC,MAAMkC;AAAAA,MAAOzB,MAAM,CAAC;AAAA,QAACF,MAAM2B,MAAM3B;AAAAA,MAAAA,CAAK;AAAA,IAAA,CAAE;AAAA,EAEjE;AAEA,SAAOiE;AACT,GCnDaG,uBACXvF,CAAAA,aACG;AACH,MAAI,CAACA,SAASG,QAAQN;AACpB,WAAO,CAAA;AAGT,QAAMuF,iBAAiBD,kBAAkBnF,QAAQ,GAG3CwF,qBAFYrC,aAAanD,QAAQ,GAEDyD,SAAS,CAAA,GAAIE,OAChDC,UACC,CAAC5D,SAASG,QAAQ8D,OAAOD,WACtBE,IAAKC,CAAAA,cAAcA,UAAUC,IAAI,EACjCE,SAASV,IAAI,CACpB;AAQA,SAN0BwB,eAAeK,QAAS3C,CAAAA,UAChDnC,iBAAAA,YAAYX,SAASG,SAAS2C,MAAMlC,IAAI,IACnCkC,MAAMlC,KAAK8E,YAAY,CAAA,IACxB,EACN,EAEyB/B,OAAQgC,aAC/BH,kBAAkBlB,SAASqB,QAAQxE,IAAI,CACzC;AACF,GC3BayE,oBAER5F,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASG,QAAQN;AACpB;AAIF,QAAMgG,qBADiBV,kBAAkBnF,QAAQ,EAAEkE,IAAKpB,CAAAA,UAAUA,MAAMlC,IAAI,EAClC+C,OAAQb,WAChDnC,iBAAAA,YAAYX,SAASG,SAAS2C,KAAK,CACrC,GAEMgD,iBAAiBD,mBAAmBE,GAAG,CAAC;AAE9C,MAAI,CAACD;AACH;AAGF,QAAME,gBAAgBF,eAAeG;AAErC,MAAKD,iBAIDH,mBAAmBd,MAAOjC,CAAAA,UAAUA,MAAMmD,aAAaD,aAAa;AACtE,WAAOA;AAIX,GC7BaE,iBACXlG,CAAAA,aACG;AACH,MAAI,CAACA,SAASG,QAAQN;AACpB;AAIF,QAAMgG,qBADiBV,kBAAkBnF,QAAQ,EAAEkE,IAAKpB,CAAAA,UAAUA,MAAMlC,IAAI,EAClC+C,OAAQb,WAChDnC,iBAAAA,YAAYX,SAASG,SAAS2C,KAAK,CACrC,GAEMgD,iBAAiBD,mBAAmBE,GAAG,CAAC;AAE9C,MAAI,CAACD;AACH;AAGF,QAAMK,aAAaL,eAAeM;AAElC,MAAKD,cAIDN,mBAAmBd,MAAOjC,CAAAA,UAAUA,MAAMsD,UAAUD,UAAU;AAChE,WAAOA;AAIX,GC3BaE,sBAMRrG,CAAAA,aAAa;AAChB,QAAMoD,iBAAiBC,6BAAAA,kBAAkBrD,QAAQ,GAC3CU,oBAAoBR,qBAAqBF,QAAQ,GACjDa,4BACJH,qBAAqB4F,MAAAA,aAAa5F,kBAAkBW,KAAK,CAAC,CAAC,IACvDX,kBAAkBW,KAAK,CAAC,EAAEF,OAC1BsB;AAEN,MAAI,CAACW,kBAAkB,CAACvC;AACtB;AAGF,MAAIE,qBAAqB,IACrBwF;AAOJ,aAAWtF,SAASmC,eAAexC,KAAKM,UAAU;AAChD,QAAID,MAAME,SAASN,2BAA2B;AAC5CE,2BAAqB;AACrB;AAAA,IACF;AAEA,QAAI,CAACK,iBAAAA,SAAOpB,SAASG,SAASc,KAAK,KAAKF,oBAAoB;AAC1DwF,qBAAe;AAAA,QACb3F,MAAMK;AAAAA,QACNI,MAAM,CAAC,GAAG+B,eAAe/B,MAAM,YAAY;AAAA,UAACF,MAAMF,MAAME;AAAAA,QAAAA,CAAK;AAAA,MAAA;AAE/D;AAAA,IACF;AAAA,EACF;AAEA,SAAOoF;AACT,GC9BaC,wBACXxG,CAAAA,aACG;AAKH,MAJI,CAACA,SAASG,QAAQN,aAIlB,CAACC,6BAAAA,qBAAqBE,QAAQ;AAChC,WAAO;AAGT,QAAMoD,iBAAiBC,6BAAAA,kBAAkBrD,QAAQ,GAC3C2B,sBAAsBH,oDAAuBxB,QAAQ,GACrDyG,uBAAuB9E,sBACzB+E,iDAAgC;AAAA,IAC9BvG,SAASH,SAASG;AAAAA,IAClBwG,gBAAgBhF;AAAAA,EAAAA,CACjB,IACDc;AAEJ,MAAI,CAACW,kBAAkB,CAACzB,uBAAuB,CAAC8E;AAC9C,WAAO;AAGT,QAAMG,uBAAuBC,6BAAAA,wBAAwB7G,QAAQ,GACvD8G,kBAAkBC,iBAAAA,mBAAmB;AAAA,IACzC5G,SAASH,SAASG;AAAAA,IAClB2C,OAAOM;AAAAA,EAAAA,CACR,GAaK4D,qBAZaC,8CAAiB;AAAA,IAClC,GAAGjH;AAAAA,IACHG,SAAS;AAAA,MACP,GAAGH,SAASG;AAAAA,MACZN,WAAW;AAAA,QACTQ,QAAQuG,uBACJ;AAAA,UAACvF,MAAMuF,qBAAqBvF;AAAAA,UAAM0B,QAAQ;AAAA,QAAA,IAC1C+D;AAAAA,QACJxG,OAAOqB;AAAAA,MAAAA;AAAAA,IACT;AAAA,EACF,CACD,EACqCuF,MAAM,KAAK,EAAEnB,GAAG,EAAE,GAElDoB,mBAAmBd,oBAAoBrG,QAAQ,GAC/CoH,gBAAgBC,0BAAAA,iBAAiB;AAAA,IACrClH,SAASH,SAASG;AAAAA,IAClB2C,OAAOM;AAAAA,EAAAA,CACR,GAaKkE,oBAZYL,8CAAiB;AAAA,IACjC,GAAGjH;AAAAA,IACHG,SAAS;AAAA,MACP,GAAGH,SAASG;AAAAA,MACZN,WAAW;AAAA,QACTQ,QAAQsB;AAAAA,QACRrB,OAAO6G,mBACH;AAAA,UAAC9F,MAAM8F,iBAAiB9F;AAAAA,UAAM0B,QAAQ;AAAA,QAAA,IACtCqE;AAAAA,MAAAA;AAAAA,IACN;AAAA,EACF,CACD,EACmCF,MAAM,KAAK,EAAEnB,GAAG,CAAC;AAErD,OACGiB,uBAAuBvE,UAAauE,uBAAuB,QAC3DM,sBAAsB7E,UAAa6E,sBAAsB;AAE1D,WAAO;AAGT,QAAMC,uBAAoCP,qBACtC;AAAA,IACE,GAAGP;AAAAA,IACH1D,QAAQ0D,qBAAqB1D,SAASiE,mBAAmB/D;AAAAA,EAAAA,IAE3DwD,sBACEe,qBAAkCF,oBACpC;AAAA,IACE,GAAGb;AAAAA,IACH1D,QAAQ0D,qBAAqB1D,SAASuE,kBAAkBrE;AAAAA,EAAAA,IAE1DwD,sBAEEgB,+BAA+BC,iDAAgC;AAAA,IACnEvH,SAASH,SAASG;AAAAA,IAClBwH,aAAaJ;AAAAA,IACbK,WAAW;AAAA,EAAA,CACZ,GACKC,6BAA6BH,iDAAgC;AAAA,IACjEvH,SAASH,SAASG;AAAAA,IAClBwH,aAAaH;AAAAA,IACbI,WAAW;AAAA,EAAA,CACZ;AAED,MAAI,CAACH,gCAAgC,CAACI;AACpC,WAAO;AAGT,QAAMC,qBAAqB;AAAA,IACzBzH,QAAQoH;AAAAA,IACRnH,OAAOuH;AAAAA,EAAAA;AAGT,SAAOjI,iDAAoB;AAAA,IAEzBO,SAAS;AAAA,MACP,GAAGH,SAASG;AAAAA,MACZN,WAAWiI;AAAAA,IAAAA;AAAAA,EACb,CACD,IACGA,qBACA;AACN,GC9HaC,gBAER/H,CAAAA,aAAa;AAChB,QAAMY,OAAOZ,SAASG,QAAQwC,MAAM,CAAC;AAErC,SAAO/B,OAAO;AAAA,IAACA;AAAAA,IAAMS,MAAM,CAAC;AAAA,MAACF,MAAMP,KAAKO;AAAAA,IAAAA,CAAK;AAAA,EAAA,IAAKsB;AACpD,GCJauF,sBAERhI,CAAAA,aAAa;AAChB,QAAMiI,aAAa7H,6BAAAA,cAAcJ,QAAQ;AAEzC,SAAOiI,cAAc,CAACtH,6BAAYX,SAASG,SAAS8H,WAAWrH,IAAI,IAC/D;AAAA,IAACA,MAAMqH,WAAWrH;AAAAA,IAAMS,MAAM4G,WAAW5G;AAAAA,EAAAA,IACzCoB;AACN,GCTayF,uBAERlI,CAAAA,aAAa;AAChB,QAAMmI,aAAaC,6BAAAA,cAAcpI,QAAQ;AAEzC,SAAOmI,cAAc,CAACE,MAAAA,mBAAmBF,WAAWvH,IAAI,IACpD;AAAA,IAACA,MAAMuH,WAAWvH;AAAAA,IAAMS,MAAM8G,WAAW9G;AAAAA,EAAAA,IACzCoB;AACN,GCPa6F,oBAERtI,CAAAA,aAAa;AAChB,QAAMoD,iBAAiBC,6BAAAA,kBAAkBrD,QAAQ;AAEjD,SAAOoD,kBAAkBmF,iBAAAA,YAAYvI,SAASG,SAASiD,eAAexC,IAAI,IACtE;AAAA,IAACA,MAAMwC,eAAexC;AAAAA,IAAMS,MAAM+B,eAAe/B;AAAAA,EAAAA,IACjDoB;AACN,GCVa+F,eAERxI,CAAAA,aAAa;AAChB,QAAMY,OAAOZ,SAASG,QAAQwC,MAAM3C,SAASG,QAAQwC,MAAMM,SAAS,CAAC,IACjEjD,SAASG,QAAQwC,MAAM3C,SAASG,QAAQwC,MAAMM,SAAS,CAAC,IACxDR;AAEJ,SAAO7B,OAAO;AAAA,IAACA;AAAAA,IAAMS,MAAM,CAAC;AAAA,MAACF,MAAMP,KAAKO;AAAAA,IAAAA,CAAK;AAAA,EAAA,IAAKsB;AACpD,GCPagG,eAERzI,CAAAA,aAAa;AAChB,QAAMS,oBAAoBV,qBAAqBC,QAAQ;AAEvD,MAAI,CAACS;AACH;AAGF,QAAM+C,QAAQxD,SAASsC,cAAcC,IAAI9B,kBAAkBG,KAAKO,IAAI;AAEpE,MAAIqC,UAAUf,UAAae,UAAUxD,SAASG,QAAQwC,MAAMM,SAAS;AACnE;AAGF,QAAMyF,YAAY1I,SAASG,QAAQwC,MAAMoD,GAAGvC,QAAQ,CAAC;AAErD,SAAOkF,YACH;AAAA,IAAC9H,MAAM8H;AAAAA,IAAWrH,MAAM,CAAC;AAAA,MAACF,MAAMuH,UAAUvH;AAAAA,IAAAA,CAAK;AAAA,EAAA,IAC/CsB;AACN,GCpBakG,mBAER3I,CAAAA,aAAa;AAChB,QAAM0B,sBAAsBJ,uBAAuBtB,QAAQ;AAE3D,MAAI,CAAC0B;AACH;AAGF,QAAM8B,QAAQxD,SAASsC,cAAcC,IAAIb,oBAAoBd,KAAKO,IAAI;AAEtE,MAAIqC,UAAUf,UAAae,UAAU;AACnC;AAGF,QAAMoF,gBAAgB5I,SAASG,QAAQwC,MAAMoD,GAAGvC,QAAQ,CAAC;AAEzD,SAAOoF,gBACH;AAAA,IAAChI,MAAMgI;AAAAA,IAAevH,MAAM,CAAC;AAAA,MAACF,MAAMyH,cAAczH;AAAAA,IAAAA,CAAK;AAAA,EAAA,IACvDsB;AACN,GClBaoG,wBAER7I,CAAAA,aAAa;AAChB,MAAI,CAACA,SAASG,QAAQN;AACpB,WAAO,CAAA;AAGT,QAAMgG,qBAGD,CAAA,GAECtE,aAAaC,iBAAAA,uBAAuBxB,SAASG,QAAQN,SAAS,GAC9DI,WAAWC,iBAAAA,qBAAqBF,SAASG,QAAQN,SAAS,GAC1DmC,gBAAgBC,iBAAAA,8BAA8BV,UAAU,GACxDW,cAAcD,iBAAAA,8BAA8BhC,QAAQ;AAE1D,MAAI,CAAC+B,iBAAiB,CAACE;AACrB,WAAO2D;AAGT,QAAMxD,kBAAkBrC,SAASsC,cAAcC,IAAIP,aAAa,GAC1DQ,gBAAgBxC,SAASsC,cAAcC,IAAIL,WAAW;AAE5D,MAAIG,oBAAoBI,UAAaD,kBAAkBC;AACrD,WAAOoD;AAGT,QAAMnD,cAAc1C,SAASG,QAAQwC,MAAMC,MACzCP,iBACAG,gBAAgB,CAClB;AAEA,aAAWM,SAASJ,aAAa;AAC/B,QAAII,MAAM3B,SAASa,eAAe;AAKhC,UAJIrB,iBAAAA,YAAYX,SAASG,SAAS2C,KAAK,KACrC+C,mBAAmB3C,KAAK;AAAA,QAACtC,MAAMkC;AAAAA,QAAOzB,MAAM,CAAC;AAAA,UAACF,MAAM2B,MAAM3B;AAAAA,QAAAA,CAAK;AAAA,MAAA,CAAE,GAG/Da,kBAAkBE;AACpB;AAEF;AAAA,IACF;AAEA,QAAIY,MAAM3B,SAASe,aAAa;AAC1BvB,uBAAAA,YAAYX,SAASG,SAAS2C,KAAK,KACrC+C,mBAAmB3C,KAAK;AAAA,QAACtC,MAAMkC;AAAAA,QAAOzB,MAAM,CAAC;AAAA,UAACF,MAAM2B,MAAM3B;AAAAA,QAAAA,CAAK;AAAA,MAAA,CAAE;AAGnE;AAAA,IACF;AAEI0E,uBAAmB5C,SAAS,KAC1BtC,6BAAYX,SAASG,SAAS2C,KAAK,KACrC+C,mBAAmB3C,KAAK;AAAA,MAACtC,MAAMkC;AAAAA,MAAOzB,MAAM,CAAC;AAAA,QAACF,MAAM2B,MAAM3B;AAAAA,MAAAA,CAAK;AAAA,IAAA,CAAE;AAAA,EAGvE;AAEA,SAAO0E;AACT,GCpDaiD,sBACX9I,CAAAA,aACG;AACH,MAAI,CAACA,SAASG,QAAQN;AACpB,WAAOG,SAASG,QAAQN;AAG1B,QAAM0B,aAAaC,iBAAAA,uBAAuBxB,SAASG,QAAQN,SAAS,GAC9DI,WAAWC,sCAAqBF,SAASG,QAAQN,SAAS,GAE1DmC,gBAAgBC,iBAAAA,8BAA8BV,UAAU,GACxDwH,gBAAgBjI,iBAAAA,8BAA8BS,UAAU,GACxDW,cAAcD,iBAAAA,8BAA8BhC,QAAQ,GACpD+I,cAAclI,iBAAAA,8BAA8Bb,QAAQ;AAE1D,MAAI,CAAC+B,iBAAiB,CAACE;AACrB,WAAOlC,SAASG,QAAQN;AAG1B,QAAMwC,kBAAkBrC,SAASsC,cAAcC,IAAIP,aAAa,GAC1DQ,gBAAgBxC,SAASsC,cAAcC,IAAIL,WAAW;AAE5D,MAAIG,oBAAoBI,UAAaD,kBAAkBC;AACrD,WAAOzC,SAASG,QAAQN;AAG1B,QAAM6C,cAAc1C,SAASG,QAAQwC,MAAMC,MACzCP,iBACAG,gBAAgB,CAClB;AAEA,MAAIK,kBAAkB,IAClBoG,oBACAC,iBAAiB,IACjBC,kBACAC,eAAe,IACfC;AAIJ,aAAWvG,SAASJ;AAClB,QAAII,EAAAA,MAAM3B,SAASa,kBACjBa,kBAAkB,IAGhBlC,6BAAYX,SAASG,SAAS2C,KAAK,KACnCwG,0BAAAA,iBAAiBtJ,SAASG,SAAS2C,KAAK,OAMvCD,mBAIAlC,iBAAAA,YAAYX,SAASG,SAAS2C,KAAK,GAIxC;AAAA,UACEA,MAAM3B,SAASe,eACfoH,0BAAAA,iBAAiBtJ,SAASG,SAAS2C,KAAK;AAExC;AAGF,iBAAW7B,SAAS6B,MAAM5B,UAAU;AAClC,YAAID,MAAME,SAAS6H,gBACb,CAAC5H,iBAAAA,OAAOpB,SAASG,SAASc,KAAK,KAAKhB,SAAS8C,WAAW,IAAG;AAC7DoG,6BAAmBE,4BACf;AAAA,YACEhI,MAAM,CACJ;AAAA,cAACF,MAAMkI,0BAA0BE;AAAAA,YAAAA,GACjC,YACA;AAAA,cAACpI,MAAMkI,0BAA0B3F,KAAKvC;AAAAA,YAAAA,CAAK;AAAA,YAE7C4B,QAAQsG,0BAA0B3F,KAAKV,KAAKC;AAAAA,UAAAA,IAE9CR,QAEJ2G,eAAe;AACf;AAAA,QACF;AAGF,YAAIF,gBAAgB;AAClB,gBAAMM,aACJpI,wBAAOpB,SAASG,SAASc,KAAK,KAAK6B,MAAM5B,SAAS+B,WAAW;AAE/D,WACG7B,wBAAOpB,SAASG,SAASc,KAAK,KAAKA,MAAM+B,KAAKC,SAAS,KACxDuG,gBAEAP,qBAAqB;AAAA,YACnB5H,MAAM,CAAC;AAAA,cAACF,MAAM2B,MAAM3B;AAAAA,YAAAA,GAAO,YAAY;AAAA,cAACA,MAAMF,MAAME;AAAAA,YAAAA,CAAK;AAAA,YACzD4B,QAAQ;AAAA,UAAA,GAEVsG,4BAA4B;AAAA,YAACE,UAAUzG,MAAM3B;AAAAA,YAAMuC,MAAMzC;AAAAA,UAAAA,GACzDiI,iBAAiB;AAGnB;AAAA,QACF;AAEA,YAAIjI,MAAME,SAAS4H,eAAe;AAChC,cAAI,CAAC3H,iBAAAA,OAAOpB,SAASG,SAASc,KAAK,GAAG;AACpCiI,6BAAiB;AACjB;AAAA,UACF;AAEA,cAAI3H,WAAWwB,WAAW9B,MAAM+B,KAAKC,QAAQ;AAC3CiG,6BAAiB,IACjBG,4BACEpI,MAAM+B,KAAKC,SAAS,IAChB;AAAA,cAACsG,UAAUzG,MAAM3B;AAAAA,cAAMuC,MAAMzC;AAAAA,YAAAA,IAC7BoI;AACN;AAAA,UACF;AAAA,QACF;AAEAA,oCACEjI,iBAAAA,OAAOpB,SAASG,SAASc,KAAK,KAAKA,MAAM+B,KAAKC,SAAS,IACnD;AAAA,UAACsG,UAAUzG,MAAM3B;AAAAA,UAAMuC,MAAMzC;AAAAA,QAAAA,IAC7BoI;AAAAA,MACR;AAEA,UAAIvG,MAAM3B,SAASe;AACjB;AAAA,IAAA;AAIJ,QAAMuH,mBAAmBzJ,SAASG,QAAQN,UAAUU,WAChD;AAAA,IACEF,QAAQ+I,gBAAgBD,mBAAmBA,mBAAmBlJ;AAAAA,IAC9DK,OAAO2I,sBAAsB1H;AAAAA,IAC7BhB,UAAU;AAAA,EAAA,IAEZ;AAAA,IACEF,QAAQ4I,sBAAsB1H;AAAAA,IAC9BjB,OAAO8I,gBAAgBD,mBAAmBA,mBAAmBlJ;AAAAA,EAAAA;AAGnE,MACEH,kDAAqB;AAAA,IAEnBK,SAAS;AAAA,MACP,GAAGH,SAASG;AAAAA,MACZN,WAAW4J;AAAAA,IAAAA;AAAAA,EACb,CACD,GACD;AACA,UAAMrG,iBAAiBC,6BAAAA,kBAAkB;AAAA,MACvC,GAAGrD;AAAAA,MACHG,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN,WAAW4J;AAAAA,MAAAA;AAAAA,IACb,CACD;AAED,QACErG,kBACA,CAACkG,0BAAAA,iBAAiBtJ,SAASG,SAASiD,eAAexC,IAAI;AAEvD,aAAO;AAAA,EAEX;AAEA,SAAO6I;AACT;ACzLO,SAASC,0BAA0B1J,UAA0B;AAClE,QAAMiE,SAASjE,SAASG,QAAQ8D;AAGhC,UAFkBd,aAAanD,QAAQ,GAEpByD,SAAS,IAAIE,OAC7BC,CAAAA,SACC,CAACK,OAAOD,WAAWE,IAAKC,CAAAA,cAAcA,UAAUC,IAAI,EAAEE,SAASV,IAAI,CACvE;AACF;ACHO,SAAS+F,mBACdC,YACyB;AACzB,SAAQ5J,CAAAA,aAAa;AAEnB,UAAM6J,oBADiB1E,kBAAkBnF,QAAQ,EACRyF,QAAS3C,CAAAA,UAChDnC,iBAAAA,YAAYX,SAASG,SAAS2C,MAAMlC,IAAI,IACnCkC,MAAMlC,KAAK8E,YAAY,CAAA,IACxB,CAAA,CACN,GACMF,oBAAoBkE,0BAA0B1J,QAAQ;AAO5D,WANuB6J,kBAAkBlG,OACtCgC,CAAAA,YACCA,QAAQmE,UAAUF,cAClBpE,kBAAkBlB,SAASqB,QAAQxE,IAAI,CAC3C,EAEsB8B,SAAS;AAAA,EACjC;AACF;ACxBO,SAAS8G,oBAAoB/J,UAA0B;AAC5D,QAAMiE,SAASjE,SAASG,QAAQ8D,QAC1B+F,iBAAiBhK,SAASgK,gBAC1BC,YAAY9G,aAAanD,QAAQ,GACjCgE,aAAaC,OAAOD,WAAWE,IAAKC,CAAAA,cAAcA,UAAUC,IAAI;AAMtE,MAAI8F,oBAJyBD,WAAWxG,SAAS,CAAA,GAAIE,OAAQC,CAAAA,SAC3DI,WAAWM,SAASV,IAAI,CAC1B;AAIA,aAAWO,aAAa6F;AAClBA,mBAAe7F,SAAS,MAAM,KAChC+F,mBAAmBA,iBAAiBvG,OACjCwG,qBAAoBA,oBAAoBhG,SAC3C,IACS6F,eAAe7F,SAAS,MAAM,OAClC+F,iBAAiB5F,SAASH,SAAS,KACtC+F,iBAAiBhH,KAAKiB,SAAS;AAKrC,SAAO+F;AACT;ACpBO,SAASE,kBAAkBjG,WAA4C;AAC5E,SAAQnE,CAAAA,aAAa;AACnB,QAAIJ,6BAAAA,oBAAoBI,QAAQ,GAAG;AACjC,YAAM+B,gBAAgBD,iBAAiB9B,QAAQ;AAE/C,aACE+B,cAAckB,SAAS,KACvBlB,cAAcgD,MAAOrB,CAAAA,SAASA,KAAK9C,KAAK6C,OAAOa,SAASH,SAAS,CAAC;AAAA,IAEtE;AAIA,WAFyB4F,oBAAoB/J,QAAQ,EAE7BsE,SAASH,SAAS;AAAA,EAC5C;AACF;ACjBO,SAASkG,iBAAiBpE,UAA2C;AAC1E,SAAQjG,CAAAA,aACiB4F,kBAAkB5F,QAAQ,MAEvBiG;AAE9B;ACNO,SAASqE,cAAclE,OAAwC;AACpE,SAAQpG,CAAAA,aACckG,eAAelG,QAAQ,MAEpBoG;AAE3B;ACHO,SAASmE,kBAAkBzH,OAGN;AAC1B,SAAQ9C,CAAAA,aAAa;AACnB,QAAI,CAACA,SAASG,QAAQN,aAAa,CAACC,6BAAAA,qBAAqBE,QAAQ;AAC/D,aAAO;AAGT,UAAMoH,gBAAgBoD,0BAAAA,iBAAuB;AAAA,MAC3CrK,SAASH,SAASG;AAAAA,MAClB2C;AAAAA,IAAAA,CACD;AAED,WAAO0H,0BAAAA,uBACLxK,SAASG,QAAQN,UAAUS,OAC3B8G,aACF;AAAA,EACF;AACF;ACnBO,SAASqD,oBAAoB3H,OAGR;AAC1B,SAAQ9C,CAAAA,aAAa;AACnB,QAAI,CAACA,SAASG,QAAQN,aAAa,CAACC,6BAAAA,qBAAqBE,QAAQ;AAC/D,aAAO;AAGT,UAAM8G,kBAAkB0D,iBAAAA,mBAAyB;AAAA,MAC/CrK,SAASH,SAASG;AAAAA,MAClB2C;AAAAA,IAAAA,CACD;AAED,WAAO0H,0BAAAA,uBACLxK,SAASG,QAAQN,UAAUS,OAC3BwG,eACF;AAAA,EACF;AACF;AChBO,SAAS4D,sBACdC,OACyB;AACzB,SAAQ3K,CAAAA,aAAa;AACnB,QAAI,CAACA,SAASG,QAAQN;AACpB,aAAO;AAGT,UAAMI,WAAWC,iBAAAA,qBAAqBF,SAASG,QAAQN,SAAS,GAC1DqC,cAAcD,iBAAAA,8BAA8BhC,QAAQ,GACpD+I,cAAclI,+CAA8Bb,QAAQ,GAEpD2K,gBAAgB3I,iBAAAA,8BAA8B0I,KAAK,GACnDE,gBAAgB/J,iBAAAA,8BAA8B6J,KAAK;AAEzD,QAAI,CAACC,iBAAiB,CAAC1I;AACrB,aAAO;AAGT,UAAM4I,kBAAkB9K,SAASsC,cAAcC,IAAIqI,aAAa,GAC1DpI,gBAAgBxC,SAASsC,cAAcC,IAAIL,WAAW;AAE5D,QAAI4I,oBAAoBrI,UAAaD,kBAAkBC;AACrD,aAAO;AAGT,QAAIqI,kBAAkBtI;AAEpB,aAAO;AAGT,QAAIsI,kBAAkBtI;AAEpB,aAAO;AAIT,UAAMuI,aAAa/K,SAASG,QAAQwC,MAAMoD,GAAG+E,eAAe;AAO5D,QALI,CAACC,cAKD,CAACpK,iBAAAA,YAAYX,SAASG,SAAS4K,UAAU;AAI3C,aAAO;AAGT,QAAIC,iBACAC,eAEAC,aAAa;AAIjB,eAAWjK,SAAS8J,WAAW7J,UAAU;AAGvC,UAFAgK,cAEIjK,MAAME,SAAS0J,iBAAiB5J,MAAME,SAAS6H;AACjD,eAAO2B,MAAM5H,SAAS9C,SAAS8C;AAWjC,UARI9B,MAAME,SAAS0J,kBACjBG,kBAAkBE,aAGhBjK,MAAME,SAAS6H,gBACjBiC,gBAAgBC,aAGdF,oBAAoBvI,UAAawI,kBAAkBxI;AACrD;AAAA,IAEJ;AAEA,WAAIuI,oBAAoBvI,UAAawI,kBAAkBxI,SAC9C,KAGFuI,kBAAkBC;AAAAA,EAC3B;AACF;ACpFO,SAASE,uBACdR,OACyB;AACzB,SAAQ3K,CAAAA,aAAa;AACnB,QAAI,CAACA,SAASG,QAAQN;AACpB,aAAO;AAGT,UAAM0B,aAAaC,iBAAAA,uBAAuBxB,SAASG,QAAQN,SAAS,GAC9DmC,gBAAgBC,iBAAAA,8BAA8BV,UAAU,GACxDwH,gBAAgBjI,+CAA8BS,UAAU,GAExDqJ,gBAAgB3I,iBAAAA,8BAA8B0I,KAAK,GACnDE,gBAAgB/J,iBAAAA,8BAA8B6J,KAAK;AAEzD,QAAI,CAACC,iBAAiB,CAAC5I;AACrB,aAAO;AAGT,UAAMK,kBAAkBrC,SAASsC,cAAcC,IAAIP,aAAa,GAC1D8I,kBAAkB9K,SAASsC,cAAcC,IAAIqI,aAAa;AAEhE,QAAIvI,oBAAoBI,UAAaqI,oBAAoBrI;AACvD,aAAO;AAGT,QAAIqI,kBAAkBzI;AAEpB,aAAO;AAGT,QAAIyI,kBAAkBzI;AAEpB,aAAO;AAIT,UAAM0I,aAAa/K,SAASG,QAAQwC,MAAMoD,GAAG+E,eAAe;AAO5D,QALI,CAACC,cAKD,CAACpK,iBAAAA,YAAYX,SAASG,SAAS4K,UAAU;AAI3C,aAAO;AAGT,QAAIC,iBACAI,iBAEAF,aAAa;AAIjB,eAAWjK,SAAS8J,WAAW7J,UAAU;AAGvC,UAFAgK,cAEIjK,MAAME,SAAS0J,iBAAiB5J,MAAME,SAAS4H;AACjD,eAAO4B,MAAM5H,SAASxB,WAAWwB;AAWnC,UARI9B,MAAME,SAAS0J,kBACjBG,kBAAkBE,aAGhBjK,MAAME,SAAS4H,kBACjBqC,kBAAkBF,aAGhBF,oBAAoBvI,UAAa2I,oBAAoB3I;AACvD;AAAA,IAEJ;AAEA,WAAIuI,oBAAoBvI,UAAa2I,oBAAoB3I,SAChD,KAGFuI,kBAAkBI;AAAAA,EAC3B;AACF;ACrFO,SAASC,uBACdxL,WACyB;AACzB,SAAQG,CAAAA,aAAa;AACnB,QAAI,CAACH,aAAa,CAACG,SAASG,QAAQN;AAClC,aAAO;AAGT,UAAM8B,sBAAsBH,6BAAAA,uBAAuB;AAAA,MAEjDrB,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN;AAAAA,MAAAA;AAAAA,IACF,CACD,GACKa,oBAAoBR,qBAAqB;AAAA,MAE7CC,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN;AAAAA,MAAAA;AAAAA,IACF,CACD,GAEKyL,8BAA8B9J,oDAAuBxB,QAAQ,GAC7DuL,4BAA4BrL,qBAAqBF,QAAQ;AAE/D,QACE,CAAC2B,uBACD,CAACjB,qBACD,CAAC4K,+BACD,CAACC;AAED,aAAO;AAGT,UAAMC,4BACJL,uBAAuBxJ,mBAAmB,EAAE3B,QAAQ,GAChDyL,2BACJf,sBAAsB/I,mBAAmB,EAAE3B,QAAQ,GAC/C0L,0BACJP,uBAAuBzK,iBAAiB,EAAEV,QAAQ,GAC9C2L,yBACJjB,sBAAsBhK,iBAAiB,EAAEV,QAAQ,GAE7C4L,qCAAqCT,uBACzCG,2BACF,EAAE;AAAA,MACA,GAAGtL;AAAAA,MACHG,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN,WAAW;AAAA,UACTQ,QAAQsB;AAAAA,UACRrB,OAAOqB;AAAAA,QAAAA;AAAAA,MACT;AAAA,IACF,CACD,GACKkK,oCAAoCnB,sBACxCY,2BACF,EAAE;AAAA,MACA,GAAGtL;AAAAA,MACHG,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN,WAAW;AAAA,UACTQ,QAAQsB;AAAAA,UACRrB,OAAOqB;AAAAA,QAAAA;AAAAA,MACT;AAAA,IACF,CACD,GAEKmK,iCAAiCX,uBACrCI,yBACF,EAAE;AAAA,MACA,GAAGvL;AAAAA,MACHG,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN,WAAW;AAAA,UACTQ,QAAQK;AAAAA,UACRJ,OAAOI;AAAAA,QAAAA;AAAAA,MACT;AAAA,IACF,CACD,GACKqL,gCAAgCrB,sBACpCa,yBACF,EAAE;AAAA,MACA,GAAGvL;AAAAA,MACHG,SAAS;AAAA,QACP,GAAGH,SAASG;AAAAA,QACZN,WAAW;AAAA,UACTQ,QAAQK;AAAAA,UACRJ,OAAOI;AAAAA,QAAAA;AAAAA,MACT;AAAA,IACF,CACD,GAEKsL,oCAAoCC,0BAAAA,uBACxCvL,mBACA4K,2BACF,GACMY,oCAAoCD,0BAAAA,uBACxCtK,qBACA4J,yBACF;AAMA,WAJIG,2BAA2B,CAACM,qCAI5BP,4BAA4B,CAACS,oCACxB,KAIP,CAACN,sCACDC,qCACA,CAACC,kCACDC,gCAEO,CAACC,oCAIRJ,sCACA,CAACC,qCACDC,kCACA,CAACC,gCAEM,CAACG,oCAIR,CAACT,4BACD,CAACD,6BACD,CAACG,0BACD,CAACD;AAAAA,EAML;AACF;AC/IO,MAAMS,0BAAoDnM,CAAAA,aAAa;AAC5E,MAAI,CAACA,SAASG,QAAQN;AACpB,WAAO;AAGT,QAAM0B,aAAavB,SAASG,QAAQN,UAAUU,WAC1CP,SAASG,QAAQN,UAAUS,QAC3BN,SAASG,QAAQN,UAAUQ,QACzBJ,WAAWD,SAASG,QAAQN,UAAUU,WACxCP,SAASG,QAAQN,UAAUQ,SAC3BL,SAASG,QAAQN,UAAUS,OAEzB8L,aAAa9K,uBAAuBtB,QAAQ,GAC5CqM,WAAWtM,qBAAqBC,QAAQ;AAE9C,MAAI,CAACoM,cAAc,CAACC;AAClB,WAAO;AAGT,QAAMC,uBAAuB9B,iBAAAA,mBAAyB;AAAA,IACpDrK,SAASH,SAASG;AAAAA,IAClB2C,OAAOsJ;AAAAA,EAAAA,CACR,GACKG,mBAAmB/B,2CAAuB;AAAA,IAC9CrK,SAASH,SAASG;AAAAA,IAClB2C,OAAOuJ;AAAAA,EAAAA,CACR;AAED,SACE7B,0BAAAA,uBAA6B8B,sBAAsB/K,UAAU,KAC7DiJ,0BAAAA,uBAA6B+B,kBAAkBtM,QAAQ;AAE3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}