'use client';
import { useState } from 'react';

const defaultFAQs = [
  {
    question: "C<PERSON> retreat jest odpowiedni dla początkujących?",
    answer: "Absolutnie! Nasze retreaty są dostosowane do wszystkich poziomów zaawansowania."
  },
  {
    question: "Co jest wliczone w cenę retreatu?",
    answer: "W cenie retreatu masz wszystko: zakwaterowanie, posiłki, transport i praktykę jogi."
  },
  {
    question: "Ile osób uczestniczy w retreatie?",
    answer: "Nasze grupy są małe - maksymalnie 12 osób."
  }
];

export default function FAQSection({
  faqs = defaultFAQs, 
  title = "Często zadawane pytania",
  className = ""
}) {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className={className}>
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-4">{title}</h2>
      </div>
      
      <div className="max-w-3xl mx-auto space-y-4">
        {faqs.map((faq, index) => (
          <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
            <button
              onClick={() => toggleFAQ(index)}
              className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
            >
              <h3 className="font-semibold">{faq.question}</h3>
              <span className="text-2xl">
                {openIndex === index ? '−' : '+'}
              </span>
            </button>
            
            {openIndex === index && (
              <div className="px-6 pb-4">
                <p className="text-gray-700">{faq.answer}</p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
