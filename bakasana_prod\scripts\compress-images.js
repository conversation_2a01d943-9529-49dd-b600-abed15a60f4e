const fs = require('fs');
const path = require('path');

// Dynamic imports for ESM modules
let imagemin, imageminMozjpeg, imageminPngquant, imageminWebp, imageminAvif;

async function loadImageminModules() {
  if (!imagemin) {
    imagemin = (await import('imagemin')).default;
    imageminMozjpeg = (await import('imagemin-mozjpeg')).default;
    imageminPngquant = (await import('imagemin-pngquant')).default;
    imageminWebp = (await import('imagemin-webp')).default;
    imageminAvif = (await import('imagemin-avif')).default;
  }
}

// Configuration for maximum compression
const config = {
  inputDir: path.join(__dirname, '../public/images'),
  outputDir: path.join(__dirname, '../public/images/optimized'),
  webpDir: path.join(__dirname, '../public/images/webp'),
  avifDir: path.join(__dirname, '../public/images/avif'),
  quality: {
    jpeg: 85,
    png: [0.6, 0.9],
    webp: 85,
    avif: 80,
  },
};

// Ensure output directories exist
[config.outputDir, config.webpDir, config.avifDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Find all images recursively
function findImages(dir, extensions = ['.jpg', '.jpeg', '.png', '.webp']) {
  const images = [];
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !file.includes('optimized') && !file.includes('webp') && !file.includes('avif')) {
      images.push(...findImages(filePath, extensions));
    } else if (extensions.some(ext => file.toLowerCase().endsWith(ext))) {
      images.push(filePath);
    }
  });

  return images;
}

// Compress JPEG/PNG images
async function compressOriginalImages() {
  await loadImageminModules();

  console.log('🗜️ KOMPRESJA OBRAZKÓW - NAJWIĘKSZY wpływ na performance!');
  console.log('='.repeat(60));
  
  const jpegImages = findImages(config.inputDir, ['.jpg', '.jpeg']);
  const pngImages = findImages(config.inputDir, ['.png']);
  
  let totalSizeBefore = 0;
  let totalSizeAfter = 0;
  let processedCount = 0;

  // Compress JPEG images
  if (jpegImages.length > 0) {
    console.log(`\n📸 Kompresowanie ${jpegImages.length} obrazków JPEG...`);
    
    for (const imagePath of jpegImages) {
      try {
        const sizeBefore = fs.statSync(imagePath).size;
        totalSizeBefore += sizeBefore;
        
        const relativePath = path.relative(config.inputDir, imagePath);
        const outputPath = path.join(config.outputDir, relativePath);
        const outputDir = path.dirname(outputPath);
        
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }

        const result = await imagemin([imagePath], {
          destination: outputDir,
          plugins: [
            imageminMozjpeg({
              quality: config.quality.jpeg,
              progressive: true,
            }),
          ],
        });

        if (result && result.length > 0) {
          const sizeAfter = result[0].data.length;
          totalSizeAfter += sizeAfter;
          const savings = Math.round(((sizeBefore - sizeAfter) / sizeBefore) * 100);
          
          console.log(`  ✅ ${relativePath}: ${Math.round(sizeBefore/1024)}KB → ${Math.round(sizeAfter/1024)}KB (${savings}% mniej)`);
          processedCount++;
        }
      } catch (error) {
        console.error(`  ❌ Błąd: ${imagePath} - ${error.message}`);
      }
    }
  }

  // Compress PNG images
  if (pngImages.length > 0) {
    console.log(`\n🖼️ Kompresowanie ${pngImages.length} obrazków PNG...`);
    
    for (const imagePath of pngImages) {
      try {
        const sizeBefore = fs.statSync(imagePath).size;
        totalSizeBefore += sizeBefore;
        
        const relativePath = path.relative(config.inputDir, imagePath);
        const outputPath = path.join(config.outputDir, relativePath);
        const outputDir = path.dirname(outputPath);
        
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }

        const result = await imagemin([imagePath], {
          destination: outputDir,
          plugins: [
            imageminPngquant({
              quality: config.quality.png,
              strip: true,
            }),
          ],
        });

        if (result && result.length > 0) {
          const sizeAfter = result[0].data.length;
          totalSizeAfter += sizeAfter;
          const savings = Math.round(((sizeBefore - sizeAfter) / sizeBefore) * 100);
          
          console.log(`  ✅ ${relativePath}: ${Math.round(sizeBefore/1024)}KB → ${Math.round(sizeAfter/1024)}KB (${savings}% mniej)`);
          processedCount++;
        }
      } catch (error) {
        console.error(`  ❌ Błąd: ${imagePath} - ${error.message}`);
      }
    }
  }

  const totalSavings = totalSizeBefore - totalSizeAfter;
  const savingsPercent = totalSizeBefore > 0 ? Math.round((totalSavings / totalSizeBefore) * 100) : 0;

  console.log('\n🎉 KOMPRESJA ZAKOŃCZONA!');
  console.log('='.repeat(30));
  console.log(`📊 Przetworzono: ${processedCount} obrazków`);
  console.log(`💾 Rozmiar przed: ${Math.round(totalSizeBefore / 1024 / 1024 * 100) / 100} MB`);
  console.log(`💾 Rozmiar po: ${Math.round(totalSizeAfter / 1024 / 1024 * 100) / 100} MB`);
  console.log(`🎯 Oszczędności: ${Math.round(totalSavings / 1024 / 1024 * 100) / 100} MB (${savingsPercent}%)`);
}

// Convert to WebP format
async function convertToWebP() {
  await loadImageminModules();

  console.log('\n🌐 KONWERSJA DO WebP - jeszcze mniejsze pliki!');
  console.log('='.repeat(50));
  
  const images = findImages(config.inputDir, ['.jpg', '.jpeg', '.png']);
  let convertedCount = 0;
  let totalSizeBefore = 0;
  let totalSizeAfter = 0;

  for (const imagePath of images) {
    try {
      const sizeBefore = fs.statSync(imagePath).size;
      totalSizeBefore += sizeBefore;
      
      const relativePath = path.relative(config.inputDir, imagePath);
      const webpPath = path.join(config.webpDir, relativePath.replace(/\.(jpg|jpeg|png)$/i, '.webp'));
      const webpDir = path.dirname(webpPath);
      
      if (!fs.existsSync(webpDir)) {
        fs.mkdirSync(webpDir, { recursive: true });
      }

      const result = await imagemin([imagePath], {
        destination: webpDir,
        plugins: [
          imageminWebp({
            quality: config.quality.webp,
            method: 6, // Best compression
          }),
        ],
      });

      if (result && result.length > 0) {
        const sizeAfter = result[0].data.length;
        totalSizeAfter += sizeAfter;
        const savings = Math.round(((sizeBefore - sizeAfter) / sizeBefore) * 100);
        
        console.log(`  ✅ ${relativePath} → WebP: ${Math.round(sizeBefore/1024)}KB → ${Math.round(sizeAfter/1024)}KB (${savings}% mniej)`);
        convertedCount++;
      }
    } catch (error) {
      console.error(`  ❌ Błąd WebP: ${imagePath} - ${error.message}`);
    }
  }

  const totalSavings = totalSizeBefore - totalSizeAfter;
  const savingsPercent = totalSizeBefore > 0 ? Math.round((totalSavings / totalSizeBefore) * 100) : 0;

  console.log('\n🎉 KONWERSJA WebP ZAKOŃCZONA!');
  console.log('='.repeat(35));
  console.log(`📊 Skonwertowano: ${convertedCount} obrazków`);
  console.log(`💾 Rozmiar oryginalny: ${Math.round(totalSizeBefore / 1024 / 1024 * 100) / 100} MB`);
  console.log(`💾 Rozmiar WebP: ${Math.round(totalSizeAfter / 1024 / 1024 * 100) / 100} MB`);
  console.log(`🎯 Oszczędności WebP: ${Math.round(totalSavings / 1024 / 1024 * 100) / 100} MB (${savingsPercent}%)`);
}

// Convert to AVIF format (next-gen format)
async function convertToAVIF() {
  await loadImageminModules();

  console.log('\n🚀 KONWERSJA DO AVIF - najnowszy format!');
  console.log('='.repeat(45));
  
  const images = findImages(config.inputDir, ['.jpg', '.jpeg', '.png']);
  let convertedCount = 0;
  let totalSizeBefore = 0;
  let totalSizeAfter = 0;

  for (const imagePath of images) {
    try {
      const sizeBefore = fs.statSync(imagePath).size;
      totalSizeBefore += sizeBefore;
      
      const relativePath = path.relative(config.inputDir, imagePath);
      const avifPath = path.join(config.avifDir, relativePath.replace(/\.(jpg|jpeg|png)$/i, '.avif'));
      const avifDir = path.dirname(avifPath);
      
      if (!fs.existsSync(avifDir)) {
        fs.mkdirSync(avifDir, { recursive: true });
      }

      const result = await imagemin([imagePath], {
        destination: avifDir,
        plugins: [
          imageminAvif({
            quality: config.quality.avif,
            effort: 9, // Maximum effort
          }),
        ],
      });

      if (result && result.length > 0) {
        const sizeAfter = result[0].data.length;
        totalSizeAfter += sizeAfter;
        const savings = Math.round(((sizeBefore - sizeAfter) / sizeBefore) * 100);
        
        console.log(`  ✅ ${relativePath} → AVIF: ${Math.round(sizeBefore/1024)}KB → ${Math.round(sizeAfter/1024)}KB (${savings}% mniej)`);
        convertedCount++;
      }
    } catch (error) {
      console.error(`  ❌ Błąd AVIF: ${imagePath} - ${error.message}`);
    }
  }

  const totalSavings = totalSizeBefore - totalSizeAfter;
  const savingsPercent = totalSizeBefore > 0 ? Math.round((totalSavings / totalSizeBefore) * 100) : 0;

  console.log('\n🎉 KONWERSJA AVIF ZAKOŃCZONA!');
  console.log('='.repeat(35));
  console.log(`📊 Skonwertowano: ${convertedCount} obrazków`);
  console.log(`💾 Rozmiar oryginalny: ${Math.round(totalSizeBefore / 1024 / 1024 * 100) / 100} MB`);
  console.log(`💾 Rozmiar AVIF: ${Math.round(totalSizeAfter / 1024 / 1024 * 100) / 100} MB`);
  console.log(`🎯 Oszczędności AVIF: ${Math.round(totalSavings / 1024 / 1024 * 100) / 100} MB (${savingsPercent}%)`);
}

// Main function
async function main() {
  const startTime = Date.now();
  
  console.log('🚀 BAKASANA - OPTYMALIZACJA OBRAZKÓW');
  console.log('💡 Największy wpływ na performance strony!');
  console.log('='.repeat(60));
  
  try {
    await compressOriginalImages();
    await convertToWebP();
    await convertToAVIF();
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    console.log('\n🎊 WSZYSTKO GOTOWE!');
    console.log('='.repeat(25));
    console.log(`⏱️ Czas wykonania: ${duration} sekund`);
    console.log('📁 Sprawdź foldery:');
    console.log('   • /public/images/optimized/ - skompresowane oryginały');
    console.log('   • /public/images/webp/ - format WebP');
    console.log('   • /public/images/avif/ - format AVIF (najnowszy)');
    console.log('\n💡 Następne kroki:');
    console.log('   1. Użyj obrazków z /optimized/ zamiast oryginalnych');
    console.log('   2. Zaimplementuj <picture> element z WebP/AVIF fallback');
    console.log('   3. Sprawdź performance w Lighthouse - powinno być >95!');
    
  } catch (error) {
    console.error('❌ Błąd podczas optymalizacji:', error.message);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { compressOriginalImages, convertToWebP, convertToAVIF };
