import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // Test notification data
    const testNotification = {
      title: 'Test BAKASANA PWA 🧘‍♀️',
      body: 'Powiadomienia push działają poprawnie! Gotowe na retreaty?',
      icon: '/apple-touch-icon.png',
      badge: '/favicon-32x32.png',
      image: '/images/og-image.jpg',
      data: {
        url: '/retreaty',
        type: 'test',
        timestamp: new Date().toISOString()
      },
      actions: [
        {
          action: 'view',
          title: 'Zob<PERSON>z retreaty',
          icon: '/favicon-16x16.png'
        },
        {
          action: 'dismiss',
          title: '<PERSON>amknij',
          icon: '/favicon-16x16.png'
        }
      ],
      requireInteraction: false,
      silent: false,
      tag: 'bakasana-test'
    };

    // Send test notification using the send API
    const sendResponse = await fetch(new URL('/api/push/send', request.url), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testNotification),
    });

    const sendResult = await sendResponse.json();

    if (sendResponse.ok) {
      return NextResponse.json({
        success: true,
        message: 'Test notification sent successfully',
        notification: testNotification,
        result: sendResult
      });
    } else {
      return NextResponse.json(
        { 
          error: 'Failed to send test notification',
          details: sendResult
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Test notification error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to send test notification',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint for development testing
export async function GET(request) {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Test endpoint only available in development' },
      { status: 403 }
    );
  }

  return NextResponse.json({
    message: 'Push notification test endpoint',
    usage: 'POST to this endpoint to send a test notification',
    endpoints: {
      subscribe: '/api/push/subscribe',
      unsubscribe: '/api/push/unsubscribe',
      send: '/api/push/send',
      test: '/api/push/test'
    },
    environment: {
      vapidConfigured: !!(process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY),
      nodeEnv: process.env.NODE_ENV
    }
  });
}
