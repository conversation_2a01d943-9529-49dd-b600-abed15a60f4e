// Common binary MIME types
// This file also used to generate ./binary-extension.js via npm run vendor
module.exports = [
  'application/octet-stream',
  // Docs
  'application/epub+zip',
  'application/msword',
  'application/pdf',
  'application/rtf',
  'application/vnd.amazon.ebook',
  'application/vnd.ms-excel',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  // Fonts
  'font/otf',
  'font/woff',
  'font/woff2',
  // Images
  'image/avif',
  'image/bmp',
  'image/gif',
  'image/heic-sequence',
  'image/heic',
  'image/heif-sequence',
  'image/heif',
  'image/jp2',
  'image/jpeg',
  'image/jpg',
  'image/jpg2',
  'image/png',
  'image/tiff',
  'image/vnd.microsoft.icon',
  'image/webp',
  // Audio
  'audio/3gpp',
  'audio/aac',
  'audio/basic',
  'audio/mpeg',
  'audio/ogg',
  'audio/wavaudio/webm',
  'audio/x-aiff',
  'audio/x-midi',
  'audio/x-wav',
  // Video
  'video/3gpp-tt',
  'video/3gpp',
  'video/3gpp2',
  'video/h264',
  'video/h265',
  'video/h266',
  'video/jpeg2000',
  'video/mj2',
  'video/mp2t',
  'video/mp4',
  'video/mpeg',
  'video/mpeg4-generic',
  'video/ogg',
  'video/quicktime',
  'video/webm',
  'video/x-msvideo',
  // Archives
  'application/java-archive',
  'application/vnd.apple.installer+xml',
  'application/x-7z-compressed',
  'application/x-apple-diskimage',
  'application/x-bzip',
  'application/x-bzip2',
  'application/x-gzip',
  'application/x-java-archive',
  'application/x-rar-compressed',
  'application/x-tar',
  'application/x-zip',
  'application/zip',
]
