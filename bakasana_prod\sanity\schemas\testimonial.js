// sanity/schemas/testimonial.js
export default {
  name: 'testimonial',
  title: 'Opinie uczestników',
  type: 'document',
  fields: [
    {
      name: 'name',
      title: '<PERSON><PERSON><PERSON> i nazwisko',
      type: 'string',
      validation: Rule => Rule.required(),
    },
    {
      name: 'photo',
      title: '<PERSON>d<PERSON><PERSON><PERSON>',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: 'alt',
          title: 'Tekst alternatywny',
          type: 'string',
        },
      ],
    },
    {
      name: 'content',
      title: 'Treść opinii',
      type: 'text',
      rows: 4,
      validation: Rule => Rule.required().min(50).max(500),
    },
    {
      name: 'rating',
      title: '<PERSON><PERSON><PERSON> (1-5 gwiazdek)',
      type: 'number',
      validation: Rule => Rule.required().min(1).max(5),
      options: {
        list: [
          { title: '⭐ 1 gwiazdka', value: 1 },
          { title: '⭐⭐ 2 gwiazdki', value: 2 },
          { title: '⭐⭐⭐ 3 gwiazdki', value: 3 },
          { title: '⭐⭐⭐⭐ 4 gwiazdki', value: 4 },
          { title: '⭐⭐⭐⭐⭐ 5 gwiazdek', value: 5 },
        ],
      },
    },
    {
      name: 'retreatDate',
      title: 'Data wyjazdu',
      type: 'string',
      description: 'Np. "Maj 2024" lub "Sierpień 2023"',
    },
    {
      name: 'location',
      title: 'Miasto/Region',
      type: 'string',
      description: 'Skąd pochodzi osoba, np. "Warszawa", "Kraków"',
    },
    {
      name: 'retreat',
      title: 'Retreat',
      type: 'reference',
      to: [{ type: 'retreat' }],
      description: 'Opcjonalne - powiąż z konkretnym retreatem',
    },
    {
      name: 'featured',
      title: 'Wyróżniona opinia',
      type: 'boolean',
      description: 'Czy pokazywać na stronie głównej?',
      initialValue: false,
    },
    {
      name: 'approved',
      title: 'Zatwierdzona',
      type: 'boolean',
      description: 'Czy opinia jest zatwierdzona do publikacji?',
      initialValue: true,
    },
    {
      name: 'source',
      title: 'Źródło opinii',
      type: 'string',
      options: {
        list: [
          { title: 'Bezpośrednio od klienta', value: 'direct' },
          { title: 'Facebook', value: 'facebook' },
          { title: 'Instagram', value: 'instagram' },
          { title: 'Google Reviews', value: 'google' },
          { title: 'Email', value: 'email' },
          { title: 'Inne', value: 'other' },
        ],
      },
      initialValue: 'direct',
    },
    {
      name: 'contactPermission',
      title: 'Zgoda na kontakt',
      type: 'boolean',
      description: 'Czy osoba wyraziła zgodę na publikację opinii?',
      initialValue: true,
    },
  ],
  preview: {
    select: {
      name: 'name',
      content: 'content',
      rating: 'rating',
      featured: 'featured',
      approved: 'approved',
      media: 'photo',
    },
    prepare(selection) {
      const { name, content, rating, featured, approved } = selection;
      const stars = '⭐'.repeat(rating || 0);
      const status = [];

      if (featured) status.push('🌟 Wyróżniona');
      if (!approved) status.push('⏳ Oczekuje');

      return {
        title: name,
        subtitle: `${stars} | ${content?.substring(0, 60)}...${status.length ? ' | ' + status.join(', ') : ''}`,
        media: selection.media,
      };
    },
  },
  orderings: [
    {
      title: 'Najnowsze',
      name: 'newest',
      by: [{ field: '_createdAt', direction: 'desc' }],
    },
    {
      title: 'Najwyższa ocena',
      name: 'highestRating',
      by: [
        { field: 'rating', direction: 'desc' },
        { field: '_createdAt', direction: 'desc' },
      ],
    },
    {
      title: 'Wyróżnione',
      name: 'featured',
      by: [
        { field: 'featured', direction: 'desc' },
        { field: 'rating', direction: 'desc' },
      ],
    },
  ],
};
