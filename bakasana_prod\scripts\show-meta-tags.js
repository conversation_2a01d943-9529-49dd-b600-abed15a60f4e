#!/usr/bin/env node

/**
 * SEO Meta Tags Preview - Bakasana Studio
 * 
 * This script shows what meta tags will be generated
 * based on current environment configuration
 */

const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

const log = (color, message) => console.log(`${colors[color]}${message}${colors.reset}`);

function parseEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    log('yellow', '⚠️  .env.local file not found');
    return {};
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    if (line.trim() && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

function showMetaTags() {
  log('cyan', '\n' + '='.repeat(60));
  log('cyan', '🔍 SEO META TAGS PREVIEW - BAKASANA STUDIO');
  log('cyan', '='.repeat(60));
  
  const env = parseEnvFile();
  
  log('magenta', '\n📋 Generated Meta Tags:');
  log('white', '-'.repeat(40));
  
  // Site verification tags from Next.js metadata
  if (env.GOOGLE_SITE_VERIFICATION) {
    log('green', `<meta name="google-site-verification" content="${env.GOOGLE_SITE_VERIFICATION}" />`);
  } else {
    log('yellow', `<meta name="google-site-verification" content="[NOT CONFIGURED]" />`);
  }
  
  if (env.BING_VERIFICATION) {
    log('green', `<meta name="msvalidate.01" content="${env.BING_VERIFICATION}" />`);
  } else {
    log('yellow', `<meta name="msvalidate.01" content="[NOT CONFIGURED]" />`);
  }
  
  if (env.YANDEX_VERIFICATION) {
    log('green', `<meta name="yandex-verification" content="${env.YANDEX_VERIFICATION}" />`);
  } else {
    log('yellow', `<meta name="yandex-verification" content="[NOT CONFIGURED]" />`);
  }
  
  // Additional verification tags from layout.jsx
  if (env.FACEBOOK_VERIFICATION) {
    log('green', `<meta name="facebook-domain-verification" content="${env.FACEBOOK_VERIFICATION}" />`);
  } else {
    log('yellow', `<meta name="facebook-domain-verification" content="[NOT CONFIGURED]" />`);
  }
  
  if (env.PINTEREST_VERIFICATION) {
    log('green', `<meta name="p:domain_verify" content="${env.PINTEREST_VERIFICATION}" />`);
  } else {
    log('yellow', `<meta name="p:domain_verify" content="[NOT CONFIGURED]" />`);
  }
  
  log('white', '-'.repeat(40));
  
  // Show site configuration
  log('magenta', '\n🌐 Site Configuration:');
  log('white', '-'.repeat(30));
  
  if (env.NEXT_PUBLIC_SITE_URL) {
    log('green', `Site URL: ${env.NEXT_PUBLIC_SITE_URL}`);
  } else {
    log('yellow', `Site URL: [NOT CONFIGURED]`);
  }
  
  if (env.NEXT_PUBLIC_BASE_URL) {
    log('green', `Base URL: ${env.NEXT_PUBLIC_BASE_URL}`);
  } else {
    log('yellow', `Base URL: [NOT CONFIGURED]`);
  }
  
  log('white', '-'.repeat(30));
  
  // Show verification status
  log('magenta', '\n📊 Verification Status:');
  log('white', '-'.repeat(25));
  
  const verificationCodes = [
    { name: 'Google Search Console', key: 'GOOGLE_SITE_VERIFICATION', required: true },
    { name: 'Bing Webmaster Tools', key: 'BING_VERIFICATION', required: true },
    { name: 'Yandex Webmaster', key: 'YANDEX_VERIFICATION', required: false },
    { name: 'Facebook Domain', key: 'FACEBOOK_VERIFICATION', required: false },
    { name: 'Pinterest Business', key: 'PINTEREST_VERIFICATION', required: false }
  ];
  
  let configuredCount = 0;
  let requiredCount = 0;
  
  verificationCodes.forEach(item => {
    const isConfigured = env[item.key] && !env[item.key].includes('your_');
    const status = isConfigured ? '✅' : (item.required ? '❌' : '⚠️');
    const statusText = isConfigured ? 'Configured' : (item.required ? 'Required' : 'Optional');
    
    log('white', `${status} ${item.name}: ${statusText}`);
    
    if (isConfigured) configuredCount++;
    if (item.required) requiredCount++;
  });
  
  log('white', '-'.repeat(25));
  log(configuredCount >= requiredCount ? 'green' : 'yellow', 
      `Configured: ${configuredCount}/${verificationCodes.length} (Required: ${requiredCount})`);
  
  // Show next steps
  log('magenta', '\n🚀 Next Steps:');
  log('white', '-'.repeat(20));
  
  if (configuredCount < requiredCount) {
    log('yellow', '1. Configure missing verification codes in .env.local');
    log('yellow', '2. Run: npm run build');
    log('yellow', '3. Run: npm run start');
    log('yellow', '4. Verify in each platform');
  } else {
    log('green', '1. Configuration looks good!');
    log('green', '2. Run: npm run build');
    log('green', '3. Run: npm run start');
    log('green', '4. Verify in each platform');
  }
  
  log('cyan', '\n' + '='.repeat(60));
  log('cyan', '🔗 View source at: view-source:' + (env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'));
  log('cyan', '='.repeat(60) + '\n');
}

// Run the preview
if (require.main === module) {
  showMetaTags();
}

module.exports = { showMetaTags, parseEnvFile };