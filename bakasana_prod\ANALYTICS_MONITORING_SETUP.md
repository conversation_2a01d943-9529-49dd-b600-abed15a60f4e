# 📊 Analytics & Monitoring Complete Setup

## 🚀 Overview

Comprehensive analytics and monitoring system for BAKASANA retreat website including:

- **Google Analytics 4** with enhanced ecommerce tracking
- **Hotjar** for user behavior analysis
- **Sentry** for error tracking and performance monitoring
- **Performance monitoring** with GTmetrix, Pingdom, UptimeRobot APIs
- **Custom retreat-specific tracking** for business insights

## 🛠️ Quick Setup

### 1. Install Dependencies
```bash
# Already installed in your project:
npm install @sentry/nextjs mixpanel-browser
```

### 2. Configure Environment Variables
```bash
# Run the interactive setup
npm run setup:analytics

# Or manually add to .env.local:
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_site_id
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
NEXT_PUBLIC_MIXPANEL_TOKEN=your_mixpanel_token
NEXT_PUBLIC_FB_PIXEL_ID=your_facebook_pixel_id

# Performance monitoring APIs (optional)
GTMETRIX_API_KEY=your_gtmetrix_api_key
PINGDOM_API_KEY=your_pingdom_api_key
UPTIMEROBOT_API_KEY=your_uptimerobot_api_key
```

### 3. Add to Layout
```jsx
// In your layout.jsx
import AnalyticsProvider from '@/components/Analytics/AnalyticsProvider';

export default function RootLayout({ children }) {
  return (
    <html lang="pl">
      <body>
        <AnalyticsProvider>
          {children}
        </AnalyticsProvider>
      </body>
    </html>
  );
}
```

## 📈 Analytics Implementation

### Google Analytics 4 Features
- ✅ **Enhanced Ecommerce**: Retreat booking funnel tracking
- ✅ **Custom Events**: Retreat interest, form interactions
- ✅ **User Journey Tracking**: From awareness to conversion
- ✅ **Custom Parameters**: Retreat-specific data
- ✅ **Conversion Tracking**: Booking completions

### Hotjar Integration
- ✅ **Heatmaps**: User interaction patterns
- ✅ **Session Recordings**: Booking process analysis
- ✅ **Custom Events**: Retreat-specific interactions
- ✅ **User Identification**: Track returning visitors

### Sentry Error Tracking
- ✅ **Real-time Error Monitoring**: JavaScript and API errors
- ✅ **Performance Monitoring**: Transaction tracking
- ✅ **Custom Error Context**: Retreat and booking specific
- ✅ **Release Tracking**: Error tracking across deployments
- ✅ **Session Replay**: Visual error reproduction

## 🎯 Custom Event Tracking

### Retreat Interest Tracking
```javascript
import { analytics } from '@/components/Analytics/AnalyticsProvider';

// Track when user views retreat details
analytics.trackRetreatInterest('Bali Retreat 2025', 'view_details');

// Track gallery interactions
analytics.trackRetreatInterest('Sri Lanka Retreat 2025', 'gallery_view');

// Track program downloads
analytics.trackRetreatInterest('Bali Retreat 2025', 'program_download');
```

### Booking Funnel Tracking
```javascript
// Track booking steps
analytics.trackBookingStep('step_1_details', 'Bali Retreat 2025', {
  price: 3400,
  value: 3400
});

analytics.trackBookingStep('step_2_payment', 'Bali Retreat 2025', {
  payment_method: 'card'
});

// Track conversion
analytics.trackConversion('purchase', 3400, 'Bali Retreat 2025');
```

### Form Interaction Tracking
```javascript
// Track form interactions
analytics.trackFormInteraction('contact_form', 'focus', 'email');
analytics.trackFormInteraction('booking_form', 'submit');
analytics.trackFormInteraction('newsletter', 'signup');
```

### Error Tracking
```javascript
// Track booking errors
window.trackBookingError(error, {
  retreatName: 'Bali Retreat 2025',
  step: 'payment',
  userId: 'user123'
});

// Track form errors
window.trackFormError(error, {
  formName: 'contact_form',
  fieldName: 'email',
  validationRule: 'email_format'
});
```

## ⚡ Performance Monitoring

### Automatic Tracking
- **Core Web Vitals**: LCP, FID, CLS, TTFB
- **Resource Performance**: Slow loading assets
- **User Interactions**: Click, scroll, keyboard response times
- **Memory Usage**: JavaScript heap monitoring
- **Network Requests**: API response times and errors

### External Monitoring
- **GTmetrix**: Automated performance testing
- **Pingdom**: Uptime and response time monitoring
- **UptimeRobot**: Additional uptime monitoring

### API Endpoint
```javascript
// Performance data is automatically sent to:
POST /api/performance/report

// Retrieve performance data:
GET /api/performance/report?page=/retreaty&days=7
```

## 🔧 Available NPM Scripts

```bash
# Setup analytics interactively
npm run setup:analytics

# Monitor performance
npm run monitor:performance

# Validate HTML and SEO
npm run validate:all

# Apply quick fixes
npm run fix:html
```

## 📊 Key Metrics Dashboard

### Business Metrics
- **Retreat Page Views**: Track interest in specific retreats
- **Booking Funnel Conversion**: Step-by-step conversion rates
- **Contact Form Submissions**: Lead generation tracking
- **Gallery Engagement**: Visual content interaction
- **Program Downloads**: Interest in retreat details

### Technical Metrics
- **Page Load Times**: LCP < 2.5s target
- **Interactivity**: FID < 100ms target
- **Visual Stability**: CLS < 0.1 target
- **Error Rates**: < 1% error rate target
- **Uptime**: 99.9% availability target

### User Experience
- **Session Duration**: Time spent on retreat pages
- **Bounce Rate**: Single-page session percentage
- **Mobile Performance**: Mobile vs desktop metrics
- **Geographic Performance**: Performance by location

## 🚨 Monitoring Alerts

### Performance Alerts
- Page load time > 3 seconds
- Error rate > 1%
- Uptime < 99.9%
- Core Web Vitals failing

### Business Alerts
- Booking form errors
- Payment processing issues
- High bounce rate on retreat pages
- Contact form failures

## 🔒 Privacy & Consent

### GDPR Compliance
```javascript
import { consentManager } from '@/components/Analytics/AnalyticsProvider';

// Grant consent
consentManager.grantConsent();

// Revoke consent
consentManager.revokeConsent();

// Check consent status
const hasConsent = consentManager.hasConsent();
```

### Do Not Track Support
- Automatically respects browser DNT settings
- User consent management
- Data anonymization options

## 📁 File Structure

```
src/
├── components/
│   ├── Analytics/
│   │   ├── AnalyticsProvider.jsx      # Main analytics wrapper
│   │   ├── EnhancedAnalytics.jsx      # GA4, Hotjar, Mixpanel
│   │   ├── ErrorTracking.jsx          # Sentry integration
│   │   └── GoogleAnalytics.jsx        # Legacy GA component
│   └── Performance/
│       └── PerformanceMonitoring.jsx  # Core Web Vitals tracking
├── app/
│   └── api/
│       └── performance/
│           └── report/
│               └── route.js            # Performance API endpoint
└── scripts/
    └── setup-analytics.js              # Interactive setup script
```

## 🔗 External Service Setup

### Google Analytics 4
1. Create GA4 property at [analytics.google.com](https://analytics.google.com)
2. Get Measurement ID (G-XXXXXXXXXX)
3. Set up Enhanced Ecommerce
4. Configure conversion goals

### Hotjar
1. Sign up at [hotjar.com](https://www.hotjar.com)
2. Create new site
3. Get Site ID
4. Configure heatmaps and recordings

### Sentry
1. Create account at [sentry.io](https://sentry.io)
2. Create new project
3. Get DSN
4. Configure error filtering

### Performance Monitoring
1. **GTmetrix**: Sign up at [gtmetrix.com](https://gtmetrix.com)
2. **Pingdom**: Sign up at [pingdom.com](https://www.pingdom.com)
3. **UptimeRobot**: Sign up at [uptimerobot.com](https://uptimerobot.com)

## 📝 Next Steps

1. ✅ **Run Setup**: `npm run setup:analytics`
2. ✅ **Verify Integration**: Check all tools are receiving data
3. ✅ **Configure Dashboards**: Set up custom dashboards
4. ✅ **Set Up Alerts**: Configure monitoring notifications
5. ✅ **Team Training**: Ensure team knows how to use tools
6. ✅ **Regular Reviews**: Schedule weekly analytics reviews

---

*Setup completed with comprehensive analytics and monitoring for BAKASANA retreat website*
