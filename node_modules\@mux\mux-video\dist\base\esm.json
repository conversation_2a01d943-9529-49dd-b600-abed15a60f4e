{"inputs": {"src/env.ts": {"bytes": 391, "imports": [], "format": "esm"}, "src/assets/mux-logo.ts": {"bytes": 1804, "imports": [], "format": "esm"}, "src/types.ts": {"bytes": 1169, "imports": [{"path": "custom-media-element", "kind": "import-statement", "external": true}, {"path": "@mux/playback-core", "kind": "import-statement", "external": true}], "format": "esm"}, "src/base.ts": {"bytes": 24790, "imports": [{"path": "@mux/playback-core", "kind": "import-statement", "external": true}, {"path": "src/env.ts", "kind": "import-statement", "original": "./env"}, {"path": "custom-media-element", "kind": "import-statement", "external": true}, {"path": "src/assets/mux-logo.ts", "kind": "import-statement", "original": "./assets/mux-logo.js"}, {"path": "src/types.ts", "kind": "import-statement", "original": "./types.js"}, {"path": "<runtime>", "kind": "import-statement", "external": true}], "format": "esm"}}, "outputs": {"dist/base.mjs.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 42949}, "dist/base.mjs": {"imports": [{"path": "@mux/playback-core", "kind": "import-statement", "external": true}, {"path": "custom-media-element", "kind": "import-statement", "external": true}], "exports": ["Attributes", "Events", "MediaError", "MuxVideoBaseElement", "generatePlayerInitTime", "playerSoftwareName", "playerSoftwareVersion"], "entryPoint": "src/base.ts", "inputs": {"src/base.ts": {"bytesInOutput": 12983}, "src/env.ts": {"bytesInOutput": 68}, "src/assets/mux-logo.ts": {"bytesInOutput": 1786}}, "bytes": 15407}}}