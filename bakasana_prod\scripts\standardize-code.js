#!/usr/bin/env node

/**
 * BAKASANA - Code Standardization Script
 * Standardizes imports, naming conventions, and code patterns
 */

const ImportStandardizer = require('./utils/ImportStandardizer');
const Logger = require('./utils/Logger');

class CodeStandardizer {
  constructor() {
    this.logger = new Logger('CodeStandardizer', { colors: true });
    this.importStandardizer = new ImportStandardizer();
  }

  async run() {
    this.logger.section('BAKASANA Code Standardization');

    try {
      // Step 1: Standardize imports
      this.logger.subsection('Step 1: Import Standardization');
      await this.importStandardizer.standardizeAllImports();

      // Step 2: Fix naming conventions (future enhancement)
      this.logger.subsection('Step 2: Naming Conventions');
      this.logger.info(
        'Naming convention fixes will be implemented in next version'
      );

      // Step 3: Code pattern standardization (future enhancement)
      this.logger.subsection('Step 3: Code Patterns');
      this.logger.info(
        'Code pattern standardization will be implemented in next version'
      );

      this.logger.success('Code standardization completed successfully!');
    } catch (error) {
      this.logger.error('Code standardization failed:', error);
      process.exit(1);
    }
  }
}

// Run if called directly
if (require.main === module) {
  const standardizer = new CodeStandardizer();
  standardizer.run().catch(console.error);
}

module.exports = CodeStandardizer;
