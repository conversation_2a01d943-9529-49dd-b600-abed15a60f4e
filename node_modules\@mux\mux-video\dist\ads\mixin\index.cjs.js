"use strict";var X=Object.defineProperty;var fe=Object.getOwnPropertyDescriptor;var ye=Object.getOwnPropertyNames;var _e=Object.getPrototypeOf,Re=Object.prototype.hasOwnProperty;var Ce=Reflect.get;var ge=l=>{throw TypeError(l)};var Ie=(l,u)=>{for(var i in u)X(l,i,{get:u[i],enumerable:!0})},Pe=(l,u,i,n)=>{if(u&&typeof u=="object"||typeof u=="function")for(let s of ye(u))!Re.call(l,s)&&s!==i&&X(l,s,{get:()=>u[s],enumerable:!(n=fe(u,s))||n.enumerable});return l};var we=l=>Pe(X({},"__esModule",{value:!0}),l);var Z=(l,u,i)=>u.has(l)||ge("Cannot "+i);var e=(l,u,i)=>(Z(l,u,"read from private field"),i?i.call(l):u.get(l)),E=(l,u,i)=>u.has(l)?ge("Cannot add the same private member more than once"):u instanceof WeakSet?u.add(l):u.set(l,i),o=(l,u,i,n)=>(Z(l,u,"write to private field"),n?n.call(l,i):u.set(l,i),i),A=(l,u,i)=>(Z(l,u,"access private method"),i);var Y=(l,u,i)=>Ce(_e(l),i,u);var be={};Ie(be,{AdEvent:()=>g,AdsVideoMixin:()=>Se,Attributes:()=>y,Events:()=>h});module.exports=we(be);var g=class extends Event{},h={AD_REQUEST:"adrequest",AD_RESPONSE:"adresponse",AD_BREAK_START:"adbreakstart",AD_FIRST_QUARTILE:"adfirstquartile",AD_MIDPOINT:"admidpoint",AD_THIRD_QUARTILE:"adthirdquartile",AD_ENDED:"adended",AD_BREAK_END:"adbreakend",AD_ERROR:"aderror",AD_PLAY:"adplay",AD_PLAYING:"adplaying",AD_PAUSE:"adpause",AD_IMPRESSION:"adimpression",AD_CLICK:"adclick",AD_SKIP:"adskip",AD_CLOSE:"adclose",PLAY:"play",PLAYING:"playing",PAUSE:"pause",VOLUME_CHANGE:"volumechange",TIME_UPDATE:"timeupdate",DURATION_CHANGE:"durationchange",WAITING:"waiting"};var N,B,W=class{constructor(u,i){E(this,N);E(this,B);o(this,N,u),o(this,B,i)}isLinear(){return e(this,N).isLinear()}isCustomPlaybackUsed(){return e(this,B).isCustomPlaybackUsed()}};N=new WeakMap,B=new WeakMap;var w,p,D,R,C,c,d,I,x,S,K,b,P,f,k,T,Ee,H,V,z,q,G,j,$,U=class extends EventTarget{constructor(i){var n;super();E(this,T);E(this,w);E(this,p);E(this,D);E(this,R);E(this,C);E(this,c);E(this,d);E(this,I);E(this,x);E(this,S);E(this,K,!1);E(this,b,!1);E(this,P,!1);E(this,f,!1);E(this,k,0);E(this,H,()=>{var i;o(this,P,!0),e(this,f)&&!((i=e(this,d))!=null&&i.isCustomPlaybackUsed())&&(console.warn("Video play prevented during ad break"),e(this,p).pause())});E(this,V,()=>{var i;e(this,f)&&!((i=e(this,d))!=null&&i.isCustomPlaybackUsed())&&e(this,p).currentTime!==e(this,k)&&(console.warn("Seek prevented during ad break"),e(this,p).currentTime=e(this,k),e(this,p).dispatchEvent(new Event("timeupdate")))});E(this,z,()=>{var i;(i=e(this,c))==null||i.contentComplete()});E(this,q,i=>{var n;console.error("Ad error",(n=i.getError())==null?void 0:n.getMessage()),this.dispatchEvent(new g(h.AD_ERROR)),A(this,T,G).call(this)});E(this,j,async i=>{var s,m,r,O,ee,te,ie,se,ne,ae,re,de,oe,he,L,le,J,t,a,v,ue;let n=new google.ima.AdsRenderingSettings;o(this,d,i.getAdsManager(e(this,p),n)),(s=e(this,d))==null||s.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,e(this,q)),(m=e(this,d))==null||m.addEventListener(google.ima.AdEvent.Type.LOADED,()=>{this.dispatchEvent(new g(h.AD_RESPONSE))}),(r=e(this,d))==null||r.addEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,_=>{if(o(this,I,_.getAd()),!e(this,I)||!e(this,d)){console.warn("Google IMA ad is undefined");return}o(this,f,!0),o(this,k,e(this,p).currentTime||0),o(this,x,new W(e(this,I),e(this,d))),this.dispatchEvent(new g(h.AD_BREAK_START))}),(O=e(this,d))==null||O.addEventListener(google.ima.AdEvent.Type.CLICK,()=>{this.dispatchEvent(new g(h.AD_CLICK))}),(ee=e(this,d))==null||ee.addEventListener(google.ima.AdEvent.Type.IMPRESSION,()=>{this.dispatchEvent(new g(h.AD_IMPRESSION))}),(te=e(this,d))==null||te.addEventListener(google.ima.AdEvent.Type.SKIPPED,()=>{this.dispatchEvent(new g(h.AD_SKIP))}),(ie=e(this,d))==null||ie.addEventListener(google.ima.AdEvent.Type.USER_CLOSE,()=>{this.dispatchEvent(new g(h.AD_CLOSE))}),(se=e(this,d))==null||se.addEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE,()=>{this.dispatchEvent(new g(h.AD_FIRST_QUARTILE))}),(ne=e(this,d))==null||ne.addEventListener(google.ima.AdEvent.Type.MIDPOINT,()=>{this.dispatchEvent(new g(h.AD_MIDPOINT))}),(ae=e(this,d))==null||ae.addEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE,()=>{this.dispatchEvent(new g(h.AD_THIRD_QUARTILE))}),(re=e(this,d))==null||re.addEventListener(google.ima.AdEvent.Type.COMPLETE,_=>{A(this,T,G).call(this,_)}),(de=e(this,d))==null||de.addEventListener(google.ima.AdEvent.Type.SKIPPED,_=>{A(this,T,G).call(this,_)}),(oe=e(this,d))==null||oe.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,()=>{o(this,f,!1),this.dispatchEvent(new g(h.AD_BREAK_END))}),(he=e(this,d))==null||he.addEventListener(google.ima.AdEvent.Type.STARTED,()=>{this.dispatchEvent(new g(h.AD_PLAYING)),this.dispatchEvent(new g(h.PLAYING))}),(L=e(this,d))==null||L.addEventListener(google.ima.AdEvent.Type.PAUSED,()=>{o(this,b,!0),this.dispatchEvent(new g(h.AD_PAUSE)),this.dispatchEvent(new g(h.PAUSE))}),(le=e(this,d))==null||le.addEventListener(google.ima.AdEvent.Type.RESUMED,()=>{o(this,b,!1),this.dispatchEvent(new g(h.AD_PLAY)),this.dispatchEvent(new g(h.PLAY)),this.dispatchEvent(new g(h.AD_PLAYING)),this.dispatchEvent(new g(h.PLAYING))}),(J=e(this,d))==null||J.addEventListener(google.ima.AdEvent.Type.AD_BUFFERING,()=>{this.dispatchEvent(new g(h.WAITING))}),(t=e(this,d))==null||t.addEventListener(google.ima.AdEvent.Type.AD_PROGRESS,_=>{o(this,S,_.getAdData()),this.dispatchEvent(new g(h.TIME_UPDATE))}),(a=e(this,d))==null||a.addEventListener(google.ima.AdEvent.Type.DURATION_CHANGE,()=>{this.dispatchEvent(new g(h.DURATION_CHANGE))}),(v=e(this,d))==null||v.addEventListener(google.ima.AdEvent.Type.VOLUME_CHANGED,()=>{this.dispatchEvent(new g(h.VOLUME_CHANGE))}),(ue=e(this,d))==null||ue.addEventListener(google.ima.AdEvent.Type.VOLUME_MUTED,()=>{this.dispatchEvent(new g(h.VOLUME_CHANGE))});try{e(this,P)?A(this,T,$).call(this):e(this,p).addEventListener("play",()=>{o(this,P,!0),A(this,T,$).call(this)},{once:!0})}catch{A(this,T,G).call(this)}});o(this,w,i.adContainer),o(this,p,i.videoElement),o(this,D,i.originalSize),o(this,P,!e(this,p).paused),e(this,p).addEventListener("play",e(this,H)),e(this,p).addEventListener("seeking",e(this,V)),e(this,p).addEventListener("ended",e(this,z)),o(this,C,new google.ima.AdDisplayContainer(e(this,w),e(this,p))),o(this,c,new google.ima.AdsLoader(e(this,C))),e(this,c).addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,e(this,q)),e(this,c).addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,e(this,j)),o(this,R,new ResizeObserver(s=>{for(let m of s){let{width:r,height:O}=m.contentRect;r>0&&O>0&&A(this,T,Ee).call(this,r,O)}})),(n=e(this,R))==null||n.observe(e(this,w))}static isSDKAvailable(){return"google"in globalThis&&"ima"in globalThis.google?!0:(console.error("Missing google.ima SDK. Make sure you include it via a script tag."),!1)}destroy(){var i,n,s,m,r;e(this,p).removeEventListener("play",e(this,H)),e(this,p).removeEventListener("seeking",e(this,V)),e(this,p).removeEventListener("ended",e(this,z)),(i=e(this,R))==null||i.disconnect(),o(this,R,void 0),(n=e(this,d))==null||n.stop(),(s=e(this,d))==null||s.destroy(),(m=e(this,C))==null||m.destroy(),(r=e(this,c))==null||r.destroy()}unload(){var i;(i=e(this,d))==null||i.stop(),setTimeout(()=>{var n;(n=e(this,d))==null||n.destroy()},0)}get adsLoader(){return e(this,c)}get ad(){return e(this,x)}get adBreak(){return e(this,f)}get paused(){return e(this,b)}get duration(){var i,n,s,m;return(m=(s=(i=e(this,S))==null?void 0:i.duration)!=null?s:(n=e(this,I))==null?void 0:n.getDuration())!=null?m:NaN}get currentTime(){var i,n;return(n=(i=e(this,S))==null?void 0:i.currentTime)!=null?n:0}get volume(){var i,n;return(n=(i=e(this,d))==null?void 0:i.getVolume())!=null?n:1}set volume(i){var n;(n=e(this,d))==null||n.setVolume(i)}play(){var i;return(i=e(this,d))==null||i.resume(),Promise.resolve()}pause(){var i;(i=e(this,d))==null||i.pause()}initializeAdDisplayContainer(){var i;e(this,K)||(o(this,K,!0),(i=e(this,C))==null||i.initialize())}requestAds(i){var s;e(this,d)&&e(this,d).destroy(),e(this,c)&&e(this,c).contentComplete();let n=new google.ima.AdsRequest;n.adTagUrl=i,(s=e(this,c))==null||s.requestAds(n),this.dispatchEvent(new g(h.AD_REQUEST))}};w=new WeakMap,p=new WeakMap,D=new WeakMap,R=new WeakMap,C=new WeakMap,c=new WeakMap,d=new WeakMap,I=new WeakMap,x=new WeakMap,S=new WeakMap,K=new WeakMap,b=new WeakMap,P=new WeakMap,f=new WeakMap,k=new WeakMap,T=new WeakSet,Ee=function(i,n){var s;o(this,D,{...e(this,D),width:i,height:n}),(s=e(this,d))==null||s.resize(e(this,D).width,e(this,D).height)},H=new WeakMap,V=new WeakMap,z=new WeakMap,q=new WeakMap,G=function(i){this.dispatchEvent(new g(h.AD_ENDED))},j=new WeakMap,$=function(){var i,n;o(this,f,!0),e(this,p).pause(),(i=e(this,d))==null||i.init(e(this,D).width,e(this,D).height),(n=e(this,d))==null||n.start()};var y={AD_TAG_URL:"ad-tag-url",ALLOW_AD_BLOCKER:"allow-ad-blocker"};function Se(l){var i,n,s,m,r,Ae,pe,F,me,ve,Q,ce,Te,M,De,Le;let L=class L extends l{constructor(){super(...arguments);E(this,r);E(this,i,!1);E(this,n);E(this,s);E(this,m)}static get observedAttributes(){return[...super.observedAttributes,"src",y.AD_TAG_URL]}static get Events(){var t;return[...new Set([...(t=super.Events)!=null?t:[],...Object.values(h)])]}connectedCallback(){if(super.connectedCallback(),!U.isSDKAvailable()){console.error("Missing google.ima SDK. Make sure you include it via a script tag."),this.allowAdBlocker||A(this,r,ce).call(this);return}if(!e(this,s)){o(this,s,new U({adContainer:e(this,r,Q),videoElement:this.nativeEl,originalSize:this.getBoundingClientRect()}));for(let t of Object.values(h))e(this,s).addEventListener(t,this)}}attributeChangedCallback(t,a,v){super.attributeChangedCallback(t,a,v),t==="src"&&v!==a&&(o(this,n,void 0),o(this,m,void 0),o(this,i,!1)),t===y.AD_TAG_URL&&A(this,r,F).call(this)}handleEvent(t){var a;if(t instanceof g){A(this,r,Te).call(this,t);return}(a=e(this,s))!=null&&a.adBreak||(t.type==="loadedmetadata"?A(this,r,Ae).call(this):t.type==="play"&&A(this,r,pe).call(this),super.handleEvent(t))}play(){var t;return!U.isSDKAvailable()&&!this.allowAdBlocker?Promise.reject(new Error("Playback failed: Ad experience not available")):(t=e(this,s))!=null&&t.adBreak?e(this,s).play():super.play()}pause(){var t,a;(t=e(this,s))!=null&&t.adBreak&&((a=e(this,s))==null||a.pause()),super.pause()}get ad(){var t;return(t=e(this,s))==null?void 0:t.ad}get adsLoader(){var t;return e(this,s)||console.warn("adsLoader not available yet"),(t=e(this,s))==null?void 0:t.adsLoader}get adTagUrl(){var t;return(t=this.getAttribute(y.AD_TAG_URL))!=null?t:void 0}set adTagUrl(t){t!=this.adTagUrl&&(t==null?this.removeAttribute(y.AD_TAG_URL):this.setAttribute(y.AD_TAG_URL,t))}get allowAdBlocker(){return this.hasAttribute(y.ALLOW_AD_BLOCKER)}set allowAdBlocker(t){this.toggleAttribute(y.ALLOW_AD_BLOCKER,!!t)}get paused(){var t,a,v;return(t=e(this,s))!=null&&t.adBreak?(v=(a=e(this,s))==null?void 0:a.paused)!=null?v:!1:super.paused}get duration(){var t,a,v;return(t=e(this,s))!=null&&t.adBreak?(v=(a=e(this,s))==null?void 0:a.duration)!=null?v:0:super.duration}get currentTime(){var t,a,v;return(t=e(this,s))!=null&&t.adBreak?(v=(a=e(this,s))==null?void 0:a.currentTime)!=null?v:0:super.currentTime}set currentTime(t){var a;(a=e(this,s))!=null&&a.adBreak||(super.currentTime=t)}get volume(){var t,a,v;return(t=e(this,s))!=null&&t.adBreak?(v=(a=e(this,s))==null?void 0:a.volume)!=null?v:0:super.volume}set volume(t){var a;(a=e(this,s))!=null&&a.adBreak&&e(this,s)&&(e(this,s).volume=t),super.volume=t}get muted(){var t,a;return(t=e(this,s))!=null&&t.adBreak?!((a=e(this,s))!=null&&a.volume):super.muted}set muted(t){var a;(a=e(this,s))!=null&&a.adBreak&&e(this,s)&&(e(this,s).volume=t?0:this.volume),super.muted=t}get readyState(){var t;return(t=e(this,s))!=null&&t.adBreak?4:super.readyState}async requestPictureInPicture(){var t;if((t=e(this,s))!=null&&t.adBreak)throw new Error("Cannot use PiP while ads are playing!");return super.requestPictureInPicture()}};i=new WeakMap,n=new WeakMap,s=new WeakMap,m=new WeakMap,r=new WeakSet,Ae=function(){o(this,i,!0),A(this,r,F).call(this)},pe=function(){var t;A(this,r,F).call(this),(t=e(this,s))==null||t.initializeAdDisplayContainer()},F=function(){this.adTagUrl?A(this,r,me).call(this):A(this,r,ve).call(this)},me=async function(){var t;!this.adTagUrl||!this.isConnected||e(this,i)&&this.adTagUrl!==e(this,n)&&(o(this,n,this.adTagUrl),(t=e(this,s))==null||t.requestAds(this.adTagUrl))},ve=function(){var t;(t=e(this,s))==null||t.unload(),o(this,n,void 0)},Q=function(){var t;return(t=this.shadowRoot)==null?void 0:t.getElementById("ad-container")},ce=function(){var t,a;(t=this.shadowRoot)!=null&&t.querySelector("#ima-unavailable-message")||(a=e(this,r,Q))==null||a.insertAdjacentHTML("afterend",`
          <div id="ima-unavailable-message">
            <h4>Ad experience unavailable.</h4>
            <span>This may be due to a missing SDK, network issue, or ad blocker.</span>
          </div>
        `)},Te=function(t){if(t.type===h.AD_BREAK_START){A(this,r,De).call(this),A(this,r,M).call(this,h.DURATION_CHANGE),A(this,r,M).call(this,t.type);return}if(t.type===h.AD_BREAK_END){A(this,r,Le).call(this),A(this,r,M).call(this,h.DURATION_CHANGE),A(this,r,M).call(this,t.type);return}A(this,r,M).call(this,t.type)},M=function(t){this.dispatchEvent(new g(t,{composed:!0}))},De=function(){var t,a;(t=e(this,r,Q))==null||t.classList.toggle("ad-break",!0),(a=this.ad)!=null&&a.isLinear()&&(Y(L.prototype,this,"pause").call(this),o(this,m,{currentTime:Y(L.prototype,this,"currentTime")}))},Le=function(){var t,a;(t=e(this,r,Q))==null||t.classList.toggle("ad-break",!1),(a=e(this,m))!=null&&a.currentTime&&(this.currentTime=e(this,m).currentTime),o(this,m,void 0),setTimeout(()=>{if(!Y(L.prototype,this,"ended"))try{this.play()}catch{}},100)},L.getTemplateHTML=t=>l.getTemplateHTML(t)+`
          <style>
            :host {
              position: relative;
            }

            #ad-container {
              position: absolute;
              top: 0px;
              left: 0px;
              bottom: 0px;
              right: 0px;
              z-index: -1;
              width: 100%;
              height: 100%;
            }

            #ad-container.ad-break {
              z-index: 0;
            }

            #ima-unavailable-message {
              position: absolute;
              inset: 0;
              z-index: 10;
              background: rgba(0, 0, 0, 0.75);
              color: white;
              font-size: 0.9em;
              text-align: center;
              line-height: 1.4;
              align-items: center;
              align-content: center;
              cursor: not-allowed;
            }

            #ima-unavailable-message h4 {
              font-size: 1rem;
              margin: 0;
            }
          </style>
          <div id="ad-container"></div>
        `;let u=L;return u}
//# sourceMappingURL=index.cjs.js.map
