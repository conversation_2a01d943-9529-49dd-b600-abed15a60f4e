#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const glob = require('glob');

console.log('📄 Starting HTML minification...');

// Create dist directory if it doesn't exist
const distDir = path.join(process.cwd(), 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Find HTML files in public directory and any static HTML
const htmlFiles = glob.sync('public/**/*.html', { 
  ignore: ['node_modules/**', 'dist/**']
});

// Also check for any HTML files in the build output
const buildHtmlFiles = glob.sync('.next/**/*.html', { 
  ignore: ['node_modules/**']
});

const allHtmlFiles = [...htmlFiles, ...buildHtmlFiles];

console.log(`Found ${allHtmlFiles.length} HTML files to minify`);

if (allHtmlFiles.length === 0) {
  console.log('⚠️ No HTML files found to minify');
  console.log('💡 Note: Next.js generates HTML dynamically. Run "npm run build" first to generate static HTML files.');
  process.exit(0);
}

let totalOriginalSize = 0;
let totalMinifiedSize = 0;

allHtmlFiles.forEach(file => {
  try {
    console.log(`⚡ Processing ${path.basename(file)}...`);
    
    const originalContent = fs.readFileSync(file, 'utf8');
    totalOriginalSize += originalContent.length;
    
    // Create output filename
    const relativePath = path.relative(process.cwd(), file);
    const outputFile = path.join(distDir, path.basename(file, '.html') + '.min.html');
    
    // Minify with html-minifier-terser
    execSync(`npx html-minifier-terser --collapse-whitespace --remove-comments --minify-css --minify-js --remove-redundant-attributes --remove-script-type-attributes --remove-style-link-type-attributes --use-short-doctype --output "${outputFile}" "${file}"`, { stdio: 'pipe' });
    
    const minifiedContent = fs.readFileSync(outputFile, 'utf8');
    totalMinifiedSize += minifiedContent.length;
    
    const savings = Math.round(((originalContent.length - minifiedContent.length) / originalContent.length) * 100);
    console.log(`  ✅ ${path.basename(file)}: ${originalContent.length} → ${minifiedContent.length} bytes (${savings}% saved)`);
    
  } catch (error) {
    console.error(`  ❌ Error minifying ${file}:`, error.message);
  }
});

// Calculate total savings
const totalSavings = totalOriginalSize > 0 ? Math.round(((totalOriginalSize - totalMinifiedSize) / totalOriginalSize) * 100) : 0;

console.log(`\n📊 HTML Minification Results:`);
console.log(`   Original size: ${Math.round(totalOriginalSize / 1024)} KB`);
console.log(`   Minified size: ${Math.round(totalMinifiedSize / 1024)} KB`);
console.log(`   Space saved: ${totalSavings}%`);
console.log(`   Output directory: ${distDir}`);

console.log('✅ HTML minification completed!');
