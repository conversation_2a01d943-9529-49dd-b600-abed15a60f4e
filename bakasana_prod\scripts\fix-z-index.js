#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const glob = require('glob');

console.log('🔧 BAKASANA Z-INDEX FIXER - Automatyczna naprawa z-index...\n');

// Konfiguracja ścieżek
const srcDir = path.join(__dirname, '../src');
const stylesDir = path.join(srcDir, 'styles');
const appDir = path.join(srcDir, 'app');

// System z-index do zastosowania
const zIndexSystem = {
  // Mapowanie starych wartości na nowe zmienne
  9999: 'var(--z-skip-link)',
  1000: 'var(--z-modal)',
  100: 'var(--z-dropdown)',
  50: 'var(--z-floating)',
  20: 'var(--z-floating)',
  10: 'var(--z-overlay)',
  2: 'var(--z-content)',
  1: 'var(--z-content)',
  0: 'var(--z-base)',
  '-1': 'var(--z-background)',
};

// Zmienne CSS do dodania
const cssVariables = `
  /* ===== Z-INDEX SYSTEM ===== */
  --z-background: -1;
  --z-base: 0;
  --z-content: 1;
  --z-overlay: 10;
  --z-floating: 20;
  --z-dropdown: 100;
  --z-modal: 1000;
  --z-tooltip: 1001;
  --z-skip-link: 9999;
`;

// Znajdź wszystkie pliki CSS
const cssFiles = [
  ...glob.sync(path.join(stylesDir, '*.css').replace(/\\/g, '/')),
  ...glob.sync(path.join(appDir, '*.css').replace(/\\/g, '/')),
].filter(
  file =>
    !file.includes('node_modules') &&
    !file.includes('.backup') &&
    !file.includes('clean')
);

function addZIndexVariables() {
  const mainCssPath = path.join(stylesDir, 'main.css');

  if (!fs.existsSync(mainCssPath)) {
    console.log('❌ Nie znaleziono main.css');
    return false;
  }

  let content = fs.readFileSync(mainCssPath, 'utf8');

  // Sprawdź czy zmienne już istnieją
  if (content.includes('--z-skip-link')) {
    console.log('ℹ️  Zmienne z-index już istnieją w main.css');
    return true;
  }

  // Znajdź sekcję :root i dodaj zmienne
  const rootMatch = content.match(/:root\s*\{([^}]+)\}/);
  if (rootMatch) {
    const rootContent = rootMatch[1];
    const newRootContent = rootContent + cssVariables;
    content = content.replace(rootMatch[0], `:root {${newRootContent}}`);

    // Utwórz kopię zapasową
    fs.copyFileSync(mainCssPath, mainCssPath + '.backup');
    fs.writeFileSync(mainCssPath, content);

    console.log('✅ Dodano zmienne z-index do main.css');
    return true;
  } else {
    console.log('❌ Nie znaleziono sekcji :root w main.css');
    return false;
  }
}

function fixZIndexInFile(filePath) {
  console.log(`🔧 Naprawiam: ${path.relative(process.cwd(), filePath)}`);

  let content = fs.readFileSync(filePath, 'utf8');
  let changes = 0;

  // Utwórz kopię zapasową
  const backupPath = filePath + '.backup';
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(filePath, backupPath);
  }

  // Zastąp z-index wartościami ze zmiennymi
  Object.entries(zIndexSystem).forEach(([oldValue, newValue]) => {
    const regex = new RegExp(`z-index\\s*:\\s*${oldValue}\\s*;?`, 'gi');
    const matches = content.match(regex);

    if (matches) {
      content = content.replace(regex, `z-index: ${newValue};`);
      changes += matches.length;
      console.log(
        `   ✅ Zastąpiono ${matches.length}x z-index: ${oldValue} → ${newValue}`
      );
    }
  });

  // Zapisz zmiany
  if (changes > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`   💾 Zapisano ${changes} zmian\n`);
  } else {
    console.log(`   ℹ️  Brak zmian do zastosowania\n`);
  }

  return changes;
}

function fixSpecialCases() {
  console.log('🎯 Naprawiam specjalne przypadki...\n');

  // Specjalne przypadki do naprawy
  const specialFixes = [
    {
      file: path.join(stylesDir, 'microinteractions.css'),
      search: 'z-index: var(--z-skip-link)',
      replace: 'z-index: var(--z-tooltip)',
      reason: 'cursor-follower nie powinien mieć najwyższego z-index',
    },
    {
      file: path.join(stylesDir, 'navbar-critical-fix.css'),
      search: 'z-index: var(--z-skip-link)',
      replace: 'z-index: var(--z-dropdown)',
      reason: 'navbar overlay nie powinien mieć najwyższego z-index',
    },
  ];

  specialFixes.forEach(fix => {
    if (fs.existsSync(fix.file)) {
      let content = fs.readFileSync(fix.file, 'utf8');

      if (content.includes(fix.search)) {
        content = content.replace(new RegExp(fix.search, 'g'), fix.replace);
        fs.writeFileSync(fix.file, content);
        console.log(`✅ ${path.basename(fix.file)}: ${fix.reason}`);
      }
    }
  });
}

function generateReport(totalChanges, processedFiles) {
  console.log('\n📊 RAPORT NAPRAWY Z-INDEX:');
  console.log(`   📁 Przetworzonych plików: ${processedFiles}`);
  console.log(`   🔧 Łączna liczba zmian: ${totalChanges}`);
  console.log(`   ✅ Dodano system zmiennych CSS`);
  console.log(`   🔄 Kopie zapasowe utworzone (.backup)`);

  const report = {
    timestamp: new Date().toISOString(),
    processedFiles,
    totalChanges,
    zIndexSystem,
    backupFiles: cssFiles.map(file => file + '.backup'),
  };

  fs.writeFileSync(
    path.join(__dirname, '../z-index-fix-report.json'),
    JSON.stringify(report, null, 2)
  );

  console.log('\n💡 NASTĘPNE KROKI:');
  console.log('   1. Przetestuj aplikację: npm run dev');
  console.log('   2. Sprawdź z-index: npm run z-index:analyze');
  console.log('   3. W razie problemów: npm run z-index:restore');
  console.log('\n✨ Naprawa z-index zakończona!');
  console.log('📄 Raport: z-index-fix-report.json');
}

function restoreBackups() {
  console.log('🔄 Przywracanie kopii zapasowych...\n');

  let restoredFiles = 0;

  cssFiles.forEach(cssFile => {
    const backupFile = cssFile + '.backup';

    if (fs.existsSync(backupFile)) {
      fs.copyFileSync(backupFile, cssFile);
      fs.unlinkSync(backupFile);
      console.log(`✅ Przywrócono: ${path.relative(process.cwd(), cssFile)}`);
      restoredFiles++;
    }
  });

  console.log(`\n📊 Przywrócono ${restoredFiles} plików CSS`);
  console.log('✨ Kopie zapasowe zostały przywrócone!');
}

// Główna funkcja naprawy
function fixZIndex() {
  console.log(`📁 Znaleziono ${cssFiles.length} plików CSS do naprawy...\n`);

  // Dodaj zmienne CSS
  if (!addZIndexVariables()) {
    console.log('❌ Nie udało się dodać zmiennych CSS. Przerywam.');
    return;
  }

  let totalChanges = 0;
  let processedFiles = 0;

  // Napraw każdy plik
  cssFiles.forEach(cssFile => {
    const changes = fixZIndexInFile(cssFile);
    totalChanges += changes;
    if (changes > 0) processedFiles++;
  });

  // Napraw specjalne przypadki
  fixSpecialCases();

  // Wygeneruj raport
  generateReport(totalChanges, processedFiles);
}

// Sprawdź argument wiersza poleceń
const action = process.argv[2];

if (action === 'restore') {
  restoreBackups();
} else {
  fixZIndex();
}
