#!/usr/bin/env node

/**
 * Analytics & Monitoring Setup Script
 * Sets up Google Analytics 4, Hotjar, Sentry, and performance monitoring
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class AnalyticsSetup {
  constructor() {
    this.envPath = path.join(process.cwd(), '.env.local');
    this.envExamplePath = path.join(process.cwd(), '.env.example');
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  log(message, color = 'white') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  async setupEnvironmentVariables() {
    this.log('\n🔧 Setting up Analytics & Monitoring Environment Variables', 'blue');
    this.log('='.repeat(60), 'blue');

    const envVars = {};

    // Google Analytics 4
    this.log('\n📊 Google Analytics 4 Setup:', 'cyan');
    const gaId = await this.prompt('Enter your GA4 Measurement ID (G-XXXXXXXXXX): ');
    if (gaId) envVars.NEXT_PUBLIC_GA_MEASUREMENT_ID = gaId;

    // Hotjar
    this.log('\n🔥 Hotjar Setup:', 'cyan');
    const hotjarId = await this.prompt('Enter your Hotjar Site ID: ');
    if (hotjarId) envVars.NEXT_PUBLIC_HOTJAR_ID = hotjarId;

    // Sentry
    this.log('\n🐛 Sentry Error Tracking Setup:', 'cyan');
    const sentryDsn = await this.prompt('Enter your Sentry DSN: ');
    if (sentryDsn) envVars.NEXT_PUBLIC_SENTRY_DSN = sentryDsn;

    // Mixpanel
    this.log('\n📈 Mixpanel Setup (optional):', 'cyan');
    const mixpanelToken = await this.prompt('Enter your Mixpanel Token (optional): ');
    if (mixpanelToken) envVars.NEXT_PUBLIC_MIXPANEL_TOKEN = mixpanelToken;

    // Facebook Pixel
    this.log('\n📘 Facebook Pixel Setup (optional):', 'cyan');
    const fbPixelId = await this.prompt('Enter your Facebook Pixel ID (optional): ');
    if (fbPixelId) envVars.NEXT_PUBLIC_FB_PIXEL_ID = fbPixelId;

    // Performance Monitoring APIs
    this.log('\n⚡ Performance Monitoring APIs (optional):', 'cyan');
    const gtmetrixKey = await this.prompt('Enter your GTmetrix API Key (optional): ');
    if (gtmetrixKey) envVars.GTMETRIX_API_KEY = gtmetrixKey;

    const pingdomKey = await this.prompt('Enter your Pingdom API Key (optional): ');
    if (pingdomKey) envVars.PINGDOM_API_KEY = pingdomKey;

    const uptimeRobotKey = await this.prompt('Enter your UptimeRobot API Key (optional): ');
    if (uptimeRobotKey) envVars.UPTIMEROBOT_API_KEY = uptimeRobotKey;

    return envVars;
  }

  async updateEnvFile(envVars) {
    let envContent = '';

    // Read existing .env.local if it exists
    if (fs.existsSync(this.envPath)) {
      envContent = fs.readFileSync(this.envPath, 'utf8');
    }

    // Add or update environment variables
    Object.entries(envVars).forEach(([key, value]) => {
      if (value) {
        const regex = new RegExp(`^${key}=.*$`, 'm');
        const newLine = `${key}=${value}`;
        
        if (regex.test(envContent)) {
          envContent = envContent.replace(regex, newLine);
        } else {
          envContent += `\n${newLine}`;
        }
      }
    });

    // Write updated content
    fs.writeFileSync(this.envPath, envContent.trim() + '\n');
    this.log(`✅ Environment variables updated in ${this.envPath}`, 'green');
  }

  generateAnalyticsGuide() {
    const guide = `# 📊 Analytics & Monitoring Setup Guide

## 🚀 Quick Start

Your analytics and monitoring setup is now configured! Here's what's been set up:

### 📈 Analytics Tools
- **Google Analytics 4**: Advanced web analytics and conversion tracking
- **Hotjar**: User behavior analytics with heatmaps and session recordings
- **Mixpanel**: Event-based analytics for detailed user journey tracking
- **Facebook Pixel**: Social media advertising and conversion tracking

### 🐛 Error Tracking
- **Sentry**: Real-time error tracking and performance monitoring
- **Enhanced Error Context**: Retreat-specific error categorization
- **Performance Error Tracking**: Automatic detection of slow operations

### ⚡ Performance Monitoring
- **Core Web Vitals**: LCP, FID, CLS, TTFB tracking
- **GTmetrix Integration**: Automated performance testing
- **Pingdom Monitoring**: Uptime and response time monitoring
- **UptimeRobot**: Additional uptime monitoring

## 🔧 Implementation

### 1. Analytics Components
The following components are now available:

\`\`\`jsx
import EnhancedAnalytics from '@/components/Analytics/EnhancedAnalytics';
import ErrorTracking from '@/components/Analytics/ErrorTracking';
import PerformanceMonitoring from '@/components/Performance/PerformanceMonitoring';
\`\`\`

### 2. Custom Event Tracking

#### Retreat-Specific Events
\`\`\`javascript
// Track retreat interest
window.trackRetreatInterest('Bali Retreat 2025', 'view_details');

// Track booking events
window.trackBookingError(error, {
  retreatName: 'Bali Retreat 2025',
  step: 'payment',
  userId: 'user123'
});

// Track form interactions
window.trackFormError(error, {
  formName: 'contact_form',
  fieldName: 'email',
  validationRule: 'email_format'
});
\`\`\`

#### Hotjar Events
\`\`\`javascript
// Track retreat interactions
window.hjTrackRetreatInteraction('Bali Retreat 2025', 'gallery_view');

// Track custom events
window.hjTrackRetreatEvent('booking_started', {
  userId: 'user123',
  retreatName: 'Bali Retreat 2025'
});
\`\`\`

### 3. Performance Monitoring

#### Automatic Tracking
- Core Web Vitals (LCP, FID, CLS, TTFB)
- Resource loading performance
- User interaction performance
- Memory usage monitoring

#### Manual Performance Tracking
\`\`\`javascript
// Track custom performance metrics
window.trackPerformanceError(error, {
  metricName: 'booking_form_load',
  metricValue: 3000,
  threshold: 2000
});
\`\`\`

## 📊 Monitoring Dashboards

### Google Analytics 4
- **Enhanced Ecommerce**: Retreat booking funnel tracking
- **Custom Events**: Retreat interest and engagement
- **User Journey**: From awareness to booking conversion

### Hotjar
- **Heatmaps**: User interaction patterns on retreat pages
- **Session Recordings**: User behavior during booking process
- **Conversion Funnels**: Booking process optimization

### Sentry
- **Error Tracking**: Real-time error monitoring
- **Performance Monitoring**: Transaction and operation tracking
- **Release Tracking**: Error tracking across deployments

## 🎯 Key Metrics to Monitor

### Business Metrics
- Retreat page views and engagement
- Booking funnel conversion rates
- Contact form submissions
- Gallery interaction rates

### Technical Metrics
- Page load times (LCP < 2.5s)
- Interactivity (FID < 100ms)
- Visual stability (CLS < 0.1)
- Error rates and types

### User Experience
- Session duration on retreat pages
- Bounce rate from key pages
- Mobile vs desktop performance
- Geographic performance variations

## 🚨 Alerts & Notifications

### Performance Alerts
- Page load time > 3 seconds
- Error rate > 1%
- Uptime < 99.9%
- Core Web Vitals failing

### Business Alerts
- Booking form errors
- Payment processing issues
- Contact form failures
- High bounce rate on key pages

## 📝 Next Steps

1. **Verify Setup**: Check all analytics tools are receiving data
2. **Set Up Dashboards**: Create custom dashboards for key metrics
3. **Configure Alerts**: Set up notifications for critical issues
4. **Team Training**: Ensure team knows how to use monitoring tools
5. **Regular Reviews**: Schedule weekly performance and analytics reviews

## 🔗 Useful Links

- [Google Analytics 4 Documentation](https://developers.google.com/analytics/devguides/collection/ga4)
- [Hotjar Documentation](https://help.hotjar.com/)
- [Sentry Documentation](https://docs.sentry.io/)
- [GTmetrix API Documentation](https://gtmetrix.com/api/)
- [Pingdom API Documentation](https://docs.pingdom.com/)

---

*Setup completed: ${new Date().toISOString()}*
`;

    fs.writeFileSync(path.join(process.cwd(), 'ANALYTICS_SETUP_GUIDE.md'), guide);
    this.log('✅ Analytics setup guide created: ANALYTICS_SETUP_GUIDE.md', 'green');
  }

  async run() {
    this.log('🚀 BAKASANA Analytics & Monitoring Setup', 'magenta');
    this.log('This script will help you configure comprehensive analytics and monitoring.', 'white');

    try {
      const envVars = await this.setupEnvironmentVariables();
      await this.updateEnvFile(envVars);
      this.generateAnalyticsGuide();

      this.log('\n✅ Analytics & Monitoring Setup Complete!', 'green');
      this.log('\nNext steps:', 'blue');
      this.log('1. Restart your development server: npm run dev', 'cyan');
      this.log('2. Check the ANALYTICS_SETUP_GUIDE.md for detailed instructions', 'cyan');
      this.log('3. Verify analytics tools are receiving data', 'cyan');
      this.log('4. Set up monitoring dashboards and alerts', 'cyan');

    } catch (error) {
      this.log(`\n❌ Setup failed: ${error.message}`, 'red');
    } finally {
      this.rl.close();
    }
  }
}

// Run the setup
if (require.main === module) {
  const setup = new AnalyticsSetup();
  setup.run();
}

module.exports = AnalyticsSetup;
