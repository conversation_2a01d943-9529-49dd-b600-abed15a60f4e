"use strict";"use client";var se=Object.create;var p=Object.defineProperty;var ie=Object.getOwnPropertyDescriptor;var ye=Object.getOwnPropertyNames;var me=Object.getPrototypeOf,ce=Object.prototype.hasOwnProperty;var pe=(e,t)=>{for(var n in t)p(e,n,{get:t[n],enumerable:!0})},x=(e,t,n,l)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of ye(t))!ce.call(e,a)&&a!==n&&p(e,a,{get:()=>t[a],enumerable:!(l=ie(t,a))||l.enumerable});return e};var M=(e,t,n)=>(n=e!=null?se(me(e)):{},x(t||!e||!e.__esModule?p(n,"default",{value:e,enumerable:!0}):n,e)),Ee=e=>x(p({},"__esModule",{value:!0}),e);var Ne={};pe(Ne,{MaxResolution:()=>i.MaxResolution,MediaError:()=>O.MediaError,MinResolution:()=>i.MinResolution,RenditionOrder:()=>i.RenditionOrder,default:()=>we,generatePlayerInitTime:()=>i.generatePlayerInitTime,playerSoftwareName:()=>w,playerSoftwareVersion:()=>S});module.exports=Ee(Ne);var s=M(require("react")),i=require("@mux/playback-core"),O=require("@mux/mux-player");var f=M(require("react")),g=parseInt(f.default.version)>=19,d={className:"class",classname:"class",htmlFor:"for",crossOrigin:"crossorigin",viewBox:"viewBox",playsInline:"playsinline",autoPlay:"autoplay",playbackRate:"playbackrate"},Me=e=>e==null,Pe=(e,t)=>Me(t)?!1:e in t,xe=e=>e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`),de=(e,t)=>{if(!(!g&&typeof t=="boolean"&&!t)){if(Pe(e,d))return d[e];if(typeof t!="undefined")return/[A-Z]/.test(e)?xe(e):e}};var fe=(e,t)=>!g&&typeof e=="boolean"?"":e,v=(e={})=>{let{ref:t,...n}=e;return Object.entries(n).reduce((l,[a,o])=>{let m=de(a,o);if(!m)return l;let E=fe(o,a);return l[m]=E,l},{})};var T=M(require("react"));function R(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function ge(...e){return t=>{let n=!1,l=e.map(a=>{let o=R(a,t);return!n&&typeof o=="function"&&(n=!0),o});if(n)return()=>{for(let a=0;a<l.length;a++){let o=l[a];typeof o=="function"?o():R(e[a],null)}}}}function h(...e){return T.useCallback(ge(...e),e)}var b=require("react"),ve=Object.prototype.hasOwnProperty,Re=(e,t)=>{if(Object.is(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;if(Array.isArray(e))return!Array.isArray(t)||e.length!==t.length?!1:e.some((a,o)=>t[o]===a);let n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(let a=0;a<n.length;a++)if(!ve.call(t,n[a])||!Object.is(e[n[a]],t[n[a]]))return!1;return!0},P=(e,t,n)=>!Re(t,e[n]),Te=(e,t,n)=>{e[n]=t},he=(e,t,n,l=Te,a=P)=>(0,b.useEffect)(()=>{let o=n==null?void 0:n.current;o&&a(o,t,e)&&l(o,t,e)},[n==null?void 0:n.current,t]),u=he;var be=()=>{try{return"3.5.1"}catch{}return"UNKNOWN"},Ce=be(),C=()=>Ce;var k=require("react"),r=(e,t,n)=>(0,k.useEffect)(()=>{let l=t==null?void 0:t.current;if(!l||!n)return;let a=e,o=n;return l.addEventListener(a,o),()=>{l.removeEventListener(a,o)}},[t==null?void 0:t.current,n,e]);var ke=s.default.forwardRef(({children:e,...t},n)=>s.default.createElement("mux-player",{suppressHydrationWarning:!0,...v(t),ref:n},e)),Oe=(e,t)=>{let{onAbort:n,onCanPlay:l,onCanPlayThrough:a,onEmptied:o,onLoadStart:m,onLoadedData:E,onLoadedMetadata:N,onProgress:L,onDurationChange:A,onVolumeChange:I,onRateChange:_,onResize:K,onWaiting:H,onPlay:D,onPlaying:V,onTimeUpdate:W,onPause:U,onSeeking:z,onSeeked:F,onStalled:G,onSuspend:Z,onEnded:j,onError:q,onCuePointChange:J,onChapterChange:Y,metadata:$,tokens:Q,paused:X,playbackId:B,playbackRates:ee,currentTime:te,themeProps:ne,extraSourceParams:ae,castCustomData:re,_hlsConfig:le,...oe}=t;return u("playbackRates",ee,e),u("metadata",$,e),u("extraSourceParams",ae,e),u("_hlsConfig",le,e),u("themeProps",ne,e),u("tokens",Q,e),u("playbackId",B,e),u("castCustomData",re,e),u("paused",X,e,(y,c)=>{c!=null&&(c?y.pause():y.play())},(y,c,ue)=>y.hasAttribute("autoplay")&&!y.hasPlayed?!1:P(y,c,ue)),u("currentTime",te,e,(y,c)=>{c!=null&&(y.currentTime=c)}),r("abort",e,n),r("canplay",e,l),r("canplaythrough",e,a),r("emptied",e,o),r("loadstart",e,m),r("loadeddata",e,E),r("loadedmetadata",e,N),r("progress",e,L),r("durationchange",e,A),r("volumechange",e,I),r("ratechange",e,_),r("resize",e,K),r("waiting",e,H),r("play",e,D),r("playing",e,V),r("timeupdate",e,W),r("pause",e,U),r("seeking",e,z),r("seeked",e,F),r("stalled",e,G),r("suspend",e,Z),r("ended",e,j),r("error",e,q),r("cuepointchange",e,J),r("chapterchange",e,Y),[oe]},S=C(),w="mux-player-react",Se=s.default.forwardRef((e,t)=>{var m;let n=(0,s.useRef)(null),l=h(n,t),[a]=Oe(n,e),[o]=(0,s.useState)((m=e.playerInitTime)!=null?m:(0,i.generatePlayerInitTime)());return s.default.createElement(ke,{ref:l,defaultHiddenCaptions:e.defaultHiddenCaptions,playerSoftwareName:w,playerSoftwareVersion:S,playerInitTime:o,...a})}),we=Se;
//# sourceMappingURL=index.cjs.js.map
