#!/usr/bin/env node

/**
 * Environment Variables Validation Script
 * Validates critical environment variables for Bakasana Travel Blog
 */

const path = require('path');
const fs = require('fs');

// Load environment variables from .env.local if it exists
const envPath = path.join(process.cwd(), '.env.local');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
}

console.log('🔍 Validating environment variables...');
console.log('=====================================');

// Critical environment variables for production
const criticalVars = [
  'JWT_SECRET',
  'ADMIN_PASSWORD',
  'NEXT_PUBLIC_SANITY_PROJECT_ID'
];

// Optional but recommended variables
const recommendedVars = [
  'NEXT_PUBLIC_SANITY_DATASET',
  'SANITY_API_TOKEN',
  'NEXT_PUBLIC_GA_ID',
  'NEXT_PUBLIC_GA_MEASUREMENT_ID',
  'REDIS_URL'
];

// Email service variables
const emailVars = [
  'RESEND_API_KEY',
  'CONVERTKIT_API_KEY',
  'CONVERTKIT_FORM_ID',
  'NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY'
];

// Analytics variables
const analyticsVars = [
  'NEXT_PUBLIC_SENTRY_DSN',
  'NEXT_PUBLIC_HOTJAR_ID',
  'NEXT_PUBLIC_MIXPANEL_TOKEN',
  'NEXT_PUBLIC_FB_PIXEL_ID'
];

// SEO verification variables
const seoVars = [
  'NEXT_PUBLIC_GOOGLE_VERIFICATION',
  'NEXT_PUBLIC_BING_VERIFICATION',
  'NEXT_PUBLIC_YANDEX_VERIFICATION'
];

let hasErrors = false;
let hasWarnings = false;

// Check critical variables
console.log('🚨 Critical Variables:');
criticalVars.forEach(varName => {
  const value = process.env[varName];
  if (!value) {
    console.log(`❌ ${varName}: MISSING (CRITICAL)`);
    hasErrors = true;
  } else {
    // Additional validation for specific variables
    if (varName === 'JWT_SECRET' && value.length < 32) {
      console.log(`⚠️  ${varName}: TOO SHORT (should be 32+ characters)`);
      hasWarnings = true;
    } else if (varName === 'NEXT_PUBLIC_SANITY_PROJECT_ID' && value === 'your-project-id') {
      console.log(`❌ ${varName}: PLACEHOLDER VALUE (set real Sanity project ID)`);
      hasErrors = true;
    } else {
      console.log(`✅ ${varName}: OK`);
    }
  }
});

console.log('');

// Check recommended variables
console.log('📋 Recommended Variables:');
recommendedVars.forEach(varName => {
  const value = process.env[varName];
  if (!value) {
    console.log(`⚠️  ${varName}: MISSING (recommended)`);
    hasWarnings = true;
  } else {
    console.log(`✅ ${varName}: OK`);
  }
});

// Check email service variables
console.log('\n📧 Email Service Variables:');
let emailConfigured = false;
emailVars.forEach(varName => {
  const value = process.env[varName];
  if (value && !value.includes('your_') && !value.includes('placeholder')) {
    console.log(`✅ ${varName}: OK`);
    emailConfigured = true;
  } else {
    console.log(`⚠️  ${varName}: NOT CONFIGURED`);
  }
});

if (!emailConfigured) {
  console.log('⚠️  No email service configured - emails will be logged only');
  hasWarnings = true;
}

// Check analytics variables
console.log('\n📊 Analytics Variables:');
analyticsVars.forEach(varName => {
  const value = process.env[varName];
  if (value && !value.includes('your_') && !value.includes('placeholder')) {
    console.log(`✅ ${varName}: OK`);
  } else {
    console.log(`⚠️  ${varName}: NOT CONFIGURED (optional)`);
  }
});

// Check SEO verification variables
console.log('\n🔍 SEO Verification Variables:');
seoVars.forEach(varName => {
  const value = process.env[varName];
  if (value && !value.includes('your_') && !value.includes('placeholder')) {
    console.log(`✅ ${varName}: OK`);
  } else {
    console.log(`⚠️  ${varName}: NOT CONFIGURED (recommended for SEO)`);
  }
});

console.log('');

// Environment-specific checks
if (process.env.NODE_ENV === 'production') {
  console.log('🚀 Production Environment Checks:');
  
  // Redis check for production
  if (!process.env.REDIS_URL) {
    console.log('⚠️  REDIS_URL not set - using memory-based rate limiting (not recommended for production)');
    hasWarnings = true;
  }
  
  // Security checks
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.includes('change-this')) {
    console.log('❌ JWT_SECRET contains placeholder text - generate a secure secret');
    hasErrors = true;
  }
  
  if (process.env.ADMIN_PASSWORD && process.env.ADMIN_PASSWORD.length < 12) {
    console.log('⚠️  ADMIN_PASSWORD is too short - use 12+ characters for production');
    hasWarnings = true;
  }
} else {
  console.log('🔧 Development Environment - some checks skipped');
}

console.log('');

// Summary
console.log('📊 VALIDATION SUMMARY:');
console.log('======================');

if (hasErrors) {
  console.log('❌ VALIDATION FAILED - Critical issues found');
  console.log('');
  console.log('🔧 To fix critical issues:');
  console.log('1. Set missing critical environment variables');
  console.log('2. Replace placeholder values with real ones');
  console.log('3. Generate secure JWT_SECRET: openssl rand -hex 32');
  console.log('4. Set up Sanity CMS project and get real project ID');
  console.log('');
  console.log('📖 See SECURITY_SETUP.md for detailed instructions');
  process.exit(1);
} else if (hasWarnings) {
  console.log('⚠️  VALIDATION PASSED WITH WARNINGS');
  console.log('');
  console.log('💡 Recommendations:');
  console.log('1. Set up Redis for better rate limiting in production');
  console.log('2. Configure Google Analytics for tracking');
  console.log('3. Set up Sanity API token for content management');
  console.log('4. Use stronger passwords (12+ characters)');
  console.log('');
  console.log('✅ Application can run but consider addressing warnings');
  process.exit(0);
} else {
  console.log('✅ ALL VALIDATIONS PASSED');
  console.log('');
  console.log('🎉 Environment is properly configured!');
  console.log('🚀 Ready for deployment');
  process.exit(0);
}
