#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const puppeteer = require('puppeteer');

console.log(
  '🔍 BAKASANA Z-INDEX CHECKER - Sprawdzanie nakładających się elementów...\n'
);

async function checkZIndexIssues() {
  let browser;

  try {
    // Uruchom przeglądarkę
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });

    // Lista stron do sprawdzenia
    const pagesToCheck = [
      'http://localhost:3002',
      'http://localhost:3002/o-mnie',
      'http://localhost:3002/retreaty',
      'http://localhost:3002/kontakt',
      'http://localhost:3002/blog',
    ];

    const allResults = [];

    for (const url of pagesToCheck) {
      console.log(`🔍 Sprawdzam: ${url}`);

      try {
        await page.goto(url, { waitUntil: 'networkidle0', timeout: 10000 });

        // Skrypt do sprawdzenia z-index
        const zIndexResults = await page.evaluate(() => {
          const results = [];
          const elements = document.querySelectorAll('*');

          elements.forEach(el => {
            const computedStyle = getComputedStyle(el);
            const zIndex = computedStyle.zIndex;

            // Sprawdź elementy z wysokim z-index
            if (zIndex !== 'auto' && parseInt(zIndex) > 10) {
              const rect = el.getBoundingClientRect();

              results.push({
                tagName: el.tagName.toLowerCase(),
                className: el.className || '',
                id: el.id || '',
                zIndex: parseInt(zIndex),
                position: computedStyle.position,
                top: computedStyle.top,
                left: computedStyle.left,
                width: Math.round(rect.width),
                height: Math.round(rect.height),
                visible: rect.width > 0 && rect.height > 0,
                selector:
                  el.tagName.toLowerCase() +
                  (el.id ? `#${el.id}` : '') +
                  (el.className ? `.${el.className.split(' ').join('.')}` : ''),
              });
            }
          });

          return results.sort((a, b) => b.zIndex - a.zIndex);
        });

        // Sprawdź nakładające się elementy
        const overlappingElements = await page.evaluate(() => {
          const overlaps = [];
          const positionedElements = Array.from(
            document.querySelectorAll('*')
          ).filter(el => {
            const style = getComputedStyle(el);
            return style.position !== 'static' && style.position !== '';
          });

          for (let i = 0; i < positionedElements.length; i++) {
            for (let j = i + 1; j < positionedElements.length; j++) {
              const el1 = positionedElements[i];
              const el2 = positionedElements[j];
              const rect1 = el1.getBoundingClientRect();
              const rect2 = el2.getBoundingClientRect();

              // Sprawdź czy elementy się nakładają
              const overlap = !(
                rect1.right < rect2.left ||
                rect2.right < rect1.left ||
                rect1.bottom < rect2.top ||
                rect2.bottom < rect1.top
              );

              if (
                overlap &&
                rect1.width > 0 &&
                rect1.height > 0 &&
                rect2.width > 0 &&
                rect2.height > 0
              ) {
                const style1 = getComputedStyle(el1);
                const style2 = getComputedStyle(el2);

                overlaps.push({
                  element1: {
                    selector:
                      el1.tagName.toLowerCase() +
                      (el1.id ? `#${el1.id}` : '') +
                      (el1.className
                        ? `.${el1.className.split(' ').join('.')}`
                        : ''),
                    zIndex: style1.zIndex,
                    position: style1.position,
                  },
                  element2: {
                    selector:
                      el2.tagName.toLowerCase() +
                      (el2.id ? `#${el2.id}` : '') +
                      (el2.className
                        ? `.${el2.className.split(' ').join('.')}`
                        : ''),
                    zIndex: style2.zIndex,
                    position: style2.position,
                  },
                });
              }
            }
          }

          return overlaps;
        });

        allResults.push({
          url,
          zIndexElements: zIndexResults,
          overlappingElements,
          timestamp: new Date().toISOString(),
        });

        console.log(
          `   ✅ Znaleziono ${zIndexResults.length} elementów z wysokim z-index`
        );
        console.log(
          `   ⚠️  Znaleziono ${overlappingElements.length} nakładających się elementów`
        );
      } catch (error) {
        console.log(`   ❌ Błąd przy sprawdzaniu ${url}: ${error.message}`);
        allResults.push({
          url,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }
    }

    // Generuj raport
    await generateReport(allResults);
  } catch (error) {
    console.error('❌ Błąd podczas sprawdzania z-index:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function generateReport(results) {
  console.log('\n📊 GENEROWANIE RAPORTU Z-INDEX...\n');

  // Zbierz wszystkie elementy z wysokim z-index
  const allHighZIndex = [];
  const allOverlaps = [];

  results.forEach(result => {
    if (result.zIndexElements) {
      result.zIndexElements.forEach(el => {
        allHighZIndex.push({
          ...el,
          page: result.url,
        });
      });
    }

    if (result.overlappingElements) {
      result.overlappingElements.forEach(overlap => {
        allOverlaps.push({
          ...overlap,
          page: result.url,
        });
      });
    }
  });

  // Sortuj według z-index
  allHighZIndex.sort((a, b) => b.zIndex - a.zIndex);

  // Znajdź najczęstsze problemy
  const zIndexCounts = {};
  allHighZIndex.forEach(el => {
    const key = el.zIndex;
    zIndexCounts[key] = (zIndexCounts[key] || 0) + 1;
  });

  // Wyświetl podsumowanie
  console.log('🎯 PODSUMOWANIE Z-INDEX:');
  console.log(
    `   📊 Łącznie elementów z wysokim z-index: ${allHighZIndex.length}`
  );
  console.log(`   ⚠️  Nakładających się elementów: ${allOverlaps.length}`);

  if (allHighZIndex.length > 0) {
    console.log('\n🔝 NAJWYŻSZE Z-INDEX:');
    allHighZIndex.slice(0, 10).forEach(el => {
      console.log(`   z-index: ${el.zIndex} - ${el.selector} (${el.page})`);
    });
  }

  if (allOverlaps.length > 0) {
    console.log('\n⚠️  NAKŁADAJĄCE SIĘ ELEMENTY:');
    allOverlaps.slice(0, 5).forEach(overlap => {
      console.log(
        `   ${overlap.element1.selector} (z:${overlap.element1.zIndex}) ↔ ${overlap.element2.selector} (z:${overlap.element2.zIndex})`
      );
    });
  }

  // Zapisz szczegółowy raport
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalHighZIndex: allHighZIndex.length,
      totalOverlaps: allOverlaps.length,
      maxZIndex: allHighZIndex.length > 0 ? allHighZIndex[0].zIndex : 0,
      zIndexDistribution: zIndexCounts,
    },
    highZIndexElements: allHighZIndex,
    overlappingElements: allOverlaps,
    pageResults: results,
  };

  fs.writeFileSync(
    path.join(__dirname, '../z-index-report.json'),
    JSON.stringify(report, null, 2)
  );

  // Generuj rekomendacje
  generateRecommendations(report);

  console.log('\n✨ Raport z-index wygenerowany!');
  console.log('📄 Szczegółowy raport: z-index-report.json');
  console.log('💡 Rekomendacje: z-index-recommendations.md');
}

function generateRecommendations(report) {
  const recommendations = [];

  // Sprawdź czy są bardzo wysokie z-index
  const veryHighZIndex = report.highZIndexElements.filter(
    el => el.zIndex > 1000
  );
  if (veryHighZIndex.length > 0) {
    recommendations.push({
      type: 'warning',
      title: 'Bardzo wysokie wartości z-index',
      description: `Znaleziono ${veryHighZIndex.length} elementów z z-index > 1000. Rozważ użycie niższych wartości.`,
      elements: veryHighZIndex.slice(0, 5).map(el => el.selector),
    });
  }

  // Sprawdź nakładające się elementy
  if (report.overlappingElements.length > 0) {
    recommendations.push({
      type: 'error',
      title: 'Nakładające się elementy',
      description: `Znaleziono ${report.overlappingElements.length} par nakładających się elementów. Może to powodować problemy z UX.`,
      elements: report.overlappingElements.slice(0, 3),
    });
  }

  // Zapisz rekomendacje
  let mdContent = '# 🔍 Z-INDEX RECOMMENDATIONS\n\n';
  mdContent += `Raport wygenerowany: ${new Date().toLocaleString()}\n\n`;

  recommendations.forEach(rec => {
    const icon =
      rec.type === 'error' ? '❌' : rec.type === 'warning' ? '⚠️' : '💡';
    mdContent += `## ${icon} ${rec.title}\n\n`;
    mdContent += `${rec.description}\n\n`;

    if (rec.elements && rec.elements.length > 0) {
      mdContent += '### Elementy:\n';
      rec.elements.forEach(el => {
        if (typeof el === 'string') {
          mdContent += `- \`${el}\`\n`;
        } else {
          mdContent += `- \`${el.element1?.selector}\` ↔ \`${el.element2?.selector}\`\n`;
        }
      });
      mdContent += '\n';
    }
  });

  fs.writeFileSync(
    path.join(__dirname, '../z-index-recommendations.md'),
    mdContent
  );
}

// Uruchom sprawdzenie
checkZIndexIssues().catch(console.error);
