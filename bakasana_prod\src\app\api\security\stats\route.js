import { NextResponse } from 'next/server';
import { securityLogger } from '@/lib/securityLogger';
import { botDetector, adaptiveRateLimiter } from '@/lib/advancedSecurity';

/**
 * API endpoint do monitorowania statystyk bezpieczeństwa
 * Dostępny tylko dla administratorów
 */

function getClientIP(request) {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return 'unknown';
}

// Sprawdź autoryzację administratora
function checkAdminAuth(request) {
  const authHeader = request.headers.get('authorization');
  const cookieAuth = request.cookies.get('admin-token');
  
  // W rzeczywistej implementacji należy zweryfikować JWT token
  // Tutaj uproszczona wersja
  return authHeader || cookieAuth;
}

export async function GET(request) {
  try {
    const clientIP = getClientIP(request);
    
    // Sprawdź autoryzację
    if (!checkAdminAuth(request)) {
      securityLogger.logSecurityEvent('UNAUTHORIZED_SECURITY_STATS_ACCESS', {
        clientIP,
        userAgent: request.headers.get('user-agent'),
        path: '/api/security/stats',
        message: 'Unauthorized access to security stats endpoint'
      }, 'medium', clientIP);
      
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    // Pobierz parametry zapytania
    const { searchParams } = new URL(request.url);
    const timeRange = parseInt(searchParams.get('timeRange')) || 24; // domyślnie 24h
    const includeDetails = searchParams.get('details') === 'true';

    // Pobierz statystyki bezpieczeństwa
    const securityStats = securityLogger.getSecurityStats(timeRange);
    
    // Dodaj statystyki z bot detector
    const botStats = {
      totalAnalyzedRequests: botDetector.userBehavior.size,
      suspiciousPatterns: Array.from(botDetector.suspiciousPatterns.entries()).slice(0, 10),
      averageRiskScore: calculateAverageRiskScore(botDetector.userBehavior)
    };

    // Dodaj statystyki rate limiting
    const rateLimitStats = {
      globalRequests: adaptiveRateLimiter.globalStats.totalRequests,
      blockedRequests: adaptiveRateLimiter.globalStats.blockedRequests,
      blockRate: adaptiveRateLimiter.globalStats.totalRequests > 0 
        ? ((adaptiveRateLimiter.globalStats.blockedRequests / adaptiveRateLimiter.globalStats.totalRequests) * 100).toFixed(2)
        : 0
    };

    const response = {
      timestamp: new Date().toISOString(),
      timeRange: `${timeRange}h`,
      security: securityStats,
      botDetection: botStats,
      rateLimiting: rateLimitStats,
      systemHealth: {
        status: 'operational',
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        nodeVersion: process.version
      }
    };

    // Dodaj szczegółowe informacje jeśli wymagane
    if (includeDetails) {
      response.recentEvents = securityLogger.events.slice(-50); // Ostatnie 50 zdarzeń
      response.topRiskIPs = getTopRiskIPs(botDetector.userBehavior, 10);
    }

    // Loguj dostęp do statystyk
    securityLogger.logSecurityEvent('SECURITY_STATS_ACCESSED', {
      clientIP,
      userAgent: request.headers.get('user-agent'),
      path: '/api/security/stats',
      timeRange,
      includeDetails,
      message: 'Security statistics accessed by admin'
    }, 'low', clientIP);

    return NextResponse.json(response);

  } catch (error) {
    console.error('Security stats error:', error);
    
    securityLogger.logSecurityEvent('SECURITY_STATS_ERROR', {
      clientIP: getClientIP(request),
      userAgent: request.headers.get('user-agent'),
      path: '/api/security/stats',
      error: error.message,
      message: 'Error retrieving security statistics'
    }, 'medium', getClientIP(request));
    
    return NextResponse.json(
      { 
        error: 'Failed to retrieve security statistics',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const clientIP = getClientIP(request);
    
    // Sprawdź autoryzację
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    const { action, data } = await request.json();

    switch (action) {
      case 'clear_events':
        // Wyczyść zdarzenia bezpieczeństwa
        const clearedCount = securityLogger.events.length;
        securityLogger.events = [];
        
        securityLogger.logSecurityEvent('SECURITY_EVENTS_CLEARED', {
          clientIP,
          userAgent: request.headers.get('user-agent'),
          path: '/api/security/stats',
          clearedCount,
          message: `Admin cleared ${clearedCount} security events`
        }, 'medium', clientIP);
        
        return NextResponse.json({
          success: true,
          message: `Cleared ${clearedCount} security events`
        });

      case 'update_thresholds':
        // Aktualizuj progi alertów
        if (data.alertThresholds) {
          Object.assign(securityLogger.alertThresholds, data.alertThresholds);
          
          securityLogger.logSecurityEvent('ALERT_THRESHOLDS_UPDATED', {
            clientIP,
            userAgent: request.headers.get('user-agent'),
            path: '/api/security/stats',
            newThresholds: data.alertThresholds,
            message: 'Admin updated alert thresholds'
          }, 'medium', clientIP);
          
          return NextResponse.json({
            success: true,
            message: 'Alert thresholds updated',
            newThresholds: securityLogger.alertThresholds
          });
        }
        break;

      case 'block_ip':
        // Zablokuj IP (placeholder - w produkcji integracja z firewall)
        if (data.ip) {
          securityLogger.logSecurityEvent('IP_BLOCKED_BY_ADMIN', {
            clientIP,
            userAgent: request.headers.get('user-agent'),
            path: '/api/security/stats',
            blockedIP: data.ip,
            reason: data.reason || 'Manual block by admin',
            message: `Admin blocked IP: ${data.ip}`
          }, 'high', clientIP);
          
          return NextResponse.json({
            success: true,
            message: `IP ${data.ip} has been blocked`
          });
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Unknown action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Security stats POST error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to process security action',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Helper functions

function calculateAverageRiskScore(userBehaviorMap) {
  if (userBehaviorMap.size === 0) return 0;
  
  let totalRisk = 0;
  let count = 0;
  
  for (const behavior of userBehaviorMap.values()) {
    if (behavior.riskScore > 0) {
      totalRisk += behavior.riskScore;
      count++;
    }
  }
  
  return count > 0 ? Math.round(totalRisk / count) : 0;
}

function getTopRiskIPs(userBehaviorMap, limit = 10) {
  const riskIPs = [];
  
  for (const [key, behavior] of userBehaviorMap.entries()) {
    if (behavior.riskScore > 30) {
      const ip = key.split(':')[0];
      riskIPs.push({
        ip,
        riskScore: behavior.riskScore,
        patterns: behavior.patterns,
        requestCount: behavior.requests.length,
        firstSeen: new Date(behavior.firstSeen).toISOString()
      });
    }
  }
  
  return riskIPs
    .sort((a, b) => b.riskScore - a.riskScore)
    .slice(0, limit);
}
