(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.jsYaml=f()}})((function(){var define,module,exports;return function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,(function(r){var n=e[i][1][r];return o(n||r)}),p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r}()({1:[function(require,module,exports){"use strict";var loader=require("./lib/loader");var dumper=require("./lib/dumper");function renamed(from,to){return function(){throw new Error("Function yaml."+from+" is removed in js-yaml 4. "+"Use yaml."+to+" instead, which is now safe by default.")}}module.exports.Type=require("./lib/type");module.exports.Schema=require("./lib/schema");module.exports.FAILSAFE_SCHEMA=require("./lib/schema/failsafe");module.exports.JSON_SCHEMA=require("./lib/schema/json");module.exports.CORE_SCHEMA=require("./lib/schema/core");module.exports.DEFAULT_SCHEMA=require("./lib/schema/default");module.exports.load=loader.load;module.exports.loadAll=loader.loadAll;module.exports.dump=dumper.dump;module.exports.YAMLException=require("./lib/exception");module.exports.types={binary:require("./lib/type/binary"),float:require("./lib/type/float"),map:require("./lib/type/map"),null:require("./lib/type/null"),pairs:require("./lib/type/pairs"),set:require("./lib/type/set"),timestamp:require("./lib/type/timestamp"),bool:require("./lib/type/bool"),int:require("./lib/type/int"),merge:require("./lib/type/merge"),omap:require("./lib/type/omap"),seq:require("./lib/type/seq"),str:require("./lib/type/str")};module.exports.safeLoad=renamed("safeLoad","load");module.exports.safeLoadAll=renamed("safeLoadAll","loadAll");module.exports.safeDump=renamed("safeDump","dump")},{"./lib/dumper":3,"./lib/exception":4,"./lib/loader":5,"./lib/schema":6,"./lib/schema/core":7,"./lib/schema/default":8,"./lib/schema/failsafe":9,"./lib/schema/json":10,"./lib/type":12,"./lib/type/binary":13,"./lib/type/bool":14,"./lib/type/float":15,"./lib/type/int":16,"./lib/type/map":17,"./lib/type/merge":18,"./lib/type/null":19,"./lib/type/omap":20,"./lib/type/pairs":21,"./lib/type/seq":22,"./lib/type/set":23,"./lib/type/str":24,"./lib/type/timestamp":25}],2:[function(require,module,exports){"use strict";function isNothing(subject){return typeof subject==="undefined"||subject===null}function isObject(subject){return typeof subject==="object"&&subject!==null}function toArray(sequence){if(Array.isArray(sequence))return sequence;else if(isNothing(sequence))return[];return[sequence]}function extend(target,source){var index,length,key,sourceKeys;if(source){sourceKeys=Object.keys(source);for(index=0,length=sourceKeys.length;index<length;index+=1){key=sourceKeys[index];target[key]=source[key]}}return target}function repeat(string,count){var result="",cycle;for(cycle=0;cycle<count;cycle+=1){result+=string}return result}function isNegativeZero(number){return number===0&&Number.NEGATIVE_INFINITY===1/number}module.exports.isNothing=isNothing;module.exports.isObject=isObject;module.exports.toArray=toArray;module.exports.repeat=repeat;module.exports.isNegativeZero=isNegativeZero;module.exports.extend=extend},{}],3:[function(require,module,exports){"use strict";var common=require("./common");var YAMLException=require("./exception");var DEFAULT_SCHEMA=require("./schema/default");var _toString=Object.prototype.toString;var _hasOwnProperty=Object.prototype.hasOwnProperty;var CHAR_BOM=65279;var CHAR_TAB=9;var CHAR_LINE_FEED=10;var CHAR_CARRIAGE_RETURN=13;var CHAR_SPACE=32;var CHAR_EXCLAMATION=33;var CHAR_DOUBLE_QUOTE=34;var CHAR_SHARP=35;var CHAR_PERCENT=37;var CHAR_AMPERSAND=38;var CHAR_SINGLE_QUOTE=39;var CHAR_ASTERISK=42;var CHAR_COMMA=44;var CHAR_MINUS=45;var CHAR_COLON=58;var CHAR_EQUALS=61;var CHAR_GREATER_THAN=62;var CHAR_QUESTION=63;var CHAR_COMMERCIAL_AT=64;var CHAR_LEFT_SQUARE_BRACKET=91;var CHAR_RIGHT_SQUARE_BRACKET=93;var CHAR_GRAVE_ACCENT=96;var CHAR_LEFT_CURLY_BRACKET=123;var CHAR_VERTICAL_LINE=124;var CHAR_RIGHT_CURLY_BRACKET=125;var ESCAPE_SEQUENCES={};ESCAPE_SEQUENCES[0]="\\0";ESCAPE_SEQUENCES[7]="\\a";ESCAPE_SEQUENCES[8]="\\b";ESCAPE_SEQUENCES[9]="\\t";ESCAPE_SEQUENCES[10]="\\n";ESCAPE_SEQUENCES[11]="\\v";ESCAPE_SEQUENCES[12]="\\f";ESCAPE_SEQUENCES[13]="\\r";ESCAPE_SEQUENCES[27]="\\e";ESCAPE_SEQUENCES[34]='\\"';ESCAPE_SEQUENCES[92]="\\\\";ESCAPE_SEQUENCES[133]="\\N";ESCAPE_SEQUENCES[160]="\\_";ESCAPE_SEQUENCES[8232]="\\L";ESCAPE_SEQUENCES[8233]="\\P";var DEPRECATED_BOOLEANS_SYNTAX=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"];var DEPRECATED_BASE60_SYNTAX=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function compileStyleMap(schema,map){var result,keys,index,length,tag,style,type;if(map===null)return{};result={};keys=Object.keys(map);for(index=0,length=keys.length;index<length;index+=1){tag=keys[index];style=String(map[tag]);if(tag.slice(0,2)==="!!"){tag="tag:yaml.org,2002:"+tag.slice(2)}type=schema.compiledTypeMap["fallback"][tag];if(type&&_hasOwnProperty.call(type.styleAliases,style)){style=type.styleAliases[style]}result[tag]=style}return result}function encodeHex(character){var string,handle,length;string=character.toString(16).toUpperCase();if(character<=255){handle="x";length=2}else if(character<=65535){handle="u";length=4}else if(character<=4294967295){handle="U";length=8}else{throw new YAMLException("code point within a string may not be greater than 0xFFFFFFFF")}return"\\"+handle+common.repeat("0",length-string.length)+string}var QUOTING_TYPE_SINGLE=1,QUOTING_TYPE_DOUBLE=2;function State(options){this.schema=options["schema"]||DEFAULT_SCHEMA;this.indent=Math.max(1,options["indent"]||2);this.noArrayIndent=options["noArrayIndent"]||false;this.skipInvalid=options["skipInvalid"]||false;this.flowLevel=common.isNothing(options["flowLevel"])?-1:options["flowLevel"];this.styleMap=compileStyleMap(this.schema,options["styles"]||null);this.sortKeys=options["sortKeys"]||false;this.lineWidth=options["lineWidth"]||80;this.noRefs=options["noRefs"]||false;this.noCompatMode=options["noCompatMode"]||false;this.condenseFlow=options["condenseFlow"]||false;this.quotingType=options["quotingType"]==='"'?QUOTING_TYPE_DOUBLE:QUOTING_TYPE_SINGLE;this.forceQuotes=options["forceQuotes"]||false;this.replacer=typeof options["replacer"]==="function"?options["replacer"]:null;this.implicitTypes=this.schema.compiledImplicit;this.explicitTypes=this.schema.compiledExplicit;this.tag=null;this.result="";this.duplicates=[];this.usedDuplicates=null}function indentString(string,spaces){var ind=common.repeat(" ",spaces),position=0,next=-1,result="",line,length=string.length;while(position<length){next=string.indexOf("\n",position);if(next===-1){line=string.slice(position);position=length}else{line=string.slice(position,next+1);position=next+1}if(line.length&&line!=="\n")result+=ind;result+=line}return result}function generateNextLine(state,level){return"\n"+common.repeat(" ",state.indent*level)}function testImplicitResolving(state,str){var index,length,type;for(index=0,length=state.implicitTypes.length;index<length;index+=1){type=state.implicitTypes[index];if(type.resolve(str)){return true}}return false}function isWhitespace(c){return c===CHAR_SPACE||c===CHAR_TAB}function isPrintable(c){return 32<=c&&c<=126||161<=c&&c<=55295&&c!==8232&&c!==8233||57344<=c&&c<=65533&&c!==CHAR_BOM||65536<=c&&c<=1114111}function isNsCharOrWhitespace(c){return isPrintable(c)&&c!==CHAR_BOM&&c!==CHAR_CARRIAGE_RETURN&&c!==CHAR_LINE_FEED}function isPlainSafe(c,prev,inblock){var cIsNsCharOrWhitespace=isNsCharOrWhitespace(c);var cIsNsChar=cIsNsCharOrWhitespace&&!isWhitespace(c);return(inblock?cIsNsCharOrWhitespace:cIsNsCharOrWhitespace&&c!==CHAR_COMMA&&c!==CHAR_LEFT_SQUARE_BRACKET&&c!==CHAR_RIGHT_SQUARE_BRACKET&&c!==CHAR_LEFT_CURLY_BRACKET&&c!==CHAR_RIGHT_CURLY_BRACKET)&&c!==CHAR_SHARP&&!(prev===CHAR_COLON&&!cIsNsChar)||isNsCharOrWhitespace(prev)&&!isWhitespace(prev)&&c===CHAR_SHARP||prev===CHAR_COLON&&cIsNsChar}function isPlainSafeFirst(c){return isPrintable(c)&&c!==CHAR_BOM&&!isWhitespace(c)&&c!==CHAR_MINUS&&c!==CHAR_QUESTION&&c!==CHAR_COLON&&c!==CHAR_COMMA&&c!==CHAR_LEFT_SQUARE_BRACKET&&c!==CHAR_RIGHT_SQUARE_BRACKET&&c!==CHAR_LEFT_CURLY_BRACKET&&c!==CHAR_RIGHT_CURLY_BRACKET&&c!==CHAR_SHARP&&c!==CHAR_AMPERSAND&&c!==CHAR_ASTERISK&&c!==CHAR_EXCLAMATION&&c!==CHAR_VERTICAL_LINE&&c!==CHAR_EQUALS&&c!==CHAR_GREATER_THAN&&c!==CHAR_SINGLE_QUOTE&&c!==CHAR_DOUBLE_QUOTE&&c!==CHAR_PERCENT&&c!==CHAR_COMMERCIAL_AT&&c!==CHAR_GRAVE_ACCENT}function isPlainSafeLast(c){return!isWhitespace(c)&&c!==CHAR_COLON}function codePointAt(string,pos){var first=string.charCodeAt(pos),second;if(first>=55296&&first<=56319&&pos+1<string.length){second=string.charCodeAt(pos+1);if(second>=56320&&second<=57343){return(first-55296)*1024+second-56320+65536}}return first}function needIndentIndicator(string){var leadingSpaceRe=/^\n* /;return leadingSpaceRe.test(string)}var STYLE_PLAIN=1,STYLE_SINGLE=2,STYLE_LITERAL=3,STYLE_FOLDED=4,STYLE_DOUBLE=5;function chooseScalarStyle(string,singleLineOnly,indentPerLevel,lineWidth,testAmbiguousType,quotingType,forceQuotes,inblock){var i;var char=0;var prevChar=null;var hasLineBreak=false;var hasFoldableLine=false;var shouldTrackWidth=lineWidth!==-1;var previousLineBreak=-1;var plain=isPlainSafeFirst(codePointAt(string,0))&&isPlainSafeLast(codePointAt(string,string.length-1));if(singleLineOnly||forceQuotes){for(i=0;i<string.length;char>=65536?i+=2:i++){char=codePointAt(string,i);if(!isPrintable(char)){return STYLE_DOUBLE}plain=plain&&isPlainSafe(char,prevChar,inblock);prevChar=char}}else{for(i=0;i<string.length;char>=65536?i+=2:i++){char=codePointAt(string,i);if(char===CHAR_LINE_FEED){hasLineBreak=true;if(shouldTrackWidth){hasFoldableLine=hasFoldableLine||i-previousLineBreak-1>lineWidth&&string[previousLineBreak+1]!==" ";previousLineBreak=i}}else if(!isPrintable(char)){return STYLE_DOUBLE}plain=plain&&isPlainSafe(char,prevChar,inblock);prevChar=char}hasFoldableLine=hasFoldableLine||shouldTrackWidth&&(i-previousLineBreak-1>lineWidth&&string[previousLineBreak+1]!==" ")}if(!hasLineBreak&&!hasFoldableLine){if(plain&&!forceQuotes&&!testAmbiguousType(string)){return STYLE_PLAIN}return quotingType===QUOTING_TYPE_DOUBLE?STYLE_DOUBLE:STYLE_SINGLE}if(indentPerLevel>9&&needIndentIndicator(string)){return STYLE_DOUBLE}if(!forceQuotes){return hasFoldableLine?STYLE_FOLDED:STYLE_LITERAL}return quotingType===QUOTING_TYPE_DOUBLE?STYLE_DOUBLE:STYLE_SINGLE}function writeScalar(state,string,level,iskey,inblock){state.dump=function(){if(string.length===0){return state.quotingType===QUOTING_TYPE_DOUBLE?'""':"''"}if(!state.noCompatMode){if(DEPRECATED_BOOLEANS_SYNTAX.indexOf(string)!==-1||DEPRECATED_BASE60_SYNTAX.test(string)){return state.quotingType===QUOTING_TYPE_DOUBLE?'"'+string+'"':"'"+string+"'"}}var indent=state.indent*Math.max(1,level);var lineWidth=state.lineWidth===-1?-1:Math.max(Math.min(state.lineWidth,40),state.lineWidth-indent);var singleLineOnly=iskey||state.flowLevel>-1&&level>=state.flowLevel;function testAmbiguity(string){return testImplicitResolving(state,string)}switch(chooseScalarStyle(string,singleLineOnly,state.indent,lineWidth,testAmbiguity,state.quotingType,state.forceQuotes&&!iskey,inblock)){case STYLE_PLAIN:return string;case STYLE_SINGLE:return"'"+string.replace(/'/g,"''")+"'";case STYLE_LITERAL:return"|"+blockHeader(string,state.indent)+dropEndingNewline(indentString(string,indent));case STYLE_FOLDED:return">"+blockHeader(string,state.indent)+dropEndingNewline(indentString(foldString(string,lineWidth),indent));case STYLE_DOUBLE:return'"'+escapeString(string,lineWidth)+'"';default:throw new YAMLException("impossible error: invalid scalar style")}}()}function blockHeader(string,indentPerLevel){var indentIndicator=needIndentIndicator(string)?String(indentPerLevel):"";var clip=string[string.length-1]==="\n";var keep=clip&&(string[string.length-2]==="\n"||string==="\n");var chomp=keep?"+":clip?"":"-";return indentIndicator+chomp+"\n"}function dropEndingNewline(string){return string[string.length-1]==="\n"?string.slice(0,-1):string}function foldString(string,width){var lineRe=/(\n+)([^\n]*)/g;var result=function(){var nextLF=string.indexOf("\n");nextLF=nextLF!==-1?nextLF:string.length;lineRe.lastIndex=nextLF;return foldLine(string.slice(0,nextLF),width)}();var prevMoreIndented=string[0]==="\n"||string[0]===" ";var moreIndented;var match;while(match=lineRe.exec(string)){var prefix=match[1],line=match[2];moreIndented=line[0]===" ";result+=prefix+(!prevMoreIndented&&!moreIndented&&line!==""?"\n":"")+foldLine(line,width);prevMoreIndented=moreIndented}return result}function foldLine(line,width){if(line===""||line[0]===" ")return line;var breakRe=/ [^ ]/g;var match;var start=0,end,curr=0,next=0;var result="";while(match=breakRe.exec(line)){next=match.index;if(next-start>width){end=curr>start?curr:next;result+="\n"+line.slice(start,end);start=end+1}curr=next}result+="\n";if(line.length-start>width&&curr>start){result+=line.slice(start,curr)+"\n"+line.slice(curr+1)}else{result+=line.slice(start)}return result.slice(1)}function escapeString(string){var result="";var char=0;var escapeSeq;for(var i=0;i<string.length;char>=65536?i+=2:i++){char=codePointAt(string,i);escapeSeq=ESCAPE_SEQUENCES[char];if(!escapeSeq&&isPrintable(char)){result+=string[i];if(char>=65536)result+=string[i+1]}else{result+=escapeSeq||encodeHex(char)}}return result}function writeFlowSequence(state,level,object){var _result="",_tag=state.tag,index,length,value;for(index=0,length=object.length;index<length;index+=1){value=object[index];if(state.replacer){value=state.replacer.call(object,String(index),value)}if(writeNode(state,level,value,false,false)||typeof value==="undefined"&&writeNode(state,level,null,false,false)){if(_result!=="")_result+=","+(!state.condenseFlow?" ":"");_result+=state.dump}}state.tag=_tag;state.dump="["+_result+"]"}function writeBlockSequence(state,level,object,compact){var _result="",_tag=state.tag,index,length,value;for(index=0,length=object.length;index<length;index+=1){value=object[index];if(state.replacer){value=state.replacer.call(object,String(index),value)}if(writeNode(state,level+1,value,true,true,false,true)||typeof value==="undefined"&&writeNode(state,level+1,null,true,true,false,true)){if(!compact||_result!==""){_result+=generateNextLine(state,level)}if(state.dump&&CHAR_LINE_FEED===state.dump.charCodeAt(0)){_result+="-"}else{_result+="- "}_result+=state.dump}}state.tag=_tag;state.dump=_result||"[]"}function writeFlowMapping(state,level,object){var _result="",_tag=state.tag,objectKeyList=Object.keys(object),index,length,objectKey,objectValue,pairBuffer;for(index=0,length=objectKeyList.length;index<length;index+=1){pairBuffer="";if(_result!=="")pairBuffer+=", ";if(state.condenseFlow)pairBuffer+='"';objectKey=objectKeyList[index];objectValue=object[objectKey];if(state.replacer){objectValue=state.replacer.call(object,objectKey,objectValue)}if(!writeNode(state,level,objectKey,false,false)){continue}if(state.dump.length>1024)pairBuffer+="? ";pairBuffer+=state.dump+(state.condenseFlow?'"':"")+":"+(state.condenseFlow?"":" ");if(!writeNode(state,level,objectValue,false,false)){continue}pairBuffer+=state.dump;_result+=pairBuffer}state.tag=_tag;state.dump="{"+_result+"}"}function writeBlockMapping(state,level,object,compact){var _result="",_tag=state.tag,objectKeyList=Object.keys(object),index,length,objectKey,objectValue,explicitPair,pairBuffer;if(state.sortKeys===true){objectKeyList.sort()}else if(typeof state.sortKeys==="function"){objectKeyList.sort(state.sortKeys)}else if(state.sortKeys){throw new YAMLException("sortKeys must be a boolean or a function")}for(index=0,length=objectKeyList.length;index<length;index+=1){pairBuffer="";if(!compact||_result!==""){pairBuffer+=generateNextLine(state,level)}objectKey=objectKeyList[index];objectValue=object[objectKey];if(state.replacer){objectValue=state.replacer.call(object,objectKey,objectValue)}if(!writeNode(state,level+1,objectKey,true,true,true)){continue}explicitPair=state.tag!==null&&state.tag!=="?"||state.dump&&state.dump.length>1024;if(explicitPair){if(state.dump&&CHAR_LINE_FEED===state.dump.charCodeAt(0)){pairBuffer+="?"}else{pairBuffer+="? "}}pairBuffer+=state.dump;if(explicitPair){pairBuffer+=generateNextLine(state,level)}if(!writeNode(state,level+1,objectValue,true,explicitPair)){continue}if(state.dump&&CHAR_LINE_FEED===state.dump.charCodeAt(0)){pairBuffer+=":"}else{pairBuffer+=": "}pairBuffer+=state.dump;_result+=pairBuffer}state.tag=_tag;state.dump=_result||"{}"}function detectType(state,object,explicit){var _result,typeList,index,length,type,style;typeList=explicit?state.explicitTypes:state.implicitTypes;for(index=0,length=typeList.length;index<length;index+=1){type=typeList[index];if((type.instanceOf||type.predicate)&&(!type.instanceOf||typeof object==="object"&&object instanceof type.instanceOf)&&(!type.predicate||type.predicate(object))){if(explicit){if(type.multi&&type.representName){state.tag=type.representName(object)}else{state.tag=type.tag}}else{state.tag="?"}if(type.represent){style=state.styleMap[type.tag]||type.defaultStyle;if(_toString.call(type.represent)==="[object Function]"){_result=type.represent(object,style)}else if(_hasOwnProperty.call(type.represent,style)){_result=type.represent[style](object,style)}else{throw new YAMLException("!<"+type.tag+'> tag resolver accepts not "'+style+'" style')}state.dump=_result}return true}}return false}function writeNode(state,level,object,block,compact,iskey,isblockseq){state.tag=null;state.dump=object;if(!detectType(state,object,false)){detectType(state,object,true)}var type=_toString.call(state.dump);var inblock=block;var tagStr;if(block){block=state.flowLevel<0||state.flowLevel>level}var objectOrArray=type==="[object Object]"||type==="[object Array]",duplicateIndex,duplicate;if(objectOrArray){duplicateIndex=state.duplicates.indexOf(object);duplicate=duplicateIndex!==-1}if(state.tag!==null&&state.tag!=="?"||duplicate||state.indent!==2&&level>0){compact=false}if(duplicate&&state.usedDuplicates[duplicateIndex]){state.dump="*ref_"+duplicateIndex}else{if(objectOrArray&&duplicate&&!state.usedDuplicates[duplicateIndex]){state.usedDuplicates[duplicateIndex]=true}if(type==="[object Object]"){if(block&&Object.keys(state.dump).length!==0){writeBlockMapping(state,level,state.dump,compact);if(duplicate){state.dump="&ref_"+duplicateIndex+state.dump}}else{writeFlowMapping(state,level,state.dump);if(duplicate){state.dump="&ref_"+duplicateIndex+" "+state.dump}}}else if(type==="[object Array]"){if(block&&state.dump.length!==0){if(state.noArrayIndent&&!isblockseq&&level>0){writeBlockSequence(state,level-1,state.dump,compact)}else{writeBlockSequence(state,level,state.dump,compact)}if(duplicate){state.dump="&ref_"+duplicateIndex+state.dump}}else{writeFlowSequence(state,level,state.dump);if(duplicate){state.dump="&ref_"+duplicateIndex+" "+state.dump}}}else if(type==="[object String]"){if(state.tag!=="?"){writeScalar(state,state.dump,level,iskey,inblock)}}else if(type==="[object Undefined]"){return false}else{if(state.skipInvalid)return false;throw new YAMLException("unacceptable kind of an object to dump "+type)}if(state.tag!==null&&state.tag!=="?"){tagStr=encodeURI(state.tag[0]==="!"?state.tag.slice(1):state.tag).replace(/!/g,"%21");if(state.tag[0]==="!"){tagStr="!"+tagStr}else if(tagStr.slice(0,18)==="tag:yaml.org,2002:"){tagStr="!!"+tagStr.slice(18)}else{tagStr="!<"+tagStr+">"}state.dump=tagStr+" "+state.dump}}return true}function getDuplicateReferences(object,state){var objects=[],duplicatesIndexes=[],index,length;inspectNode(object,objects,duplicatesIndexes);for(index=0,length=duplicatesIndexes.length;index<length;index+=1){state.duplicates.push(objects[duplicatesIndexes[index]])}state.usedDuplicates=new Array(length)}function inspectNode(object,objects,duplicatesIndexes){var objectKeyList,index,length;if(object!==null&&typeof object==="object"){index=objects.indexOf(object);if(index!==-1){if(duplicatesIndexes.indexOf(index)===-1){duplicatesIndexes.push(index)}}else{objects.push(object);if(Array.isArray(object)){for(index=0,length=object.length;index<length;index+=1){inspectNode(object[index],objects,duplicatesIndexes)}}else{objectKeyList=Object.keys(object);for(index=0,length=objectKeyList.length;index<length;index+=1){inspectNode(object[objectKeyList[index]],objects,duplicatesIndexes)}}}}}function dump(input,options){options=options||{};var state=new State(options);if(!state.noRefs)getDuplicateReferences(input,state);var value=input;if(state.replacer){value=state.replacer.call({"":value},"",value)}if(writeNode(state,0,value,true,true))return state.dump+"\n";return""}module.exports.dump=dump},{"./common":2,"./exception":4,"./schema/default":8}],4:[function(require,module,exports){"use strict";function formatError(exception,compact){var where="",message=exception.reason||"(unknown reason)";if(!exception.mark)return message;if(exception.mark.name){where+='in "'+exception.mark.name+'" '}where+="("+(exception.mark.line+1)+":"+(exception.mark.column+1)+")";if(!compact&&exception.mark.snippet){where+="\n\n"+exception.mark.snippet}return message+" "+where}function YAMLException(reason,mark){Error.call(this);this.name="YAMLException";this.reason=reason;this.mark=mark;this.message=formatError(this,false);if(Error.captureStackTrace){Error.captureStackTrace(this,this.constructor)}else{this.stack=(new Error).stack||""}}YAMLException.prototype=Object.create(Error.prototype);YAMLException.prototype.constructor=YAMLException;YAMLException.prototype.toString=function toString(compact){return this.name+": "+formatError(this,compact)};module.exports=YAMLException},{}],5:[function(require,module,exports){"use strict";var common=require("./common");var YAMLException=require("./exception");var makeSnippet=require("./snippet");var DEFAULT_SCHEMA=require("./schema/default");var _hasOwnProperty=Object.prototype.hasOwnProperty;var CONTEXT_FLOW_IN=1;var CONTEXT_FLOW_OUT=2;var CONTEXT_BLOCK_IN=3;var CONTEXT_BLOCK_OUT=4;var CHOMPING_CLIP=1;var CHOMPING_STRIP=2;var CHOMPING_KEEP=3;var PATTERN_NON_PRINTABLE=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/;var PATTERN_NON_ASCII_LINE_BREAKS=/[\x85\u2028\u2029]/;var PATTERN_FLOW_INDICATORS=/[,\[\]\{\}]/;var PATTERN_TAG_HANDLE=/^(?:!|!!|![a-z\-]+!)$/i;var PATTERN_TAG_URI=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function _class(obj){return Object.prototype.toString.call(obj)}function is_EOL(c){return c===10||c===13}function is_WHITE_SPACE(c){return c===9||c===32}function is_WS_OR_EOL(c){return c===9||c===32||c===10||c===13}function is_FLOW_INDICATOR(c){return c===44||c===91||c===93||c===123||c===125}function fromHexCode(c){var lc;if(48<=c&&c<=57){return c-48}lc=c|32;if(97<=lc&&lc<=102){return lc-97+10}return-1}function escapedHexLen(c){if(c===120){return 2}if(c===117){return 4}if(c===85){return 8}return 0}function fromDecimalCode(c){if(48<=c&&c<=57){return c-48}return-1}function simpleEscapeSequence(c){return c===48?"\0":c===97?"":c===98?"\b":c===116?"\t":c===9?"\t":c===110?"\n":c===118?"\v":c===102?"\f":c===114?"\r":c===101?"":c===32?" ":c===34?'"':c===47?"/":c===92?"\\":c===78?"":c===95?" ":c===76?"\u2028":c===80?"\u2029":""}function charFromCodepoint(c){if(c<=65535){return String.fromCharCode(c)}return String.fromCharCode((c-65536>>10)+55296,(c-65536&1023)+56320)}var simpleEscapeCheck=new Array(256);var simpleEscapeMap=new Array(256);for(var i=0;i<256;i++){simpleEscapeCheck[i]=simpleEscapeSequence(i)?1:0;simpleEscapeMap[i]=simpleEscapeSequence(i)}function State(input,options){this.input=input;this.filename=options["filename"]||null;this.schema=options["schema"]||DEFAULT_SCHEMA;this.onWarning=options["onWarning"]||null;this.legacy=options["legacy"]||false;this.json=options["json"]||false;this.listener=options["listener"]||null;this.implicitTypes=this.schema.compiledImplicit;this.typeMap=this.schema.compiledTypeMap;this.length=input.length;this.position=0;this.line=0;this.lineStart=0;this.lineIndent=0;this.firstTabInLine=-1;this.documents=[]}function generateError(state,message){var mark={name:state.filename,buffer:state.input.slice(0,-1),position:state.position,line:state.line,column:state.position-state.lineStart};mark.snippet=makeSnippet(mark);return new YAMLException(message,mark)}function throwError(state,message){throw generateError(state,message)}function throwWarning(state,message){if(state.onWarning){state.onWarning.call(null,generateError(state,message))}}var directiveHandlers={YAML:function handleYamlDirective(state,name,args){var match,major,minor;if(state.version!==null){throwError(state,"duplication of %YAML directive")}if(args.length!==1){throwError(state,"YAML directive accepts exactly one argument")}match=/^([0-9]+)\.([0-9]+)$/.exec(args[0]);if(match===null){throwError(state,"ill-formed argument of the YAML directive")}major=parseInt(match[1],10);minor=parseInt(match[2],10);if(major!==1){throwError(state,"unacceptable YAML version of the document")}state.version=args[0];state.checkLineBreaks=minor<2;if(minor!==1&&minor!==2){throwWarning(state,"unsupported YAML version of the document")}},TAG:function handleTagDirective(state,name,args){var handle,prefix;if(args.length!==2){throwError(state,"TAG directive accepts exactly two arguments")}handle=args[0];prefix=args[1];if(!PATTERN_TAG_HANDLE.test(handle)){throwError(state,"ill-formed tag handle (first argument) of the TAG directive")}if(_hasOwnProperty.call(state.tagMap,handle)){throwError(state,'there is a previously declared suffix for "'+handle+'" tag handle')}if(!PATTERN_TAG_URI.test(prefix)){throwError(state,"ill-formed tag prefix (second argument) of the TAG directive")}try{prefix=decodeURIComponent(prefix)}catch(err){throwError(state,"tag prefix is malformed: "+prefix)}state.tagMap[handle]=prefix}};function captureSegment(state,start,end,checkJson){var _position,_length,_character,_result;if(start<end){_result=state.input.slice(start,end);if(checkJson){for(_position=0,_length=_result.length;_position<_length;_position+=1){_character=_result.charCodeAt(_position);if(!(_character===9||32<=_character&&_character<=1114111)){throwError(state,"expected valid JSON character")}}}else if(PATTERN_NON_PRINTABLE.test(_result)){throwError(state,"the stream contains non-printable characters")}state.result+=_result}}function mergeMappings(state,destination,source,overridableKeys){var sourceKeys,key,index,quantity;if(!common.isObject(source)){throwError(state,"cannot merge mappings; the provided source object is unacceptable")}sourceKeys=Object.keys(source);for(index=0,quantity=sourceKeys.length;index<quantity;index+=1){key=sourceKeys[index];if(!_hasOwnProperty.call(destination,key)){destination[key]=source[key];overridableKeys[key]=true}}}function storeMappingPair(state,_result,overridableKeys,keyTag,keyNode,valueNode,startLine,startLineStart,startPos){var index,quantity;if(Array.isArray(keyNode)){keyNode=Array.prototype.slice.call(keyNode);for(index=0,quantity=keyNode.length;index<quantity;index+=1){if(Array.isArray(keyNode[index])){throwError(state,"nested arrays are not supported inside keys")}if(typeof keyNode==="object"&&_class(keyNode[index])==="[object Object]"){keyNode[index]="[object Object]"}}}if(typeof keyNode==="object"&&_class(keyNode)==="[object Object]"){keyNode="[object Object]"}keyNode=String(keyNode);if(_result===null){_result={}}if(keyTag==="tag:yaml.org,2002:merge"){if(Array.isArray(valueNode)){for(index=0,quantity=valueNode.length;index<quantity;index+=1){mergeMappings(state,_result,valueNode[index],overridableKeys)}}else{mergeMappings(state,_result,valueNode,overridableKeys)}}else{if(!state.json&&!_hasOwnProperty.call(overridableKeys,keyNode)&&_hasOwnProperty.call(_result,keyNode)){state.line=startLine||state.line;state.lineStart=startLineStart||state.lineStart;state.position=startPos||state.position;throwError(state,"duplicated mapping key")}if(keyNode==="__proto__"){Object.defineProperty(_result,keyNode,{configurable:true,enumerable:true,writable:true,value:valueNode})}else{_result[keyNode]=valueNode}delete overridableKeys[keyNode]}return _result}function readLineBreak(state){var ch;ch=state.input.charCodeAt(state.position);if(ch===10){state.position++}else if(ch===13){state.position++;if(state.input.charCodeAt(state.position)===10){state.position++}}else{throwError(state,"a line break is expected")}state.line+=1;state.lineStart=state.position;state.firstTabInLine=-1}function skipSeparationSpace(state,allowComments,checkIndent){var lineBreaks=0,ch=state.input.charCodeAt(state.position);while(ch!==0){while(is_WHITE_SPACE(ch)){if(ch===9&&state.firstTabInLine===-1){state.firstTabInLine=state.position}ch=state.input.charCodeAt(++state.position)}if(allowComments&&ch===35){do{ch=state.input.charCodeAt(++state.position)}while(ch!==10&&ch!==13&&ch!==0)}if(is_EOL(ch)){readLineBreak(state);ch=state.input.charCodeAt(state.position);lineBreaks++;state.lineIndent=0;while(ch===32){state.lineIndent++;ch=state.input.charCodeAt(++state.position)}}else{break}}if(checkIndent!==-1&&lineBreaks!==0&&state.lineIndent<checkIndent){throwWarning(state,"deficient indentation")}return lineBreaks}function testDocumentSeparator(state){var _position=state.position,ch;ch=state.input.charCodeAt(_position);if((ch===45||ch===46)&&ch===state.input.charCodeAt(_position+1)&&ch===state.input.charCodeAt(_position+2)){_position+=3;ch=state.input.charCodeAt(_position);if(ch===0||is_WS_OR_EOL(ch)){return true}}return false}function writeFoldedLines(state,count){if(count===1){state.result+=" "}else if(count>1){state.result+=common.repeat("\n",count-1)}}function readPlainScalar(state,nodeIndent,withinFlowCollection){var preceding,following,captureStart,captureEnd,hasPendingContent,_line,_lineStart,_lineIndent,_kind=state.kind,_result=state.result,ch;ch=state.input.charCodeAt(state.position);if(is_WS_OR_EOL(ch)||is_FLOW_INDICATOR(ch)||ch===35||ch===38||ch===42||ch===33||ch===124||ch===62||ch===39||ch===34||ch===37||ch===64||ch===96){return false}if(ch===63||ch===45){following=state.input.charCodeAt(state.position+1);if(is_WS_OR_EOL(following)||withinFlowCollection&&is_FLOW_INDICATOR(following)){return false}}state.kind="scalar";state.result="";captureStart=captureEnd=state.position;hasPendingContent=false;while(ch!==0){if(ch===58){following=state.input.charCodeAt(state.position+1);if(is_WS_OR_EOL(following)||withinFlowCollection&&is_FLOW_INDICATOR(following)){break}}else if(ch===35){preceding=state.input.charCodeAt(state.position-1);if(is_WS_OR_EOL(preceding)){break}}else if(state.position===state.lineStart&&testDocumentSeparator(state)||withinFlowCollection&&is_FLOW_INDICATOR(ch)){break}else if(is_EOL(ch)){_line=state.line;_lineStart=state.lineStart;_lineIndent=state.lineIndent;skipSeparationSpace(state,false,-1);if(state.lineIndent>=nodeIndent){hasPendingContent=true;ch=state.input.charCodeAt(state.position);continue}else{state.position=captureEnd;state.line=_line;state.lineStart=_lineStart;state.lineIndent=_lineIndent;break}}if(hasPendingContent){captureSegment(state,captureStart,captureEnd,false);writeFoldedLines(state,state.line-_line);captureStart=captureEnd=state.position;hasPendingContent=false}if(!is_WHITE_SPACE(ch)){captureEnd=state.position+1}ch=state.input.charCodeAt(++state.position)}captureSegment(state,captureStart,captureEnd,false);if(state.result){return true}state.kind=_kind;state.result=_result;return false}function readSingleQuotedScalar(state,nodeIndent){var ch,captureStart,captureEnd;ch=state.input.charCodeAt(state.position);if(ch!==39){return false}state.kind="scalar";state.result="";state.position++;captureStart=captureEnd=state.position;while((ch=state.input.charCodeAt(state.position))!==0){if(ch===39){captureSegment(state,captureStart,state.position,true);ch=state.input.charCodeAt(++state.position);if(ch===39){captureStart=state.position;state.position++;captureEnd=state.position}else{return true}}else if(is_EOL(ch)){captureSegment(state,captureStart,captureEnd,true);writeFoldedLines(state,skipSeparationSpace(state,false,nodeIndent));captureStart=captureEnd=state.position}else if(state.position===state.lineStart&&testDocumentSeparator(state)){throwError(state,"unexpected end of the document within a single quoted scalar")}else{state.position++;captureEnd=state.position}}throwError(state,"unexpected end of the stream within a single quoted scalar")}function readDoubleQuotedScalar(state,nodeIndent){var captureStart,captureEnd,hexLength,hexResult,tmp,ch;ch=state.input.charCodeAt(state.position);if(ch!==34){return false}state.kind="scalar";state.result="";state.position++;captureStart=captureEnd=state.position;while((ch=state.input.charCodeAt(state.position))!==0){if(ch===34){captureSegment(state,captureStart,state.position,true);state.position++;return true}else if(ch===92){captureSegment(state,captureStart,state.position,true);ch=state.input.charCodeAt(++state.position);if(is_EOL(ch)){skipSeparationSpace(state,false,nodeIndent)}else if(ch<256&&simpleEscapeCheck[ch]){state.result+=simpleEscapeMap[ch];state.position++}else if((tmp=escapedHexLen(ch))>0){hexLength=tmp;hexResult=0;for(;hexLength>0;hexLength--){ch=state.input.charCodeAt(++state.position);if((tmp=fromHexCode(ch))>=0){hexResult=(hexResult<<4)+tmp}else{throwError(state,"expected hexadecimal character")}}state.result+=charFromCodepoint(hexResult);state.position++}else{throwError(state,"unknown escape sequence")}captureStart=captureEnd=state.position}else if(is_EOL(ch)){captureSegment(state,captureStart,captureEnd,true);writeFoldedLines(state,skipSeparationSpace(state,false,nodeIndent));captureStart=captureEnd=state.position}else if(state.position===state.lineStart&&testDocumentSeparator(state)){throwError(state,"unexpected end of the document within a double quoted scalar")}else{state.position++;captureEnd=state.position}}throwError(state,"unexpected end of the stream within a double quoted scalar")}function readFlowCollection(state,nodeIndent){var readNext=true,_line,_lineStart,_pos,_tag=state.tag,_result,_anchor=state.anchor,following,terminator,isPair,isExplicitPair,isMapping,overridableKeys=Object.create(null),keyNode,keyTag,valueNode,ch;ch=state.input.charCodeAt(state.position);if(ch===91){terminator=93;isMapping=false;_result=[]}else if(ch===123){terminator=125;isMapping=true;_result={}}else{return false}if(state.anchor!==null){state.anchorMap[state.anchor]=_result}ch=state.input.charCodeAt(++state.position);while(ch!==0){skipSeparationSpace(state,true,nodeIndent);ch=state.input.charCodeAt(state.position);if(ch===terminator){state.position++;state.tag=_tag;state.anchor=_anchor;state.kind=isMapping?"mapping":"sequence";state.result=_result;return true}else if(!readNext){throwError(state,"missed comma between flow collection entries")}else if(ch===44){throwError(state,"expected the node content, but found ','")}keyTag=keyNode=valueNode=null;isPair=isExplicitPair=false;if(ch===63){following=state.input.charCodeAt(state.position+1);if(is_WS_OR_EOL(following)){isPair=isExplicitPair=true;state.position++;skipSeparationSpace(state,true,nodeIndent)}}_line=state.line;_lineStart=state.lineStart;_pos=state.position;composeNode(state,nodeIndent,CONTEXT_FLOW_IN,false,true);keyTag=state.tag;keyNode=state.result;skipSeparationSpace(state,true,nodeIndent);ch=state.input.charCodeAt(state.position);if((isExplicitPair||state.line===_line)&&ch===58){isPair=true;ch=state.input.charCodeAt(++state.position);skipSeparationSpace(state,true,nodeIndent);composeNode(state,nodeIndent,CONTEXT_FLOW_IN,false,true);valueNode=state.result}if(isMapping){storeMappingPair(state,_result,overridableKeys,keyTag,keyNode,valueNode,_line,_lineStart,_pos)}else if(isPair){_result.push(storeMappingPair(state,null,overridableKeys,keyTag,keyNode,valueNode,_line,_lineStart,_pos))}else{_result.push(keyNode)}skipSeparationSpace(state,true,nodeIndent);ch=state.input.charCodeAt(state.position);if(ch===44){readNext=true;ch=state.input.charCodeAt(++state.position)}else{readNext=false}}throwError(state,"unexpected end of the stream within a flow collection")}function readBlockScalar(state,nodeIndent){var captureStart,folding,chomping=CHOMPING_CLIP,didReadContent=false,detectedIndent=false,textIndent=nodeIndent,emptyLines=0,atMoreIndented=false,tmp,ch;ch=state.input.charCodeAt(state.position);if(ch===124){folding=false}else if(ch===62){folding=true}else{return false}state.kind="scalar";state.result="";while(ch!==0){ch=state.input.charCodeAt(++state.position);if(ch===43||ch===45){if(CHOMPING_CLIP===chomping){chomping=ch===43?CHOMPING_KEEP:CHOMPING_STRIP}else{throwError(state,"repeat of a chomping mode identifier")}}else if((tmp=fromDecimalCode(ch))>=0){if(tmp===0){throwError(state,"bad explicit indentation width of a block scalar; it cannot be less than one")}else if(!detectedIndent){textIndent=nodeIndent+tmp-1;detectedIndent=true}else{throwError(state,"repeat of an indentation width identifier")}}else{break}}if(is_WHITE_SPACE(ch)){do{ch=state.input.charCodeAt(++state.position)}while(is_WHITE_SPACE(ch));if(ch===35){do{ch=state.input.charCodeAt(++state.position)}while(!is_EOL(ch)&&ch!==0)}}while(ch!==0){readLineBreak(state);state.lineIndent=0;ch=state.input.charCodeAt(state.position);while((!detectedIndent||state.lineIndent<textIndent)&&ch===32){state.lineIndent++;ch=state.input.charCodeAt(++state.position)}if(!detectedIndent&&state.lineIndent>textIndent){textIndent=state.lineIndent}if(is_EOL(ch)){emptyLines++;continue}if(state.lineIndent<textIndent){if(chomping===CHOMPING_KEEP){state.result+=common.repeat("\n",didReadContent?1+emptyLines:emptyLines)}else if(chomping===CHOMPING_CLIP){if(didReadContent){state.result+="\n"}}break}if(folding){if(is_WHITE_SPACE(ch)){atMoreIndented=true;state.result+=common.repeat("\n",didReadContent?1+emptyLines:emptyLines)}else if(atMoreIndented){atMoreIndented=false;state.result+=common.repeat("\n",emptyLines+1)}else if(emptyLines===0){if(didReadContent){state.result+=" "}}else{state.result+=common.repeat("\n",emptyLines)}}else{state.result+=common.repeat("\n",didReadContent?1+emptyLines:emptyLines)}didReadContent=true;detectedIndent=true;emptyLines=0;captureStart=state.position;while(!is_EOL(ch)&&ch!==0){ch=state.input.charCodeAt(++state.position)}captureSegment(state,captureStart,state.position,false)}return true}function readBlockSequence(state,nodeIndent){var _line,_tag=state.tag,_anchor=state.anchor,_result=[],following,detected=false,ch;if(state.firstTabInLine!==-1)return false;if(state.anchor!==null){state.anchorMap[state.anchor]=_result}ch=state.input.charCodeAt(state.position);while(ch!==0){if(state.firstTabInLine!==-1){state.position=state.firstTabInLine;throwError(state,"tab characters must not be used in indentation")}if(ch!==45){break}following=state.input.charCodeAt(state.position+1);if(!is_WS_OR_EOL(following)){break}detected=true;state.position++;if(skipSeparationSpace(state,true,-1)){if(state.lineIndent<=nodeIndent){_result.push(null);ch=state.input.charCodeAt(state.position);continue}}_line=state.line;composeNode(state,nodeIndent,CONTEXT_BLOCK_IN,false,true);_result.push(state.result);skipSeparationSpace(state,true,-1);ch=state.input.charCodeAt(state.position);if((state.line===_line||state.lineIndent>nodeIndent)&&ch!==0){throwError(state,"bad indentation of a sequence entry")}else if(state.lineIndent<nodeIndent){break}}if(detected){state.tag=_tag;state.anchor=_anchor;state.kind="sequence";state.result=_result;return true}return false}function readBlockMapping(state,nodeIndent,flowIndent){var following,allowCompact,_line,_keyLine,_keyLineStart,_keyPos,_tag=state.tag,_anchor=state.anchor,_result={},overridableKeys=Object.create(null),keyTag=null,keyNode=null,valueNode=null,atExplicitKey=false,detected=false,ch;if(state.firstTabInLine!==-1)return false;if(state.anchor!==null){state.anchorMap[state.anchor]=_result}ch=state.input.charCodeAt(state.position);while(ch!==0){if(!atExplicitKey&&state.firstTabInLine!==-1){state.position=state.firstTabInLine;throwError(state,"tab characters must not be used in indentation")}following=state.input.charCodeAt(state.position+1);_line=state.line;if((ch===63||ch===58)&&is_WS_OR_EOL(following)){if(ch===63){if(atExplicitKey){storeMappingPair(state,_result,overridableKeys,keyTag,keyNode,null,_keyLine,_keyLineStart,_keyPos);keyTag=keyNode=valueNode=null}detected=true;atExplicitKey=true;allowCompact=true}else if(atExplicitKey){atExplicitKey=false;allowCompact=true}else{throwError(state,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line")}state.position+=1;ch=following}else{_keyLine=state.line;_keyLineStart=state.lineStart;_keyPos=state.position;if(!composeNode(state,flowIndent,CONTEXT_FLOW_OUT,false,true)){break}if(state.line===_line){ch=state.input.charCodeAt(state.position);while(is_WHITE_SPACE(ch)){ch=state.input.charCodeAt(++state.position)}if(ch===58){ch=state.input.charCodeAt(++state.position);if(!is_WS_OR_EOL(ch)){throwError(state,"a whitespace character is expected after the key-value separator within a block mapping")}if(atExplicitKey){storeMappingPair(state,_result,overridableKeys,keyTag,keyNode,null,_keyLine,_keyLineStart,_keyPos);keyTag=keyNode=valueNode=null}detected=true;atExplicitKey=false;allowCompact=false;keyTag=state.tag;keyNode=state.result}else if(detected){throwError(state,"can not read an implicit mapping pair; a colon is missed")}else{state.tag=_tag;state.anchor=_anchor;return true}}else if(detected){throwError(state,"can not read a block mapping entry; a multiline key may not be an implicit key")}else{state.tag=_tag;state.anchor=_anchor;return true}}if(state.line===_line||state.lineIndent>nodeIndent){if(atExplicitKey){_keyLine=state.line;_keyLineStart=state.lineStart;_keyPos=state.position}if(composeNode(state,nodeIndent,CONTEXT_BLOCK_OUT,true,allowCompact)){if(atExplicitKey){keyNode=state.result}else{valueNode=state.result}}if(!atExplicitKey){storeMappingPair(state,_result,overridableKeys,keyTag,keyNode,valueNode,_keyLine,_keyLineStart,_keyPos);keyTag=keyNode=valueNode=null}skipSeparationSpace(state,true,-1);ch=state.input.charCodeAt(state.position)}if((state.line===_line||state.lineIndent>nodeIndent)&&ch!==0){throwError(state,"bad indentation of a mapping entry")}else if(state.lineIndent<nodeIndent){break}}if(atExplicitKey){storeMappingPair(state,_result,overridableKeys,keyTag,keyNode,null,_keyLine,_keyLineStart,_keyPos)}if(detected){state.tag=_tag;state.anchor=_anchor;state.kind="mapping";state.result=_result}return detected}function readTagProperty(state){var _position,isVerbatim=false,isNamed=false,tagHandle,tagName,ch;ch=state.input.charCodeAt(state.position);if(ch!==33)return false;if(state.tag!==null){throwError(state,"duplication of a tag property")}ch=state.input.charCodeAt(++state.position);if(ch===60){isVerbatim=true;ch=state.input.charCodeAt(++state.position)}else if(ch===33){isNamed=true;tagHandle="!!";ch=state.input.charCodeAt(++state.position)}else{tagHandle="!"}_position=state.position;if(isVerbatim){do{ch=state.input.charCodeAt(++state.position)}while(ch!==0&&ch!==62);if(state.position<state.length){tagName=state.input.slice(_position,state.position);ch=state.input.charCodeAt(++state.position)}else{throwError(state,"unexpected end of the stream within a verbatim tag")}}else{while(ch!==0&&!is_WS_OR_EOL(ch)){if(ch===33){if(!isNamed){tagHandle=state.input.slice(_position-1,state.position+1);if(!PATTERN_TAG_HANDLE.test(tagHandle)){throwError(state,"named tag handle cannot contain such characters")}isNamed=true;_position=state.position+1}else{throwError(state,"tag suffix cannot contain exclamation marks")}}ch=state.input.charCodeAt(++state.position)}tagName=state.input.slice(_position,state.position);if(PATTERN_FLOW_INDICATORS.test(tagName)){throwError(state,"tag suffix cannot contain flow indicator characters")}}if(tagName&&!PATTERN_TAG_URI.test(tagName)){throwError(state,"tag name cannot contain such characters: "+tagName)}try{tagName=decodeURIComponent(tagName)}catch(err){throwError(state,"tag name is malformed: "+tagName)}if(isVerbatim){state.tag=tagName}else if(_hasOwnProperty.call(state.tagMap,tagHandle)){state.tag=state.tagMap[tagHandle]+tagName}else if(tagHandle==="!"){state.tag="!"+tagName}else if(tagHandle==="!!"){state.tag="tag:yaml.org,2002:"+tagName}else{throwError(state,'undeclared tag handle "'+tagHandle+'"')}return true}function readAnchorProperty(state){var _position,ch;ch=state.input.charCodeAt(state.position);if(ch!==38)return false;if(state.anchor!==null){throwError(state,"duplication of an anchor property")}ch=state.input.charCodeAt(++state.position);_position=state.position;while(ch!==0&&!is_WS_OR_EOL(ch)&&!is_FLOW_INDICATOR(ch)){ch=state.input.charCodeAt(++state.position)}if(state.position===_position){throwError(state,"name of an anchor node must contain at least one character")}state.anchor=state.input.slice(_position,state.position);return true}function readAlias(state){var _position,alias,ch;ch=state.input.charCodeAt(state.position);if(ch!==42)return false;ch=state.input.charCodeAt(++state.position);_position=state.position;while(ch!==0&&!is_WS_OR_EOL(ch)&&!is_FLOW_INDICATOR(ch)){ch=state.input.charCodeAt(++state.position)}if(state.position===_position){throwError(state,"name of an alias node must contain at least one character")}alias=state.input.slice(_position,state.position);if(!_hasOwnProperty.call(state.anchorMap,alias)){throwError(state,'unidentified alias "'+alias+'"')}state.result=state.anchorMap[alias];skipSeparationSpace(state,true,-1);return true}function composeNode(state,parentIndent,nodeContext,allowToSeek,allowCompact){var allowBlockStyles,allowBlockScalars,allowBlockCollections,indentStatus=1,atNewLine=false,hasContent=false,typeIndex,typeQuantity,typeList,type,flowIndent,blockIndent;if(state.listener!==null){state.listener("open",state)}state.tag=null;state.anchor=null;state.kind=null;state.result=null;allowBlockStyles=allowBlockScalars=allowBlockCollections=CONTEXT_BLOCK_OUT===nodeContext||CONTEXT_BLOCK_IN===nodeContext;if(allowToSeek){if(skipSeparationSpace(state,true,-1)){atNewLine=true;if(state.lineIndent>parentIndent){indentStatus=1}else if(state.lineIndent===parentIndent){indentStatus=0}else if(state.lineIndent<parentIndent){indentStatus=-1}}}if(indentStatus===1){while(readTagProperty(state)||readAnchorProperty(state)){if(skipSeparationSpace(state,true,-1)){atNewLine=true;allowBlockCollections=allowBlockStyles;if(state.lineIndent>parentIndent){indentStatus=1}else if(state.lineIndent===parentIndent){indentStatus=0}else if(state.lineIndent<parentIndent){indentStatus=-1}}else{allowBlockCollections=false}}}if(allowBlockCollections){allowBlockCollections=atNewLine||allowCompact}if(indentStatus===1||CONTEXT_BLOCK_OUT===nodeContext){if(CONTEXT_FLOW_IN===nodeContext||CONTEXT_FLOW_OUT===nodeContext){flowIndent=parentIndent}else{flowIndent=parentIndent+1}blockIndent=state.position-state.lineStart;if(indentStatus===1){if(allowBlockCollections&&(readBlockSequence(state,blockIndent)||readBlockMapping(state,blockIndent,flowIndent))||readFlowCollection(state,flowIndent)){hasContent=true}else{if(allowBlockScalars&&readBlockScalar(state,flowIndent)||readSingleQuotedScalar(state,flowIndent)||readDoubleQuotedScalar(state,flowIndent)){hasContent=true}else if(readAlias(state)){hasContent=true;if(state.tag!==null||state.anchor!==null){throwError(state,"alias node should not have any properties")}}else if(readPlainScalar(state,flowIndent,CONTEXT_FLOW_IN===nodeContext)){hasContent=true;if(state.tag===null){state.tag="?"}}if(state.anchor!==null){state.anchorMap[state.anchor]=state.result}}}else if(indentStatus===0){hasContent=allowBlockCollections&&readBlockSequence(state,blockIndent)}}if(state.tag===null){if(state.anchor!==null){state.anchorMap[state.anchor]=state.result}}else if(state.tag==="?"){if(state.result!==null&&state.kind!=="scalar"){throwError(state,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+state.kind+'"')}for(typeIndex=0,typeQuantity=state.implicitTypes.length;typeIndex<typeQuantity;typeIndex+=1){type=state.implicitTypes[typeIndex];if(type.resolve(state.result)){state.result=type.construct(state.result);state.tag=type.tag;if(state.anchor!==null){state.anchorMap[state.anchor]=state.result}break}}}else if(state.tag!=="!"){if(_hasOwnProperty.call(state.typeMap[state.kind||"fallback"],state.tag)){type=state.typeMap[state.kind||"fallback"][state.tag]}else{type=null;typeList=state.typeMap.multi[state.kind||"fallback"];for(typeIndex=0,typeQuantity=typeList.length;typeIndex<typeQuantity;typeIndex+=1){if(state.tag.slice(0,typeList[typeIndex].tag.length)===typeList[typeIndex].tag){type=typeList[typeIndex];break}}}if(!type){throwError(state,"unknown tag !<"+state.tag+">")}if(state.result!==null&&type.kind!==state.kind){throwError(state,"unacceptable node kind for !<"+state.tag+'> tag; it should be "'+type.kind+'", not "'+state.kind+'"')}if(!type.resolve(state.result,state.tag)){throwError(state,"cannot resolve a node with !<"+state.tag+"> explicit tag")}else{state.result=type.construct(state.result,state.tag);if(state.anchor!==null){state.anchorMap[state.anchor]=state.result}}}if(state.listener!==null){state.listener("close",state)}return state.tag!==null||state.anchor!==null||hasContent}function readDocument(state){var documentStart=state.position,_position,directiveName,directiveArgs,hasDirectives=false,ch;state.version=null;state.checkLineBreaks=state.legacy;state.tagMap=Object.create(null);state.anchorMap=Object.create(null);while((ch=state.input.charCodeAt(state.position))!==0){skipSeparationSpace(state,true,-1);ch=state.input.charCodeAt(state.position);if(state.lineIndent>0||ch!==37){break}hasDirectives=true;ch=state.input.charCodeAt(++state.position);_position=state.position;while(ch!==0&&!is_WS_OR_EOL(ch)){ch=state.input.charCodeAt(++state.position)}directiveName=state.input.slice(_position,state.position);directiveArgs=[];if(directiveName.length<1){throwError(state,"directive name must not be less than one character in length")}while(ch!==0){while(is_WHITE_SPACE(ch)){ch=state.input.charCodeAt(++state.position)}if(ch===35){do{ch=state.input.charCodeAt(++state.position)}while(ch!==0&&!is_EOL(ch));break}if(is_EOL(ch))break;_position=state.position;while(ch!==0&&!is_WS_OR_EOL(ch)){ch=state.input.charCodeAt(++state.position)}directiveArgs.push(state.input.slice(_position,state.position))}if(ch!==0)readLineBreak(state);if(_hasOwnProperty.call(directiveHandlers,directiveName)){directiveHandlers[directiveName](state,directiveName,directiveArgs)}else{throwWarning(state,'unknown document directive "'+directiveName+'"')}}skipSeparationSpace(state,true,-1);if(state.lineIndent===0&&state.input.charCodeAt(state.position)===45&&state.input.charCodeAt(state.position+1)===45&&state.input.charCodeAt(state.position+2)===45){state.position+=3;skipSeparationSpace(state,true,-1)}else if(hasDirectives){throwError(state,"directives end mark is expected")}composeNode(state,state.lineIndent-1,CONTEXT_BLOCK_OUT,false,true);skipSeparationSpace(state,true,-1);if(state.checkLineBreaks&&PATTERN_NON_ASCII_LINE_BREAKS.test(state.input.slice(documentStart,state.position))){throwWarning(state,"non-ASCII line breaks are interpreted as content")}state.documents.push(state.result);if(state.position===state.lineStart&&testDocumentSeparator(state)){if(state.input.charCodeAt(state.position)===46){state.position+=3;skipSeparationSpace(state,true,-1)}return}if(state.position<state.length-1){throwError(state,"end of the stream or a document separator is expected")}else{return}}function loadDocuments(input,options){input=String(input);options=options||{};if(input.length!==0){if(input.charCodeAt(input.length-1)!==10&&input.charCodeAt(input.length-1)!==13){input+="\n"}if(input.charCodeAt(0)===65279){input=input.slice(1)}}var state=new State(input,options);var nullpos=input.indexOf("\0");if(nullpos!==-1){state.position=nullpos;throwError(state,"null byte is not allowed in input")}state.input+="\0";while(state.input.charCodeAt(state.position)===32){state.lineIndent+=1;state.position+=1}while(state.position<state.length-1){readDocument(state)}return state.documents}function loadAll(input,iterator,options){if(iterator!==null&&typeof iterator==="object"&&typeof options==="undefined"){options=iterator;iterator=null}var documents=loadDocuments(input,options);if(typeof iterator!=="function"){return documents}for(var index=0,length=documents.length;index<length;index+=1){iterator(documents[index])}}function load(input,options){var documents=loadDocuments(input,options);if(documents.length===0){return undefined}else if(documents.length===1){return documents[0]}throw new YAMLException("expected a single document in the stream, but found more")}module.exports.loadAll=loadAll;module.exports.load=load},{"./common":2,"./exception":4,"./schema/default":8,"./snippet":11}],6:[function(require,module,exports){"use strict";var YAMLException=require("./exception");var Type=require("./type");function compileList(schema,name){var result=[];schema[name].forEach((function(currentType){var newIndex=result.length;result.forEach((function(previousType,previousIndex){if(previousType.tag===currentType.tag&&previousType.kind===currentType.kind&&previousType.multi===currentType.multi){newIndex=previousIndex}}));result[newIndex]=currentType}));return result}function compileMap(){var result={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},index,length;function collectType(type){if(type.multi){result.multi[type.kind].push(type);result.multi["fallback"].push(type)}else{result[type.kind][type.tag]=result["fallback"][type.tag]=type}}for(index=0,length=arguments.length;index<length;index+=1){arguments[index].forEach(collectType)}return result}function Schema(definition){return this.extend(definition)}Schema.prototype.extend=function extend(definition){var implicit=[];var explicit=[];if(definition instanceof Type){explicit.push(definition)}else if(Array.isArray(definition)){explicit=explicit.concat(definition)}else if(definition&&(Array.isArray(definition.implicit)||Array.isArray(definition.explicit))){if(definition.implicit)implicit=implicit.concat(definition.implicit);if(definition.explicit)explicit=explicit.concat(definition.explicit)}else{throw new YAMLException("Schema.extend argument should be a Type, [ Type ], "+"or a schema definition ({ implicit: [...], explicit: [...] })")}implicit.forEach((function(type){if(!(type instanceof Type)){throw new YAMLException("Specified list of YAML types (or a single Type object) contains a non-Type object.")}if(type.loadKind&&type.loadKind!=="scalar"){throw new YAMLException("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}if(type.multi){throw new YAMLException("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}}));explicit.forEach((function(type){if(!(type instanceof Type)){throw new YAMLException("Specified list of YAML types (or a single Type object) contains a non-Type object.")}}));var result=Object.create(Schema.prototype);result.implicit=(this.implicit||[]).concat(implicit);result.explicit=(this.explicit||[]).concat(explicit);result.compiledImplicit=compileList(result,"implicit");result.compiledExplicit=compileList(result,"explicit");result.compiledTypeMap=compileMap(result.compiledImplicit,result.compiledExplicit);return result};module.exports=Schema},{"./exception":4,"./type":12}],7:[function(require,module,exports){"use strict";module.exports=require("./json")},{"./json":10}],8:[function(require,module,exports){"use strict";module.exports=require("./core").extend({implicit:[require("../type/timestamp"),require("../type/merge")],explicit:[require("../type/binary"),require("../type/omap"),require("../type/pairs"),require("../type/set")]})},{"../type/binary":13,"../type/merge":18,"../type/omap":20,"../type/pairs":21,"../type/set":23,"../type/timestamp":25,"./core":7}],9:[function(require,module,exports){"use strict";var Schema=require("../schema");module.exports=new Schema({explicit:[require("../type/str"),require("../type/seq"),require("../type/map")]})},{"../schema":6,"../type/map":17,"../type/seq":22,"../type/str":24}],10:[function(require,module,exports){"use strict";module.exports=require("./failsafe").extend({implicit:[require("../type/null"),require("../type/bool"),require("../type/int"),require("../type/float")]})},{"../type/bool":14,"../type/float":15,"../type/int":16,"../type/null":19,"./failsafe":9}],11:[function(require,module,exports){"use strict";var common=require("./common");function getLine(buffer,lineStart,lineEnd,position,maxLineLength){var head="";var tail="";var maxHalfLength=Math.floor(maxLineLength/2)-1;if(position-lineStart>maxHalfLength){head=" ... ";lineStart=position-maxHalfLength+head.length}if(lineEnd-position>maxHalfLength){tail=" ...";lineEnd=position+maxHalfLength-tail.length}return{str:head+buffer.slice(lineStart,lineEnd).replace(/\t/g,"→")+tail,pos:position-lineStart+head.length}}function padStart(string,max){return common.repeat(" ",max-string.length)+string}function makeSnippet(mark,options){options=Object.create(options||null);if(!mark.buffer)return null;if(!options.maxLength)options.maxLength=79;if(typeof options.indent!=="number")options.indent=1;if(typeof options.linesBefore!=="number")options.linesBefore=3;if(typeof options.linesAfter!=="number")options.linesAfter=2;var re=/\r?\n|\r|\0/g;var lineStarts=[0];var lineEnds=[];var match;var foundLineNo=-1;while(match=re.exec(mark.buffer)){lineEnds.push(match.index);lineStarts.push(match.index+match[0].length);if(mark.position<=match.index&&foundLineNo<0){foundLineNo=lineStarts.length-2}}if(foundLineNo<0)foundLineNo=lineStarts.length-1;var result="",i,line;var lineNoLength=Math.min(mark.line+options.linesAfter,lineEnds.length).toString().length;var maxLineLength=options.maxLength-(options.indent+lineNoLength+3);for(i=1;i<=options.linesBefore;i++){if(foundLineNo-i<0)break;line=getLine(mark.buffer,lineStarts[foundLineNo-i],lineEnds[foundLineNo-i],mark.position-(lineStarts[foundLineNo]-lineStarts[foundLineNo-i]),maxLineLength);result=common.repeat(" ",options.indent)+padStart((mark.line-i+1).toString(),lineNoLength)+" | "+line.str+"\n"+result}line=getLine(mark.buffer,lineStarts[foundLineNo],lineEnds[foundLineNo],mark.position,maxLineLength);result+=common.repeat(" ",options.indent)+padStart((mark.line+1).toString(),lineNoLength)+" | "+line.str+"\n";result+=common.repeat("-",options.indent+lineNoLength+3+line.pos)+"^"+"\n";for(i=1;i<=options.linesAfter;i++){if(foundLineNo+i>=lineEnds.length)break;line=getLine(mark.buffer,lineStarts[foundLineNo+i],lineEnds[foundLineNo+i],mark.position-(lineStarts[foundLineNo]-lineStarts[foundLineNo+i]),maxLineLength);result+=common.repeat(" ",options.indent)+padStart((mark.line+i+1).toString(),lineNoLength)+" | "+line.str+"\n"}return result.replace(/\n$/,"")}module.exports=makeSnippet},{"./common":2}],12:[function(require,module,exports){"use strict";var YAMLException=require("./exception");var TYPE_CONSTRUCTOR_OPTIONS=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"];var YAML_NODE_KINDS=["scalar","sequence","mapping"];function compileStyleAliases(map){var result={};if(map!==null){Object.keys(map).forEach((function(style){map[style].forEach((function(alias){result[String(alias)]=style}))}))}return result}function Type(tag,options){options=options||{};Object.keys(options).forEach((function(name){if(TYPE_CONSTRUCTOR_OPTIONS.indexOf(name)===-1){throw new YAMLException('Unknown option "'+name+'" is met in definition of "'+tag+'" YAML type.')}}));this.options=options;this.tag=tag;this.kind=options["kind"]||null;this.resolve=options["resolve"]||function(){return true};this.construct=options["construct"]||function(data){return data};this.instanceOf=options["instanceOf"]||null;this.predicate=options["predicate"]||null;this.represent=options["represent"]||null;this.representName=options["representName"]||null;this.defaultStyle=options["defaultStyle"]||null;this.multi=options["multi"]||false;this.styleAliases=compileStyleAliases(options["styleAliases"]||null);if(YAML_NODE_KINDS.indexOf(this.kind)===-1){throw new YAMLException('Unknown kind "'+this.kind+'" is specified for "'+tag+'" YAML type.')}}module.exports=Type},{"./exception":4}],13:[function(require,module,exports){"use strict";var Type=require("../type");var BASE64_MAP="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";function resolveYamlBinary(data){if(data===null)return false;var code,idx,bitlen=0,max=data.length,map=BASE64_MAP;for(idx=0;idx<max;idx++){code=map.indexOf(data.charAt(idx));if(code>64)continue;if(code<0)return false;bitlen+=6}return bitlen%8===0}function constructYamlBinary(data){var idx,tailbits,input=data.replace(/[\r\n=]/g,""),max=input.length,map=BASE64_MAP,bits=0,result=[];for(idx=0;idx<max;idx++){if(idx%4===0&&idx){result.push(bits>>16&255);result.push(bits>>8&255);result.push(bits&255)}bits=bits<<6|map.indexOf(input.charAt(idx))}tailbits=max%4*6;if(tailbits===0){result.push(bits>>16&255);result.push(bits>>8&255);result.push(bits&255)}else if(tailbits===18){result.push(bits>>10&255);result.push(bits>>2&255)}else if(tailbits===12){result.push(bits>>4&255)}return new Uint8Array(result)}function representYamlBinary(object){var result="",bits=0,idx,tail,max=object.length,map=BASE64_MAP;for(idx=0;idx<max;idx++){if(idx%3===0&&idx){result+=map[bits>>18&63];result+=map[bits>>12&63];result+=map[bits>>6&63];result+=map[bits&63]}bits=(bits<<8)+object[idx]}tail=max%3;if(tail===0){result+=map[bits>>18&63];result+=map[bits>>12&63];result+=map[bits>>6&63];result+=map[bits&63]}else if(tail===2){result+=map[bits>>10&63];result+=map[bits>>4&63];result+=map[bits<<2&63];result+=map[64]}else if(tail===1){result+=map[bits>>2&63];result+=map[bits<<4&63];result+=map[64];result+=map[64]}return result}function isBinary(obj){return Object.prototype.toString.call(obj)==="[object Uint8Array]"}module.exports=new Type("tag:yaml.org,2002:binary",{kind:"scalar",resolve:resolveYamlBinary,construct:constructYamlBinary,predicate:isBinary,represent:representYamlBinary})},{"../type":12}],14:[function(require,module,exports){"use strict";var Type=require("../type");function resolveYamlBoolean(data){if(data===null)return false;var max=data.length;return max===4&&(data==="true"||data==="True"||data==="TRUE")||max===5&&(data==="false"||data==="False"||data==="FALSE")}function constructYamlBoolean(data){return data==="true"||data==="True"||data==="TRUE"}function isBoolean(object){return Object.prototype.toString.call(object)==="[object Boolean]"}module.exports=new Type("tag:yaml.org,2002:bool",{kind:"scalar",resolve:resolveYamlBoolean,construct:constructYamlBoolean,predicate:isBoolean,represent:{lowercase:function(object){return object?"true":"false"},uppercase:function(object){return object?"TRUE":"FALSE"},camelcase:function(object){return object?"True":"False"}},defaultStyle:"lowercase"})},{"../type":12}],15:[function(require,module,exports){"use strict";var common=require("../common");var Type=require("../type");var YAML_FLOAT_PATTERN=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?"+"|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?"+"|[-+]?\\.(?:inf|Inf|INF)"+"|\\.(?:nan|NaN|NAN))$");function resolveYamlFloat(data){if(data===null)return false;if(!YAML_FLOAT_PATTERN.test(data)||data[data.length-1]==="_"){return false}return true}function constructYamlFloat(data){var value,sign;value=data.replace(/_/g,"").toLowerCase();sign=value[0]==="-"?-1:1;if("+-".indexOf(value[0])>=0){value=value.slice(1)}if(value===".inf"){return sign===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY}else if(value===".nan"){return NaN}return sign*parseFloat(value,10)}var SCIENTIFIC_WITHOUT_DOT=/^[-+]?[0-9]+e/;function representYamlFloat(object,style){var res;if(isNaN(object)){switch(style){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}}else if(Number.POSITIVE_INFINITY===object){switch(style){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}}else if(Number.NEGATIVE_INFINITY===object){switch(style){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}}else if(common.isNegativeZero(object)){return"-0.0"}res=object.toString(10);return SCIENTIFIC_WITHOUT_DOT.test(res)?res.replace("e",".e"):res}function isFloat(object){return Object.prototype.toString.call(object)==="[object Number]"&&(object%1!==0||common.isNegativeZero(object))}module.exports=new Type("tag:yaml.org,2002:float",{kind:"scalar",resolve:resolveYamlFloat,construct:constructYamlFloat,predicate:isFloat,represent:representYamlFloat,defaultStyle:"lowercase"})},{"../common":2,"../type":12}],16:[function(require,module,exports){"use strict";var common=require("../common");var Type=require("../type");function isHexCode(c){return 48<=c&&c<=57||65<=c&&c<=70||97<=c&&c<=102}function isOctCode(c){return 48<=c&&c<=55}function isDecCode(c){return 48<=c&&c<=57}function resolveYamlInteger(data){if(data===null)return false;var max=data.length,index=0,hasDigits=false,ch;if(!max)return false;ch=data[index];if(ch==="-"||ch==="+"){ch=data[++index]}if(ch==="0"){if(index+1===max)return true;ch=data[++index];if(ch==="b"){index++;for(;index<max;index++){ch=data[index];if(ch==="_")continue;if(ch!=="0"&&ch!=="1")return false;hasDigits=true}return hasDigits&&ch!=="_"}if(ch==="x"){index++;for(;index<max;index++){ch=data[index];if(ch==="_")continue;if(!isHexCode(data.charCodeAt(index)))return false;hasDigits=true}return hasDigits&&ch!=="_"}if(ch==="o"){index++;for(;index<max;index++){ch=data[index];if(ch==="_")continue;if(!isOctCode(data.charCodeAt(index)))return false;hasDigits=true}return hasDigits&&ch!=="_"}}if(ch==="_")return false;for(;index<max;index++){ch=data[index];if(ch==="_")continue;if(!isDecCode(data.charCodeAt(index))){return false}hasDigits=true}if(!hasDigits||ch==="_")return false;return true}function constructYamlInteger(data){var value=data,sign=1,ch;if(value.indexOf("_")!==-1){value=value.replace(/_/g,"")}ch=value[0];if(ch==="-"||ch==="+"){if(ch==="-")sign=-1;value=value.slice(1);ch=value[0]}if(value==="0")return 0;if(ch==="0"){if(value[1]==="b")return sign*parseInt(value.slice(2),2);if(value[1]==="x")return sign*parseInt(value.slice(2),16);if(value[1]==="o")return sign*parseInt(value.slice(2),8)}return sign*parseInt(value,10)}function isInteger(object){return Object.prototype.toString.call(object)==="[object Number]"&&(object%1===0&&!common.isNegativeZero(object))}module.exports=new Type("tag:yaml.org,2002:int",{kind:"scalar",resolve:resolveYamlInteger,construct:constructYamlInteger,predicate:isInteger,represent:{binary:function(obj){return obj>=0?"0b"+obj.toString(2):"-0b"+obj.toString(2).slice(1)},octal:function(obj){return obj>=0?"0o"+obj.toString(8):"-0o"+obj.toString(8).slice(1)},decimal:function(obj){return obj.toString(10)},hexadecimal:function(obj){return obj>=0?"0x"+obj.toString(16).toUpperCase():"-0x"+obj.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})},{"../common":2,"../type":12}],17:[function(require,module,exports){"use strict";var Type=require("../type");module.exports=new Type("tag:yaml.org,2002:map",{kind:"mapping",construct:function(data){return data!==null?data:{}}})},{"../type":12}],18:[function(require,module,exports){"use strict";var Type=require("../type");function resolveYamlMerge(data){return data==="<<"||data===null}module.exports=new Type("tag:yaml.org,2002:merge",{kind:"scalar",resolve:resolveYamlMerge})},{"../type":12}],19:[function(require,module,exports){"use strict";var Type=require("../type");function resolveYamlNull(data){if(data===null)return true;var max=data.length;return max===1&&data==="~"||max===4&&(data==="null"||data==="Null"||data==="NULL")}function constructYamlNull(){return null}function isNull(object){return object===null}module.exports=new Type("tag:yaml.org,2002:null",{kind:"scalar",resolve:resolveYamlNull,construct:constructYamlNull,predicate:isNull,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"})},{"../type":12}],20:[function(require,module,exports){"use strict";var Type=require("../type");var _hasOwnProperty=Object.prototype.hasOwnProperty;var _toString=Object.prototype.toString;function resolveYamlOmap(data){if(data===null)return true;var objectKeys=[],index,length,pair,pairKey,pairHasKey,object=data;for(index=0,length=object.length;index<length;index+=1){pair=object[index];pairHasKey=false;if(_toString.call(pair)!=="[object Object]")return false;for(pairKey in pair){if(_hasOwnProperty.call(pair,pairKey)){if(!pairHasKey)pairHasKey=true;else return false}}if(!pairHasKey)return false;if(objectKeys.indexOf(pairKey)===-1)objectKeys.push(pairKey);else return false}return true}function constructYamlOmap(data){return data!==null?data:[]}module.exports=new Type("tag:yaml.org,2002:omap",{kind:"sequence",resolve:resolveYamlOmap,construct:constructYamlOmap})},{"../type":12}],21:[function(require,module,exports){"use strict";var Type=require("../type");var _toString=Object.prototype.toString;function resolveYamlPairs(data){if(data===null)return true;var index,length,pair,keys,result,object=data;result=new Array(object.length);for(index=0,length=object.length;index<length;index+=1){pair=object[index];if(_toString.call(pair)!=="[object Object]")return false;keys=Object.keys(pair);if(keys.length!==1)return false;result[index]=[keys[0],pair[keys[0]]]}return true}function constructYamlPairs(data){if(data===null)return[];var index,length,pair,keys,result,object=data;result=new Array(object.length);for(index=0,length=object.length;index<length;index+=1){pair=object[index];keys=Object.keys(pair);result[index]=[keys[0],pair[keys[0]]]}return result}module.exports=new Type("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:resolveYamlPairs,construct:constructYamlPairs})},{"../type":12}],22:[function(require,module,exports){"use strict";var Type=require("../type");module.exports=new Type("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(data){return data!==null?data:[]}})},{"../type":12}],23:[function(require,module,exports){"use strict";var Type=require("../type");var _hasOwnProperty=Object.prototype.hasOwnProperty;function resolveYamlSet(data){if(data===null)return true;var key,object=data;for(key in object){if(_hasOwnProperty.call(object,key)){if(object[key]!==null)return false}}return true}function constructYamlSet(data){return data!==null?data:{}}module.exports=new Type("tag:yaml.org,2002:set",{kind:"mapping",resolve:resolveYamlSet,construct:constructYamlSet})},{"../type":12}],24:[function(require,module,exports){"use strict";var Type=require("../type");module.exports=new Type("tag:yaml.org,2002:str",{kind:"scalar",construct:function(data){return data!==null?data:""}})},{"../type":12}],25:[function(require,module,exports){"use strict";var Type=require("../type");var YAML_DATE_REGEXP=new RegExp("^([0-9][0-9][0-9][0-9])"+"-([0-9][0-9])"+"-([0-9][0-9])$");var YAML_TIMESTAMP_REGEXP=new RegExp("^([0-9][0-9][0-9][0-9])"+"-([0-9][0-9]?)"+"-([0-9][0-9]?)"+"(?:[Tt]|[ \\t]+)"+"([0-9][0-9]?)"+":([0-9][0-9])"+":([0-9][0-9])"+"(?:\\.([0-9]*))?"+"(?:[ \\t]*(Z|([-+])([0-9][0-9]?)"+"(?::([0-9][0-9]))?))?$");function resolveYamlTimestamp(data){if(data===null)return false;if(YAML_DATE_REGEXP.exec(data)!==null)return true;if(YAML_TIMESTAMP_REGEXP.exec(data)!==null)return true;return false}function constructYamlTimestamp(data){var match,year,month,day,hour,minute,second,fraction=0,delta=null,tz_hour,tz_minute,date;match=YAML_DATE_REGEXP.exec(data);if(match===null)match=YAML_TIMESTAMP_REGEXP.exec(data);if(match===null)throw new Error("Date resolve error");year=+match[1];month=+match[2]-1;day=+match[3];if(!match[4]){return new Date(Date.UTC(year,month,day))}hour=+match[4];minute=+match[5];second=+match[6];if(match[7]){fraction=match[7].slice(0,3);while(fraction.length<3){fraction+="0"}fraction=+fraction}if(match[9]){tz_hour=+match[10];tz_minute=+(match[11]||0);delta=(tz_hour*60+tz_minute)*6e4;if(match[9]==="-")delta=-delta}date=new Date(Date.UTC(year,month,day,hour,minute,second,fraction));if(delta)date.setTime(date.getTime()-delta);return date}function representYamlTimestamp(object){return object.toISOString()}module.exports=new Type("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:resolveYamlTimestamp,construct:constructYamlTimestamp,instanceOf:Date,represent:representYamlTimestamp})},{"../type":12}]},{},[1])(1)}));