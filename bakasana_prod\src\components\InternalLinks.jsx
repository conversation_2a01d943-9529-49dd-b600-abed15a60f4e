import React from 'react';
import Link from 'next/link';

export default function InternalLinks({ currentPage, className = '' }) {
  const linkSuggestions = {
    home: [
      { href: '/retreaty', label: 'Retreaty Jogi', description: 'Bali & Sri Lanka' },
      { href: '/o-mnie', label: 'O mnie', description: '<PERSON>' },
      { href: '/kontakt', label: 'Konta<PERSON>', description: 'Skontaktuj się' }
    ],
    retreaty: [
      { href: '/rezerwacja', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>', description: '<PERSON><PERSON><PERSON><PERSON>j miej<PERSON>' },
      { href: '/o-mnie', label: 'O instruktorce', description: '<PERSON>' },
      { href: '/kontakt', label: 'Kontakt', description: 'Zadaj pytanie' }
    ],
    default: [
      { href: '/', label: 'Strona główna', description: 'Powrót do home' },
      { href: '/retreaty', label: 'Retreaty', description: '<PERSON><PERSON><PERSON><PERSON> of<PERSON>ę' },
      { href: '/kontakt', label: '<PERSON>nta<PERSON>', description: 'Skontaktuj się' }
    ]
  };

  const links = linkSuggestions[currentPage] || linkSuggestions.default;

  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4">
        <h2 className="text-2xl font-bold text-center mb-8">Przydatne linki</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          {links.map((link, index) => (
            <Link 
              key={index} 
              href={link.href}
              className="block p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
            >
              <h3 className="font-semibold text-lg mb-2">{link.label}</h3>
              <p className="text-gray-600">{link.description}</p>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

export const RelatedBlogPosts = ({ currentPost, allPosts }) => {
  return null;
};
