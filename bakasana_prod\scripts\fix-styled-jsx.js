#!/usr/bin/env node

/**
 * Styled-JSX Fix Script - Bakasana Studio
 * 
 * This script fixes styled-jsx compatibility issues by converting
 * styled-jsx usage to dangerouslySetInnerHTML for Next.js 15 compatibility
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

const log = (color, message) => console.log(`${colors[color]}${message}${colors.reset}`);

class StyledJSXFixer {
  constructor() {
    this.files = [];
    this.fixedFiles = [];
    this.skippedFiles = [];
    this.stats = {
      filesScanned: 0,
      filesWithStyledJSX: 0,
      filesFixed: 0,
      issuesFound: 0,
      issuesFixed: 0
    };
  }

  // Find all JSX/TSX files recursively
  findFiles(dir = path.join(process.cwd(), 'src')) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.findFiles(fullPath);
      } else if (stat.isFile() && /\.(jsx|tsx)$/.test(item)) {
        this.files.push(fullPath);
      }
    });
    
    this.stats.filesScanned = this.files.length;
  }

  // Check if file has styled-jsx usage
  hasStyledJSX(content) {
    return content.includes('<style jsx') || content.includes('<style jsx>');
  }

  // Fix styled-jsx in file content
  fixStyledJSXInContent(content) {
    let fixedContent = content;
    let fixCount = 0;

    // Pattern 1: <style jsx>{`...`}</style>
    const pattern1 = /<style jsx>\{`([\s\S]*?)`\}<\/style>/g;
    fixedContent = fixedContent.replace(pattern1, (match, cssContent) => {
      fixCount++;
      return `<style dangerouslySetInnerHTML={{
        __html: \`${cssContent.trim()}\`
      }} />`;
    });

    // Pattern 2: <style jsx global>{`...`}</style>
    const pattern2 = /<style jsx global>\{`([\s\S]*?)`\}<\/style>/g;
    fixedContent = fixedContent.replace(pattern2, (match, cssContent) => {
      fixCount++;
      return `<style dangerouslySetInnerHTML={{
        __html: \`${cssContent.trim()}\`
      }} />`;
    });

    // Pattern 3: <style jsx={true}>{`...`}</style>
    const pattern3 = /<style jsx=\{true\}>\{`([\s\S]*?)`\}<\/style>/g;
    fixedContent = fixedContent.replace(pattern3, (match, cssContent) => {
      fixCount++;
      return `<style dangerouslySetInnerHTML={{
        __html: \`${cssContent.trim()}\`
      }} />`;
    });

    return { content: fixedContent, fixCount };
  }

  // Process a single file
  processFile(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    
    if (!fs.existsSync(filePath)) {
      log('red', `❌ File not found: ${relativePath}`);
      return;
    }

    const originalContent = fs.readFileSync(filePath, 'utf8');
    
    if (!this.hasStyledJSX(originalContent)) {
      return; // Skip files without styled-jsx
    }

    this.stats.filesWithStyledJSX++;
    log('yellow', `🔍 Processing: ${relativePath}`);

    const { content: fixedContent, fixCount } = this.fixStyledJSXInContent(originalContent);

    if (fixCount > 0) {
      // Create backup
      const backupPath = `${filePath}.backup`;
      if (!fs.existsSync(backupPath)) {
        fs.writeFileSync(backupPath, originalContent);
      }

      // Write fixed content
      fs.writeFileSync(filePath, fixedContent);
      
      this.fixedFiles.push({
        path: relativePath,
        fixes: fixCount
      });
      
      this.stats.filesFixed++;
      this.stats.issuesFixed += fixCount;
      
      log('green', `✅ Fixed ${fixCount} styled-jsx issues in: ${relativePath}`);
    } else {
      this.skippedFiles.push(relativePath);
      log('yellow', `⚠️  No fixes needed for: ${relativePath}`);
    }
  }

  // Generate comprehensive report
  generateReport() {
    log('cyan', '\n' + '='.repeat(60));
    log('cyan', '🎨 STYLED-JSX FIXER REPORT - BAKASANA STUDIO');
    log('cyan', '='.repeat(60));

    // Summary stats
    log('magenta', '\n📊 Processing Summary:');
    log('white', `Files scanned: ${this.stats.filesScanned}`);
    log('white', `Files with styled-jsx: ${this.stats.filesWithStyledJSX}`);
    log(this.stats.filesFixed > 0 ? 'green' : 'yellow', 
        `Files fixed: ${this.stats.filesFixed}`);
    log('green', `Issues fixed: ${this.stats.issuesFixed}`);

    // Show fixed files
    if (this.fixedFiles.length > 0) {
      log('green', '\n✅ Successfully Fixed Files:');
      log('white', '-'.repeat(40));
      this.fixedFiles.forEach(file => {
        log('green', `• ${file.path} (${file.fixes} fixes)`);
      });
    }

    // Show skipped files
    if (this.skippedFiles.length > 0) {
      log('yellow', '\n⚠️  Skipped Files:');
      log('white', '-'.repeat(30));
      this.skippedFiles.forEach(file => {
        log('yellow', `• ${file}`);
      });
    }

    // Show what was changed
    this.showChangesExplanation();

    // Show next steps
    this.showNextSteps();

    log('cyan', '\n' + '='.repeat(60));
    log(this.stats.filesFixed > 0 ? 'green' : 'blue', 
        `Status: ${this.stats.filesFixed > 0 ? '✅ FIXES APPLIED' : '📋 NO CHANGES NEEDED'}`);
    log('cyan', '='.repeat(60) + '\n');
  }

  showChangesExplanation() {
    log('magenta', '\n🔧 Changes Applied:');
    log('white', '• Converted <style jsx>{`...`}</style> to dangerouslySetInnerHTML');
    log('white', '• Converted <style jsx global>{`...`}</style> to dangerouslySetInnerHTML');
    log('white', '• Maintained all CSS functionality and styling');
    log('white', '• Created .backup files for safety');
    log('white', '• Fixed Next.js 15 compatibility issues');
  }

  showNextSteps() {
    log('magenta', '\n🚀 Next Steps:');
    log('white', '1. Test the application: npm run dev');
    log('white', '2. Verify all styles are working correctly');
    log('white', '3. Run build: npm run build');
    log('white', '4. If everything works, delete .backup files');
    log('white', '5. Commit the changes to version control');
  }

  // Restore from backups if needed
  restoreBackups() {
    log('cyan', '\n🔄 Restoring from backups...');
    
    let restoredCount = 0;
    
    this.files.forEach(filePath => {
      const backupPath = `${filePath}.backup`;
      if (fs.existsSync(backupPath)) {
        const backupContent = fs.readFileSync(backupPath, 'utf8');
        fs.writeFileSync(filePath, backupContent);
        fs.unlinkSync(backupPath);
        restoredCount++;
        
        const relativePath = path.relative(process.cwd(), filePath);
        log('green', `✅ Restored: ${relativePath}`);
      }
    });
    
    log('cyan', `\n📋 Restored ${restoredCount} files from backups`);
  }

  // Clean up backup files
  cleanBackups() {
    log('cyan', '\n🧹 Cleaning backup files...');
    
    let cleanedCount = 0;
    
    this.files.forEach(filePath => {
      const backupPath = `${filePath}.backup`;
      if (fs.existsSync(backupPath)) {
        fs.unlinkSync(backupPath);
        cleanedCount++;
        
        const relativePath = path.relative(process.cwd(), filePath);
        log('green', `🗑️  Cleaned: ${relativePath}.backup`);
      }
    });
    
    log('cyan', `\n📋 Cleaned ${cleanedCount} backup files`);
  }

  run(command = 'fix') {
    this.findFiles();
    
    if (command === 'restore') {
      this.restoreBackups();
      return true;
    }
    
    if (command === 'clean') {
      this.cleanBackups();
      return true;
    }
    
    log('cyan', '\n🔍 Scanning for styled-jsx usage...');
    
    this.files.forEach(filePath => {
      this.processFile(filePath);
    });
    
    this.generateReport();
    
    return this.stats.filesFixed >= 0; // Success if no errors
  }
}

// Command line interface
if (require.main === module) {
  const command = process.argv[2] || 'fix';
  
  if (!['fix', 'restore', 'clean'].includes(command)) {
    log('red', '❌ Invalid command. Use: fix, restore, or clean');
    process.exit(1);
  }
  
  const fixer = new StyledJSXFixer();
  const success = fixer.run(command);
  
  process.exit(success ? 0 : 1);
}

module.exports = StyledJSXFixer;