import { blockOffsetToSpanSelectionPoint, getBlockKeyFromSelectionPoint, getChildKeyFromSelectionPoint, isTextBlock, isSpan } from "./util.slice-blocks.js";
function blockOffsetToBlockSelectionPoint({
  context,
  blockOffset
}) {
  let selectionPoint;
  for (const block of context.value)
    if (block._key === blockOffset.path[0]._key) {
      selectionPoint = {
        path: [{
          _key: block._key
        }],
        offset: blockOffset.offset
      };
      break;
    }
  return selectionPoint;
}
function blockOffsetToSelectionPoint({
  context,
  blockOffset,
  direction
}) {
  return blockOffsetToSpanSelectionPoint({
    context,
    blockOffset,
    direction
  }) || blockOffsetToBlockSelectionPoint({
    context,
    blockOffset
  });
}
function blockOffsetsToSelection({
  context,
  offsets,
  backward
}) {
  const anchor = blockOffsetToSelectionPoint({
    context,
    blockOffset: offsets.anchor,
    direction: backward ? "backward" : "forward"
  }), focus = blockOffsetToSelectionPoint({
    context,
    blockOffset: offsets.focus,
    direction: backward ? "forward" : "backward"
  });
  return !anchor || !focus ? null : {
    anchor,
    focus,
    backward
  };
}
function childSelectionPointToBlockOffset({
  context,
  selectionPoint
}) {
  let offset = 0;
  const blockKey = getBlockKeyFromSelectionPoint(selectionPoint), childKey = getChildKeyFromSelectionPoint(selectionPoint);
  if (!(!blockKey || !childKey)) {
    for (const block of context.value)
      if (block._key === blockKey && isTextBlock(context, block))
        for (const child of block.children) {
          if (child._key === childKey)
            return {
              path: [{
                _key: block._key
              }],
              offset: offset + selectionPoint.offset
            };
          isSpan(context, child) && (offset += child.text.length);
        }
  }
}
export {
  blockOffsetToBlockSelectionPoint,
  blockOffsetToSelectionPoint,
  blockOffsetsToSelection,
  childSelectionPointToBlockOffset
};
//# sourceMappingURL=util.child-selection-point-to-block-offset.js.map
