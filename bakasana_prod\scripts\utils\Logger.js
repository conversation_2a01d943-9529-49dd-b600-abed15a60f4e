/**
 * Shared Logger Utility
 * Consistent logging across all build scripts
 */

const fs = require('fs');
const path = require('path');

class Logger {
  constructor(scriptName = 'Script', options = {}) {
    this.scriptName = scriptName;
    this.options = {
      logToFile: false,
      logLevel: 'info',
      colors: true,
      timestamp: true,
      ...options,
    };

    this.logFile =
      options.logFile ||
      path.join(process.cwd(), 'logs', `${scriptName.toLowerCase()}.log`);
    this.colors = {
      reset: '\x1b[0m',
      bright: '\x1b[1m',
      dim: '\x1b[2m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      magenta: '\x1b[35m',
      cyan: '\x1b[36m',
      white: '\x1b[37m',
    };

    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
    };

    this.currentLevel = this.levels[this.options.logLevel] || 2;

    // Ensure log directory exists if logging to file
    if (this.options.logToFile) {
      this.ensureLogDirectory();
    }
  }

  /**
   * Ensure log directory exists
   */
  ensureLogDirectory() {
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  /**
   * Format timestamp
   */
  getTimestamp() {
    return new Date().toISOString();
  }

  /**
   * Colorize text for console output
   */
  colorize(text, color) {
    if (!this.options.colors) return text;
    return `${this.colors[color] || ''}${text}${this.colors.reset}`;
  }

  /**
   * Format log message
   */
  formatMessage(level, message, data = null) {
    const timestamp = this.options.timestamp ? `[${this.getTimestamp()}] ` : '';
    const scriptTag = `[${this.scriptName}] `;
    const levelTag = `[${level.toUpperCase()}] `;

    let formattedMessage = `${timestamp}${scriptTag}${levelTag}${message}`;

    if (data) {
      formattedMessage += `\n${JSON.stringify(data, null, 2)}`;
    }

    return formattedMessage;
  }

  /**
   * Write to log file
   */
  writeToFile(message) {
    if (!this.options.logToFile) return;

    try {
      fs.appendFileSync(this.logFile, message + '\n', 'utf8');
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  /**
   * Log with specific level
   */
  log(level, message, data = null) {
    const levelNum = this.levels[level];
    if (levelNum === undefined || levelNum > this.currentLevel) {
      return;
    }

    const formattedMessage = this.formatMessage(level, message, data);

    // Write to file (without colors)
    this.writeToFile(formattedMessage);

    // Console output with colors
    let coloredMessage = formattedMessage;
    switch (level) {
      case 'error':
        coloredMessage = this.colorize(formattedMessage, 'red');
        break;
      case 'warn':
        coloredMessage = this.colorize(formattedMessage, 'yellow');
        break;
      case 'info':
        coloredMessage = this.colorize(formattedMessage, 'cyan');
        break;
      case 'debug':
        coloredMessage = this.colorize(formattedMessage, 'dim');
        break;
    }

    console.log(coloredMessage);
  }

  /**
   * Convenience methods
   */
  error(message, data = null) {
    this.log('error', message, data);
  }

  warn(message, data = null) {
    this.log('warn', message, data);
  }

  info(message, data = null) {
    this.log('info', message, data);
  }

  debug(message, data = null) {
    this.log('debug', message, data);
  }

  /**
   * Success message (info level with green color)
   */
  success(message, data = null) {
    const formattedMessage = this.formatMessage('info', message, data);
    this.writeToFile(formattedMessage);
    console.log(this.colorize(formattedMessage, 'green'));
  }

  /**
   * Progress indicator
   */
  progress(current, total, item = '') {
    const percentage = Math.round((current / total) * 100);
    const progressBar =
      '█'.repeat(Math.floor(percentage / 5)) +
      '░'.repeat(20 - Math.floor(percentage / 5));
    const message = `Progress: [${progressBar}] ${percentage}% (${current}/${total}) ${item}`;

    // Use carriage return to overwrite previous line
    process.stdout.write('\r' + this.colorize(message, 'cyan'));

    if (current === total) {
      process.stdout.write('\n');
    }
  }

  /**
   * Section header
   */
  section(title) {
    const border = '='.repeat(60);
    const centeredTitle = title.padStart((60 + title.length) / 2).padEnd(60);

    console.log(this.colorize(border, 'bright'));
    console.log(this.colorize(centeredTitle, 'bright'));
    console.log(this.colorize(border, 'bright'));
  }

  /**
   * Subsection header
   */
  subsection(title) {
    const border = '-'.repeat(40);
    console.log(this.colorize(`\n${border}`, 'dim'));
    console.log(this.colorize(title, 'bright'));
    console.log(this.colorize(border, 'dim'));
  }

  /**
   * Summary table
   */
  table(data, headers = null) {
    if (!Array.isArray(data) || data.length === 0) {
      this.warn('No data to display in table');
      return;
    }

    const keys = headers || Object.keys(data[0]);
    const columnWidths = keys.map(key =>
      Math.max(key.length, ...data.map(row => String(row[key] || '').length))
    );

    // Header
    const headerRow = keys
      .map((key, i) => key.padEnd(columnWidths[i]))
      .join(' | ');
    console.log(this.colorize(headerRow, 'bright'));
    console.log(this.colorize('-'.repeat(headerRow.length), 'dim'));

    // Rows
    data.forEach(row => {
      const dataRow = keys
        .map((key, i) => String(row[key] || '').padEnd(columnWidths[i]))
        .join(' | ');
      console.log(dataRow);
    });
  }
}

module.exports = Logger;
