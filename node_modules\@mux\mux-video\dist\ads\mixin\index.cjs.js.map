{"version": 3, "sources": ["../../../src/ads/mixin/index.ts", "../../../src/ads/mixin/events.ts", "../../../src/ads/mixin/google-ima-client-ad.ts", "../../../src/ads/mixin/google-ima-client-provider.ts"], "sourcesContent": ["import { GoogleImaClientProvider } from './google-ima-client-provider';\nimport type { CustomVideoElement } from 'custom-media-element';\nimport { Events as AdEvents, AdEvent } from './events.js';\nimport { Constructor, IAdsVideo } from './types.js';\n\nexport * from './events.js';\nexport * from './types.js';\n\nexport const Attributes = {\n  AD_TAG_URL: 'ad-tag-url',\n  ALLOW_AD_BLOCKER: 'allow-ad-blocker',\n} as const;\n\ntype VideoBackup = {\n  currentTime: number;\n};\n\nexport function AdsVideoMixin<T extends CustomVideoElement>(superclass: T): Constructor<IAdsVideo> & T {\n  class AdsVideo extends superclass implements IAdsVideo {\n    static get observedAttributes() {\n      return [...super.observedAttributes, 'src', Attributes.AD_TAG_URL];\n    }\n\n    static get Events() {\n      // Filter out any duplicate events with a Set.\n      return [...new Set([...(super.Events ?? []), ...Object.values(AdEvents)])];\n    }\n\n    static getTemplateHTML = (attrs: Record<string, string>) => {\n      return (\n        superclass.getTemplateHTML(attrs) +\n        /*html*/ `\n          <style>\n            :host {\n              position: relative;\n            }\n\n            #ad-container {\n              position: absolute;\n              top: 0px;\n              left: 0px;\n              bottom: 0px;\n              right: 0px;\n              z-index: -1;\n              width: 100%;\n              height: 100%;\n            }\n\n            #ad-container.ad-break {\n              z-index: 0;\n            }\n\n            #ima-unavailable-message {\n              position: absolute;\n              inset: 0;\n              z-index: 10;\n              background: rgba(0, 0, 0, 0.75);\n              color: white;\n              font-size: 0.9em;\n              text-align: center;\n              line-height: 1.4;\n              align-items: center;\n              align-content: center;\n              cursor: not-allowed;\n            }\n\n            #ima-unavailable-message h4 {\n              font-size: 1rem;\n              margin: 0;\n            }\n          </style>\n          <div id=\"ad-container\"></div>\n        `\n      );\n    };\n\n    #videoMetadataLoaded = false;\n    #oldAdTagUrl?: string | null;\n    #adProvider?: GoogleImaClientProvider;\n    #videoBackup?: VideoBackup;\n\n    connectedCallback() {\n      super.connectedCallback();\n\n      if (!GoogleImaClientProvider.isSDKAvailable()) {\n        console.error('Missing google.ima SDK. Make sure you include it via a script tag.');\n\n        if (!this.allowAdBlocker) {\n          this.#showAdBlockedMessage();\n        }\n        return;\n      }\n\n      if (!this.#adProvider) {\n        this.#adProvider = new GoogleImaClientProvider({\n          adContainer: this.#adContainer,\n          videoElement: this.nativeEl,\n          originalSize: this.getBoundingClientRect(),\n        });\n\n        for (const event of Object.values(AdEvents)) {\n          this.#adProvider.addEventListener(event, this);\n        }\n      }\n    }\n\n    attributeChangedCallback(attrName: string, oldValue?: string | null, newValue?: string | null): void {\n      super.attributeChangedCallback(attrName, oldValue, newValue);\n\n      if (attrName === 'src' && newValue !== oldValue) {\n        // If subsequent videos are loaded, reset the old ad tag url\n        // to allow the same ads to be requested for a new video.\n        // Don't use events to reset the state as they could be triggered\n        // if Google IMA reuses the same video element for ads.\n        this.#oldAdTagUrl = undefined;\n        this.#videoBackup = undefined;\n        this.#videoMetadataLoaded = false;\n      }\n\n      if (attrName === Attributes.AD_TAG_URL) {\n        this.#resetAds();\n      }\n    }\n\n    /**\n     * See https://github.com/muxinc/media-elements/blob/main/packages/custom-media-element/custom-media-element.ts#L345-L359\n     * In custom-media-element this method forwards events from the native video element to the custom element.\n     */\n    handleEvent(event: Event | AdEvent): void {\n      if (event instanceof AdEvent) {\n        this.#handleAdEvent(event);\n        return;\n      }\n\n      // If we are in an ad-break block the events from the native video element.\n      // This can happen when Google IMA reuses the same video element for ads.\n      if (this.#adProvider?.adBreak) {\n        return;\n      }\n\n      if (event.type === 'loadedmetadata') {\n        this.#onLoadedMetadata();\n      } else if (event.type === 'play') {\n        this.#onPlay();\n      }\n\n      super.handleEvent(event);\n    }\n\n    #onLoadedMetadata() {\n      this.#videoMetadataLoaded = true;\n      // When a new video is loaded, make sure we reset the ads.\n      this.#resetAds();\n    }\n\n    #onPlay() {\n      // Make sure the ads are reset before playing if needed.\n      this.#resetAds();\n      this.#adProvider?.initializeAdDisplayContainer();\n    }\n\n    #resetAds() {\n      if (this.adTagUrl) {\n        this.#requestAds();\n      } else {\n        this.#destroyAds();\n      }\n    }\n\n    async #requestAds() {\n      // The container element must be in the DOM to initialize the ad display container.\n      if (!this.adTagUrl || !this.isConnected) return;\n\n      // Wait until the video metadata has loaded before requesting ads to avoid unnecessary requests.\n      if (!this.#videoMetadataLoaded) return;\n\n      if (this.adTagUrl !== this.#oldAdTagUrl) {\n        this.#oldAdTagUrl = this.adTagUrl;\n        this.#adProvider?.requestAds(this.adTagUrl);\n      }\n    }\n\n    #destroyAds() {\n      this.#adProvider?.unload();\n      this.#oldAdTagUrl = undefined;\n    }\n\n    get #adContainer() {\n      return this.shadowRoot?.getElementById('ad-container') as HTMLElement;\n    }\n\n    #showAdBlockedMessage() {\n      if (this.shadowRoot?.querySelector('#ima-unavailable-message')) {\n        return;\n      }\n\n      this.#adContainer?.insertAdjacentHTML(\n        'afterend',\n        /* html */ `\n          <div id=\"ima-unavailable-message\">\n            <h4>Ad experience unavailable.</h4>\n            <span>This may be due to a missing SDK, network issue, or ad blocker.</span>\n          </div>\n        `\n      );\n    }\n\n    #handleAdEvent(event: Event | AdEvent) {\n      if (event.type === AdEvents.AD_BREAK_START) {\n        this.#onAdBreakStart();\n        this.#dispatchAdEvent(AdEvents.DURATION_CHANGE);\n        this.#dispatchAdEvent(event.type);\n        return;\n      }\n\n      if (event.type === AdEvents.AD_BREAK_END) {\n        this.#onAdBreakEnd();\n        this.#dispatchAdEvent(AdEvents.DURATION_CHANGE);\n        this.#dispatchAdEvent(event.type);\n        return;\n      }\n\n      this.#dispatchAdEvent(event.type);\n    }\n\n    #dispatchAdEvent(eventType: string) {\n      // Composed events are forwarded to parent shadow hosts (e.g. mux-player).\n      this.dispatchEvent(new AdEvent(eventType, { composed: true }));\n    }\n\n    #onAdBreakStart() {\n      this.#adContainer?.classList.toggle('ad-break', true);\n\n      if (!this.ad?.isLinear()) {\n        return;\n      }\n\n      super.pause();\n\n      this.#videoBackup = {\n        currentTime: super.currentTime,\n      };\n    }\n\n    #onAdBreakEnd() {\n      this.#adContainer?.classList.toggle('ad-break', false);\n\n      if (this.#videoBackup?.currentTime) {\n        this.currentTime = this.#videoBackup.currentTime;\n      }\n\n      this.#videoBackup = undefined;\n\n      setTimeout(() => {\n        if (!super.ended) {\n          try {\n            this.play();\n          } catch {\n            // Ignore abort errors\n          }\n        }\n      }, 100);\n    }\n\n    play() {\n      if (!GoogleImaClientProvider.isSDKAvailable() && !this.allowAdBlocker) {\n        return Promise.reject(new Error('Playback failed: Ad experience not available'));\n      }\n      if (this.#adProvider?.adBreak) {\n        return this.#adProvider.play();\n      }\n      return super.play();\n    }\n\n    pause() {\n      if (this.#adProvider?.adBreak) {\n        this.#adProvider?.pause();\n      }\n      super.pause();\n    }\n\n    get ad() {\n      return this.#adProvider?.ad;\n    }\n\n    get adsLoader() {\n      if (!this.#adProvider) {\n        console.warn('adsLoader not available yet');\n      }\n      return this.#adProvider?.adsLoader;\n    }\n\n    get adTagUrl() {\n      return this.getAttribute(Attributes.AD_TAG_URL) ?? undefined;\n    }\n\n    set adTagUrl(value) {\n      if (value == this.adTagUrl) return;\n\n      if (value == null) {\n        this.removeAttribute(Attributes.AD_TAG_URL);\n      } else {\n        this.setAttribute(Attributes.AD_TAG_URL, value);\n      }\n    }\n\n    get allowAdBlocker() {\n      return this.hasAttribute(Attributes.ALLOW_AD_BLOCKER);\n    }\n\n    set allowAdBlocker(val) {\n      this.toggleAttribute(Attributes.ALLOW_AD_BLOCKER, Boolean(val));\n    }\n\n    get paused() {\n      if (this.#adProvider?.adBreak) {\n        return this.#adProvider?.paused ?? false;\n      }\n      return super.paused;\n    }\n\n    get duration() {\n      if (this.#adProvider?.adBreak) {\n        return this.#adProvider?.duration ?? 0;\n      }\n      return super.duration;\n    }\n\n    get currentTime() {\n      if (this.#adProvider?.adBreak) {\n        return this.#adProvider?.currentTime ?? 0;\n      }\n      return super.currentTime;\n    }\n\n    set currentTime(val) {\n      if (this.#adProvider?.adBreak) {\n        return;\n      }\n      super.currentTime = val;\n    }\n\n    get volume() {\n      if (this.#adProvider?.adBreak) {\n        return this.#adProvider?.volume ?? 0;\n      }\n      return super.volume;\n    }\n\n    set volume(val) {\n      if (this.#adProvider?.adBreak) {\n        if (this.#adProvider) {\n          this.#adProvider.volume = val;\n        }\n      }\n      super.volume = val;\n    }\n\n    get muted() {\n      if (this.#adProvider?.adBreak) {\n        return !this.#adProvider?.volume;\n      }\n      return super.muted;\n    }\n\n    set muted(val) {\n      if (this.#adProvider?.adBreak) {\n        if (this.#adProvider) {\n          this.#adProvider.volume = val ? 0 : this.volume;\n        }\n      }\n      super.muted = val;\n    }\n\n    get readyState() {\n      if (this.#adProvider?.adBreak) {\n        return 4;\n      }\n      return super.readyState;\n    }\n\n    async requestPictureInPicture(): Promise<PictureInPictureWindow> {\n      if (this.#adProvider?.adBreak) {\n        throw new Error('Cannot use PiP while ads are playing!');\n      }\n      return super.requestPictureInPicture();\n    }\n  }\n\n  return AdsVideo as unknown as Constructor<IAdsVideo> & T;\n}\n", "export class AdEvent extends Event {}\n\nexport const Events = {\n  // https://www.mux.com/docs/guides/mux-data-playback-events#ad-events\n  AD_REQUEST: 'adrequest',\n  AD_RESPONSE: 'adresponse',\n  AD_BREAK_START: 'adbreakstart',\n  AD_FIRST_QUARTILE: 'adfirstquartile',\n  AD_MIDPOINT: 'admidpoint',\n  AD_THIRD_QUARTILE: 'adthirdquartile',\n  AD_ENDED: 'adended',\n  AD_BREAK_END: 'adbreakend',\n  AD_ERROR: 'aderror',\n  AD_PLAY: 'adplay',\n  AD_PLAYING: 'adplaying',\n  AD_PAUSE: 'adpause',\n\n  // Useful ad specific events that are not part of the Mux Data spec\n  AD_IMPRESSION: 'adimpression',\n  AD_CLICK: 'adclick',\n  AD_SKIP: 'adskip',\n  AD_CLOSE: 'adclose',\n\n  // Use standard events if possible to be consistent with HTMLVideoElement\n  // https://www.w3.org/TR/html52/semantics-embedded-content.html#mediaevents\n  PLAY: 'play',\n  PLAYING: 'playing',\n  PAUSE: 'pause',\n  VOLUME_CHANGE: 'volumechange',\n  TIME_UPDATE: 'timeupdate',\n  DURATION_CHANGE: 'durationchange',\n  WAITING: 'waiting',\n} as const;\n", "import type { IAdsVideoClientAd } from './types.js';\n\nexport class GoogleImaClientAd implements IAdsVideoClientAd {\n  #ad: google.ima.Ad;\n  #manager: google.ima.AdsManager;\n\n  constructor(ad: google.ima.Ad, manager: google.ima.AdsManager) {\n    this.#ad = ad;\n    this.#manager = manager;\n  }\n\n  isLinear() {\n    return this.#ad.isLinear();\n  }\n\n  isCustomPlaybackUsed() {\n    return this.#manager.isCustomPlaybackUsed();\n  }\n}\n", "/* eslint @typescript-eslint/triple-slash-reference: \"off\" */\n/// <reference types=\"google_interactive_media_ads_types\" preserve=\"true\"/>\nimport { Events, AdEvent } from './events.js';\nimport { GoogleImaClientAd } from './google-ima-client-ad.js';\nimport { IAdsVideoClientProvider } from './types.js';\n\nexport type GoogleImaClientProviderConfig = {\n  adContainer: HTMLElement;\n  videoElement: HTMLVideoElement;\n  originalSize: DOMRect;\n};\n\nexport class GoogleImaClientProvider extends EventTarget implements IAdsVideoClientProvider {\n  static isSDKAvailable() {\n    if (!('google' in globalThis && 'ima' in globalThis['google'])) {\n      console.error('Missing google.ima SDK. Make sure you include it via a script tag.');\n      return false;\n    } else {\n      return true;\n    }\n  }\n\n  #adContainer: HTMLElement;\n  #videoElement: HTMLVideoElement;\n  #originalSize: DOMRect;\n  #resizeObserver?: ResizeObserver;\n  #adDisplayContainer: google.ima.AdDisplayContainer;\n  #adsLoader: google.ima.AdsLoader;\n  #adsManager?: google.ima.AdsManager;\n  #imaAd?: google.ima.Ad | null;\n  #ad?: GoogleImaClientAd;\n  #adProgressData?: google.ima.AdProgressData;\n  #initializedAdDisplayContainer = false;\n  #adPaused = false;\n  #videoPlayed = false;\n  #adBreak = false;\n  #lastCurrentTime = 0;\n\n  constructor(config: GoogleImaClientProviderConfig) {\n    super();\n\n    this.#adContainer = config.adContainer;\n    this.#videoElement = config.videoElement;\n    this.#originalSize = config.originalSize;\n\n    this.#videoPlayed = !this.#videoElement.paused;\n    this.#videoElement.addEventListener('play', this.#onVideoPlay);\n    this.#videoElement.addEventListener('seeking', this.#onVideoSeeking);\n    this.#videoElement.addEventListener('ended', this.#onVideoEnded);\n\n    this.#adDisplayContainer = new google.ima.AdDisplayContainer(this.#adContainer, this.#videoElement);\n    this.#adsLoader = new google.ima.AdsLoader(this.#adDisplayContainer);\n\n    this.#adsLoader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, this.#onAdError);\n    this.#adsLoader.addEventListener(\n      google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,\n      this.#onAdsManagerLoaded\n    );\n\n    this.#resizeObserver = new ResizeObserver((entries) => {\n      for (const entry of entries) {\n        const { width, height } = entry.contentRect;\n        if (width > 0 && height > 0) {\n          this.#resize(width, height);\n        }\n      }\n    });\n    this.#resizeObserver?.observe(this.#adContainer);\n  }\n\n  destroy() {\n    this.#videoElement.removeEventListener('play', this.#onVideoPlay);\n    this.#videoElement.removeEventListener('seeking', this.#onVideoSeeking);\n    this.#videoElement.removeEventListener('ended', this.#onVideoEnded);\n\n    this.#resizeObserver?.disconnect();\n    this.#resizeObserver = undefined;\n\n    this.#adsManager?.stop();\n    this.#adsManager?.destroy();\n    this.#adDisplayContainer?.destroy();\n    this.#adsLoader?.destroy();\n  }\n\n  unload() {\n    this.#adsManager?.stop();\n    // Wait for the next tick to ensure the ad is stopped and the content is resumed.\n    setTimeout(() => {\n      this.#adsManager?.destroy();\n    }, 0);\n  }\n\n  #resize(width: number, height: number) {\n    this.#originalSize = { ...this.#originalSize, width, height };\n    this.#adsManager?.resize(this.#originalSize.width, this.#originalSize.height);\n  }\n\n  #onVideoPlay = () => {\n    this.#videoPlayed = true;\n\n    if (this.#adBreak && !this.#adsManager?.isCustomPlaybackUsed()) {\n      console.warn('Video play prevented during ad break');\n      this.#videoElement.pause();\n    }\n  };\n\n  #onVideoSeeking = () => {\n    if (this.#adBreak && !this.#adsManager?.isCustomPlaybackUsed()) {\n      if (this.#videoElement.currentTime !== this.#lastCurrentTime) {\n        console.warn('Seek prevented during ad break');\n        this.#videoElement.currentTime = this.#lastCurrentTime;\n        this.#videoElement.dispatchEvent(new Event('timeupdate'));\n      }\n    }\n  };\n\n  #onVideoEnded = () => {\n    this.#adsLoader?.contentComplete();\n  };\n\n  #onAdError = (adErrorEvent: google.ima.AdErrorEvent) => {\n    console.error('Ad error', adErrorEvent.getError()?.getMessage());\n    this.dispatchEvent(new AdEvent(Events.AD_ERROR));\n    this.#onAdComplete();\n  };\n\n  #onAdComplete(_adEvent?: google.ima.AdEvent) {\n    this.dispatchEvent(new AdEvent(Events.AD_ENDED));\n  }\n\n  #onAdsManagerLoaded = async (loadedEvent: google.ima.AdsManagerLoadedEvent) => {\n    const adsRenderingSettings = new google.ima.AdsRenderingSettings();\n    this.#adsManager = loadedEvent.getAdsManager(this.#videoElement, adsRenderingSettings);\n\n    this.#adsManager?.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, this.#onAdError);\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.LOADED, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_RESPONSE));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED, (event: google.ima.AdEvent) => {\n      this.#imaAd = event.getAd();\n\n      if (!this.#imaAd || !this.#adsManager) {\n        console.warn('Google IMA ad is undefined');\n        return;\n      }\n\n      this.#adBreak = true;\n      this.#lastCurrentTime = this.#videoElement.currentTime || 0;\n      this.#ad = new GoogleImaClientAd(this.#imaAd, this.#adsManager);\n      this.dispatchEvent(new AdEvent(Events.AD_BREAK_START));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.CLICK, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_CLICK));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.IMPRESSION, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_IMPRESSION));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.SKIPPED, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_SKIP));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.USER_CLOSE, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_CLOSE));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_FIRST_QUARTILE));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.MIDPOINT, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_MIDPOINT));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_THIRD_QUARTILE));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.COMPLETE, (event: google.ima.AdEvent) => {\n      this.#onAdComplete(event);\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.SKIPPED, (event: google.ima.AdEvent) => {\n      this.#onAdComplete(event);\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED, () => {\n      this.#adBreak = false;\n      this.dispatchEvent(new AdEvent(Events.AD_BREAK_END));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.STARTED, () => {\n      this.dispatchEvent(new AdEvent(Events.AD_PLAYING));\n      this.dispatchEvent(new AdEvent(Events.PLAYING));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.PAUSED, () => {\n      this.#adPaused = true;\n      this.dispatchEvent(new AdEvent(Events.AD_PAUSE));\n      this.dispatchEvent(new AdEvent(Events.PAUSE));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.RESUMED, () => {\n      this.#adPaused = false;\n      this.dispatchEvent(new AdEvent(Events.AD_PLAY));\n      this.dispatchEvent(new AdEvent(Events.PLAY));\n      this.dispatchEvent(new AdEvent(Events.AD_PLAYING));\n      this.dispatchEvent(new AdEvent(Events.PLAYING));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.AD_BUFFERING, () => {\n      this.dispatchEvent(new AdEvent(Events.WAITING));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.AD_PROGRESS, (adProgressEvent: google.ima.AdEvent) => {\n      this.#adProgressData = adProgressEvent.getAdData() as google.ima.AdProgressData;\n      this.dispatchEvent(new AdEvent(Events.TIME_UPDATE));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.DURATION_CHANGE, () => {\n      this.dispatchEvent(new AdEvent(Events.DURATION_CHANGE));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.VOLUME_CHANGED, () => {\n      this.dispatchEvent(new AdEvent(Events.VOLUME_CHANGE));\n    });\n\n    this.#adsManager?.addEventListener(google.ima.AdEvent.Type.VOLUME_MUTED, () => {\n      this.dispatchEvent(new AdEvent(Events.VOLUME_CHANGE));\n    });\n\n    try {\n      if (this.#videoPlayed) {\n        this.#startAds();\n      } else {\n        this.#videoElement.addEventListener(\n          'play',\n          () => {\n            this.#videoPlayed = true;\n            this.#startAds();\n          },\n          { once: true }\n        );\n      }\n    } catch {\n      this.#onAdComplete();\n    }\n  };\n\n  #startAds() {\n    this.#adBreak = true;\n    this.#videoElement.pause();\n\n    // init() is included here because some ads (VMAP) start playing without the start() call.\n    this.#adsManager?.init(this.#originalSize.width, this.#originalSize.height);\n    this.#adsManager?.start();\n  }\n\n  get adsLoader() {\n    return this.#adsLoader;\n  }\n\n  get ad() {\n    return this.#ad;\n  }\n\n  get adBreak() {\n    return this.#adBreak;\n  }\n\n  get paused() {\n    return this.#adPaused;\n  }\n\n  get duration() {\n    return this.#adProgressData?.duration ?? this.#imaAd?.getDuration() ?? NaN;\n  }\n\n  get currentTime() {\n    return this.#adProgressData?.currentTime ?? 0;\n  }\n\n  get volume() {\n    return this.#adsManager?.getVolume() ?? 1;\n  }\n\n  set volume(val: number) {\n    this.#adsManager?.setVolume(val);\n  }\n\n  play() {\n    this.#adsManager?.resume();\n    // todo: resolve on playing event\n    return Promise.resolve();\n  }\n\n  pause() {\n    this.#adsManager?.pause();\n  }\n\n  /**\n   * Initializes the ad display container video elements for playback.\n   * You must call this method as a direct result of a user action,\n   * so that the browser can mark the video element as user initiated.\n   */\n  initializeAdDisplayContainer() {\n    if (this.#initializedAdDisplayContainer) return;\n    this.#initializedAdDisplayContainer = true;\n    this.#adDisplayContainer?.initialize();\n  }\n\n  requestAds(adTagUrl: string) {\n    // Destroy the current AdsManager to prevent any previously requested post-roll ads from playing.\n    if (this.#adsManager) {\n      this.#adsManager.destroy();\n    }\n\n    // Reuse the existing AdsLoader instance initialized on page load.\n    if (this.#adsLoader) {\n      // Reset the IMA SDK.\n      this.#adsLoader.contentComplete();\n    }\n\n    const adsRequest = new google.ima.AdsRequest();\n    adsRequest.adTagUrl = adTagUrl;\n    this.#adsLoader?.requestAds(adsRequest);\n    this.dispatchEvent(new AdEvent(Events.AD_REQUEST));\n  }\n}\n"], "mappings": "w3BAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,aAAAE,EAAA,kBAAAC,GAAA,eAAAC,EAAA,WAAAC,IAAA,eAAAC,GAAAN,ICAO,IAAMO,EAAN,cAAsB,KAAM,CAAC,EAEvBC,EAAS,CAEpB,WAAY,YACZ,YAAa,aACb,eAAgB,eAChB,kBAAmB,kBACnB,YAAa,aACb,kBAAmB,kBACnB,SAAU,UACV,aAAc,aACd,SAAU,UACV,QAAS,SACT,WAAY,YACZ,SAAU,UAGV,cAAe,eACf,SAAU,UACV,QAAS,SACT,SAAU,UAIV,KAAM,OACN,QAAS,UACT,MAAO,QACP,cAAe,eACf,YAAa,aACb,gBAAiB,iBACjB,QAAS,SACX,EChCA,IAAAC,EAAAC,EAEaC,EAAN,KAAqD,CAI1D,YAAYC,EAAmBC,EAAgC,CAH/DC,EAAA,KAAAL,GACAK,EAAA,KAAAJ,GAGEK,EAAA,KAAKN,EAAMG,GACXG,EAAA,KAAKL,EAAWG,EAClB,CAEA,UAAW,CACT,OAAOG,EAAA,KAAKP,GAAI,SAAS,CAC3B,CAEA,sBAAuB,CACrB,OAAOO,EAAA,KAAKN,GAAS,qBAAqB,CAC5C,CACF,EAfED,EAAA,YACAC,EAAA,YCJF,IAAAO,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAYaC,EAAN,cAAsC,WAA+C,CA0B1F,YAAYC,EAAuC,CAtCrD,IAAAC,EAuCI,MAAM,EA3BHC,EAAA,KAAAZ,GAULY,EAAA,KAAA3B,GACA2B,EAAA,KAAA1B,GACA0B,EAAA,KAAAzB,GACAyB,EAAA,KAAAxB,GACAwB,EAAA,KAAAvB,GACAuB,EAAA,KAAAtB,GACAsB,EAAA,KAAArB,GACAqB,EAAA,KAAApB,GACAoB,EAAA,KAAAnB,GACAmB,EAAA,KAAAlB,GACAkB,EAAA,KAAAjB,EAAiC,IACjCiB,EAAA,KAAAhB,EAAY,IACZgB,EAAA,KAAAf,EAAe,IACfe,EAAA,KAAAd,EAAW,IACXc,EAAA,KAAAb,EAAmB,GA6DnBa,EAAA,KAAAV,EAAe,IAAM,CAjGvB,IAAAS,EAkGIE,EAAA,KAAKhB,EAAe,IAEhBiB,EAAA,KAAKhB,IAAY,GAACa,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,0BACtC,QAAQ,KAAK,sCAAsC,EACnDG,EAAA,KAAK5B,GAAc,MAAM,EAE7B,GAEA0B,EAAA,KAAAT,EAAkB,IAAM,CA1G1B,IAAAQ,EA2GQG,EAAA,KAAKhB,IAAY,GAACa,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,yBAClCG,EAAA,KAAK5B,GAAc,cAAgB4B,EAAA,KAAKf,KAC1C,QAAQ,KAAK,gCAAgC,EAC7Ce,EAAA,KAAK5B,GAAc,YAAc4B,EAAA,KAAKf,GACtCe,EAAA,KAAK5B,GAAc,cAAc,IAAI,MAAM,YAAY,CAAC,EAG9D,GAEA0B,EAAA,KAAAR,EAAgB,IAAM,CApHxB,IAAAO,GAqHIA,EAAAG,EAAA,KAAKxB,KAAL,MAAAqB,EAAiB,iBACnB,GAEAC,EAAA,KAAAP,EAAcU,GAA0C,CAxH1D,IAAAJ,EAyHI,QAAQ,MAAM,YAAYA,EAAAI,EAAa,SAAS,IAAtB,YAAAJ,EAAyB,YAAY,EAC/D,KAAK,cAAc,IAAIK,EAAQC,EAAO,QAAQ,CAAC,EAC/CC,EAAA,KAAKlB,EAAAM,GAAL,UACF,GAMAM,EAAA,KAAAL,EAAsB,MAAOY,GAAkD,CAlIjF,IAAAR,EAAAS,EAAAC,EAAAC,EAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,GAAAC,EAAAC,GAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAmII,IAAMC,EAAuB,IAAI,OAAO,IAAI,qBAC5C3B,EAAA,KAAKtB,EAAc4B,EAAY,cAAcL,EAAA,KAAK5B,GAAesD,CAAoB,IAErF7B,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,iBAAiB,OAAO,IAAI,aAAa,KAAK,SAAUG,EAAA,KAAKT,KAE/Ee,EAAAN,EAAA,KAAKvB,KAAL,MAAA6B,EAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,OAAQ,IAAM,CACvE,KAAK,cAAc,IAAIJ,EAAQC,EAAO,WAAW,CAAC,CACpD,IAEAI,EAAAP,EAAA,KAAKvB,KAAL,MAAA8B,EAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,wBAA0BoB,GAA8B,CAGjH,GAFA5B,EAAA,KAAKrB,EAASiD,EAAM,MAAM,GAEtB,CAAC3B,EAAA,KAAKtB,IAAU,CAACsB,EAAA,KAAKvB,GAAa,CACrC,QAAQ,KAAK,4BAA4B,EACzC,MACF,CAEAsB,EAAA,KAAKf,EAAW,IAChBe,EAAA,KAAKd,EAAmBe,EAAA,KAAK5B,GAAc,aAAe,GAC1D2B,EAAA,KAAKpB,EAAM,IAAIiD,EAAkB5B,EAAA,KAAKtB,GAAQsB,EAAA,KAAKvB,EAAW,GAC9D,KAAK,cAAc,IAAIyB,EAAQC,EAAO,cAAc,CAAC,CACvD,IAEAK,EAAAR,EAAA,KAAKvB,KAAL,MAAA+B,EAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,MAAO,IAAM,CACtE,KAAK,cAAc,IAAIN,EAAQC,EAAO,QAAQ,CAAC,CACjD,IAEAM,GAAAT,EAAA,KAAKvB,KAAL,MAAAgC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,WAAY,IAAM,CAC3E,KAAK,cAAc,IAAIP,EAAQC,EAAO,aAAa,CAAC,CACtD,IAEAO,GAAAV,EAAA,KAAKvB,KAAL,MAAAiC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,QAAS,IAAM,CACxE,KAAK,cAAc,IAAIR,EAAQC,EAAO,OAAO,CAAC,CAChD,IAEAQ,GAAAX,EAAA,KAAKvB,KAAL,MAAAkC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,WAAY,IAAM,CAC3E,KAAK,cAAc,IAAIT,EAAQC,EAAO,QAAQ,CAAC,CACjD,IAEAS,GAAAZ,EAAA,KAAKvB,KAAL,MAAAmC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,eAAgB,IAAM,CAC/E,KAAK,cAAc,IAAIV,EAAQC,EAAO,iBAAiB,CAAC,CAC1D,IAEAU,GAAAb,EAAA,KAAKvB,KAAL,MAAAoC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,SAAU,IAAM,CACzE,KAAK,cAAc,IAAIX,EAAQC,EAAO,WAAW,CAAC,CACpD,IAEAW,GAAAd,EAAA,KAAKvB,KAAL,MAAAqC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,eAAgB,IAAM,CAC/E,KAAK,cAAc,IAAIZ,EAAQC,EAAO,iBAAiB,CAAC,CAC1D,IAEAY,GAAAf,EAAA,KAAKvB,KAAL,MAAAsC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,SAAWY,GAA8B,CAClGvB,EAAA,KAAKlB,EAAAM,GAAL,UAAmBmC,EACrB,IAEAX,GAAAhB,EAAA,KAAKvB,KAAL,MAAAuC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,QAAUW,GAA8B,CACjGvB,EAAA,KAAKlB,EAAAM,GAAL,UAAmBmC,EACrB,IAEAV,GAAAjB,EAAA,KAAKvB,KAAL,MAAAwC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,yBAA0B,IAAM,CACzFlB,EAAA,KAAKf,EAAW,IAChB,KAAK,cAAc,IAAIkB,EAAQC,EAAO,YAAY,CAAC,CACrD,IAEAe,GAAAlB,EAAA,KAAKvB,KAAL,MAAAyC,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,QAAS,IAAM,CACxE,KAAK,cAAc,IAAIhB,EAAQC,EAAO,UAAU,CAAC,EACjD,KAAK,cAAc,IAAID,EAAQC,EAAO,OAAO,CAAC,CAChD,IAEAgB,EAAAnB,EAAA,KAAKvB,KAAL,MAAA0C,EAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,OAAQ,IAAM,CACvEpB,EAAA,KAAKjB,EAAY,IACjB,KAAK,cAAc,IAAIoB,EAAQC,EAAO,QAAQ,CAAC,EAC/C,KAAK,cAAc,IAAID,EAAQC,EAAO,KAAK,CAAC,CAC9C,IAEAiB,GAAApB,EAAA,KAAKvB,KAAL,MAAA2C,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,QAAS,IAAM,CACxErB,EAAA,KAAKjB,EAAY,IACjB,KAAK,cAAc,IAAIoB,EAAQC,EAAO,OAAO,CAAC,EAC9C,KAAK,cAAc,IAAID,EAAQC,EAAO,IAAI,CAAC,EAC3C,KAAK,cAAc,IAAID,EAAQC,EAAO,UAAU,CAAC,EACjD,KAAK,cAAc,IAAID,EAAQC,EAAO,OAAO,CAAC,CAChD,IAEAkB,EAAArB,EAAA,KAAKvB,KAAL,MAAA4C,EAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,aAAc,IAAM,CAC7E,KAAK,cAAc,IAAInB,EAAQC,EAAO,OAAO,CAAC,CAChD,IAEAmB,EAAAtB,EAAA,KAAKvB,KAAL,MAAA6C,EAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,YAAcO,GAAwC,CAC/G9B,EAAA,KAAKnB,EAAkBiD,EAAgB,UAAU,GACjD,KAAK,cAAc,IAAI3B,EAAQC,EAAO,WAAW,CAAC,CACpD,IAEAoB,EAAAvB,EAAA,KAAKvB,KAAL,MAAA8C,EAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,gBAAiB,IAAM,CAChF,KAAK,cAAc,IAAIrB,EAAQC,EAAO,eAAe,CAAC,CACxD,IAEAqB,EAAAxB,EAAA,KAAKvB,KAAL,MAAA+C,EAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,eAAgB,IAAM,CAC/E,KAAK,cAAc,IAAItB,EAAQC,EAAO,aAAa,CAAC,CACtD,IAEAsB,GAAAzB,EAAA,KAAKvB,KAAL,MAAAgD,GAAkB,iBAAiB,OAAO,IAAI,QAAQ,KAAK,aAAc,IAAM,CAC7E,KAAK,cAAc,IAAIvB,EAAQC,EAAO,aAAa,CAAC,CACtD,GAEA,GAAI,CACEH,EAAA,KAAKjB,GACPqB,EAAA,KAAKlB,EAAAQ,GAAL,WAEAM,EAAA,KAAK5B,GAAc,iBACjB,OACA,IAAM,CACJ2B,EAAA,KAAKhB,EAAe,IACpBqB,EAAA,KAAKlB,EAAAQ,GAAL,UACF,EACA,CAAE,KAAM,EAAK,CACf,CAEJ,MAAQ,CACNU,EAAA,KAAKlB,EAAAM,GAAL,UACF,CACF,GAlNEO,EAAA,KAAK5B,EAAeyB,EAAO,aAC3BG,EAAA,KAAK3B,EAAgBwB,EAAO,cAC5BG,EAAA,KAAK1B,EAAgBuB,EAAO,cAE5BG,EAAA,KAAKhB,EAAe,CAACiB,EAAA,KAAK5B,GAAc,QACxC4B,EAAA,KAAK5B,GAAc,iBAAiB,OAAQ4B,EAAA,KAAKZ,EAAY,EAC7DY,EAAA,KAAK5B,GAAc,iBAAiB,UAAW4B,EAAA,KAAKX,EAAe,EACnEW,EAAA,KAAK5B,GAAc,iBAAiB,QAAS4B,EAAA,KAAKV,EAAa,EAE/DS,EAAA,KAAKxB,EAAsB,IAAI,OAAO,IAAI,mBAAmByB,EAAA,KAAK7B,GAAc6B,EAAA,KAAK5B,EAAa,GAClG2B,EAAA,KAAKvB,EAAa,IAAI,OAAO,IAAI,UAAUwB,EAAA,KAAKzB,EAAmB,GAEnEyB,EAAA,KAAKxB,GAAW,iBAAiB,OAAO,IAAI,aAAa,KAAK,SAAUwB,EAAA,KAAKT,EAAU,EACvFS,EAAA,KAAKxB,GAAW,iBACd,OAAO,IAAI,sBAAsB,KAAK,mBACtCwB,EAAA,KAAKP,EACP,EAEAM,EAAA,KAAKzB,EAAkB,IAAI,eAAgBwD,GAAY,CACrD,QAAWC,KAASD,EAAS,CAC3B,GAAM,CAAE,MAAAE,EAAO,OAAAC,CAAO,EAAIF,EAAM,YAC5BC,EAAQ,GAAKC,EAAS,GACxB7B,EAAA,KAAKlB,EAAAC,IAAL,UAAa6C,EAAOC,EAExB,CACF,CAAC,IACDpC,EAAAG,EAAA,KAAK1B,KAAL,MAAAuB,EAAsB,QAAQG,EAAA,KAAK7B,GACrC,CAvDA,OAAO,gBAAiB,CACtB,MAAM,WAAY,YAAc,QAAS,WAAW,OAI3C,IAHP,QAAQ,MAAM,oEAAoE,EAC3E,GAIX,CAkDA,SAAU,CAtEZ,IAAA0B,EAAAS,EAAAC,EAAAC,EAAAC,EAuEIT,EAAA,KAAK5B,GAAc,oBAAoB,OAAQ4B,EAAA,KAAKZ,EAAY,EAChEY,EAAA,KAAK5B,GAAc,oBAAoB,UAAW4B,EAAA,KAAKX,EAAe,EACtEW,EAAA,KAAK5B,GAAc,oBAAoB,QAAS4B,EAAA,KAAKV,EAAa,GAElEO,EAAAG,EAAA,KAAK1B,KAAL,MAAAuB,EAAsB,aACtBE,EAAA,KAAKzB,EAAkB,SAEvBgC,EAAAN,EAAA,KAAKvB,KAAL,MAAA6B,EAAkB,QAClBC,EAAAP,EAAA,KAAKvB,KAAL,MAAA8B,EAAkB,WAClBC,EAAAR,EAAA,KAAKzB,KAAL,MAAAiC,EAA0B,WAC1BC,EAAAT,EAAA,KAAKxB,KAAL,MAAAiC,EAAiB,SACnB,CAEA,QAAS,CApFX,IAAAZ,GAqFIA,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,OAElB,WAAW,IAAM,CAvFrB,IAAAA,GAwFMA,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,SACpB,EAAG,CAAC,CACN,CA4KA,IAAI,WAAY,CACd,OAAOG,EAAA,KAAKxB,EACd,CAEA,IAAI,IAAK,CACP,OAAOwB,EAAA,KAAKrB,EACd,CAEA,IAAI,SAAU,CACZ,OAAOqB,EAAA,KAAKhB,EACd,CAEA,IAAI,QAAS,CACX,OAAOgB,EAAA,KAAKlB,EACd,CAEA,IAAI,UAAW,CAtRjB,IAAAe,EAAAS,EAAAC,EAAAC,EAuRI,OAAOA,GAAAD,GAAAV,EAAAG,EAAA,KAAKpB,KAAL,YAAAiB,EAAsB,WAAtB,KAAAU,GAAkCD,EAAAN,EAAA,KAAKtB,KAAL,YAAA4B,EAAa,gBAA/C,KAAAE,EAAgE,GACzE,CAEA,IAAI,aAAc,CA1RpB,IAAAX,EAAAS,EA2RI,OAAOA,GAAAT,EAAAG,EAAA,KAAKpB,KAAL,YAAAiB,EAAsB,cAAtB,KAAAS,EAAqC,CAC9C,CAEA,IAAI,QAAS,CA9Rf,IAAAT,EAAAS,EA+RI,OAAOA,GAAAT,EAAAG,EAAA,KAAKvB,KAAL,YAAAoB,EAAkB,cAAlB,KAAAS,EAAiC,CAC1C,CAEA,IAAI,OAAO4B,EAAa,CAlS1B,IAAArC,GAmSIA,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,UAAUqC,EAC9B,CAEA,MAAO,CAtST,IAAArC,EAuSI,OAAAA,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,SAEX,QAAQ,QAAQ,CACzB,CAEA,OAAQ,CA5SV,IAAAA,GA6SIA,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,OACpB,CAOA,8BAA+B,CArTjC,IAAAA,EAsTQG,EAAA,KAAKnB,KACTkB,EAAA,KAAKlB,EAAiC,KACtCgB,EAAAG,EAAA,KAAKzB,KAAL,MAAAsB,EAA0B,aAC5B,CAEA,WAAWsC,EAAkB,CA3T/B,IAAAtC,EA6TQG,EAAA,KAAKvB,IACPuB,EAAA,KAAKvB,GAAY,QAAQ,EAIvBuB,EAAA,KAAKxB,IAEPwB,EAAA,KAAKxB,GAAW,gBAAgB,EAGlC,IAAM4D,EAAa,IAAI,OAAO,IAAI,WAClCA,EAAW,SAAWD,GACtBtC,EAAAG,EAAA,KAAKxB,KAAL,MAAAqB,EAAiB,WAAWuC,GAC5B,KAAK,cAAc,IAAIlC,EAAQC,EAAO,UAAU,CAAC,CACnD,CACF,EAtTEhC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YAxBKC,EAAA,YAgFLC,GAAO,SAAC6C,EAAeC,EAAgB,CA5FzC,IAAApC,EA6FIE,EAAA,KAAK1B,EAAgB,CAAE,GAAG2B,EAAA,KAAK3B,GAAe,MAAA2D,EAAO,OAAAC,CAAO,IAC5DpC,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,OAAOG,EAAA,KAAK3B,GAAc,MAAO2B,EAAA,KAAK3B,GAAc,OACxE,EAEAe,EAAA,YASAC,EAAA,YAUAC,EAAA,YAIAC,EAAA,YAMAC,EAAa,SAAC6C,EAA+B,CAC3C,KAAK,cAAc,IAAInC,EAAQC,EAAO,QAAQ,CAAC,CACjD,EAEAV,EAAA,YA2HAC,EAAS,UAAG,CA7Pd,IAAAG,EAAAS,EA8PIP,EAAA,KAAKf,EAAW,IAChBgB,EAAA,KAAK5B,GAAc,MAAM,GAGzByB,EAAAG,EAAA,KAAKvB,KAAL,MAAAoB,EAAkB,KAAKG,EAAA,KAAK3B,GAAc,MAAO2B,EAAA,KAAK3B,GAAc,SACpEiC,EAAAN,EAAA,KAAKvB,KAAL,MAAA6B,EAAkB,OACpB,EH5PK,IAAMgC,EAAa,CACxB,WAAY,aACZ,iBAAkB,kBACpB,EAMO,SAASC,GAA4CC,EAA2C,CAjBvG,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAAAC,GAAAC,EAAAC,GAAAC,GAAAC,EAAAC,GAAAC,GAAAC,EAAAC,GAAAC,GAkBE,IAAMC,EAAN,MAAMA,UAAiBjB,CAAgC,CAAvD,kCAAAkB,EAAA,KAAAb,GA0DEa,EAAA,KAAAjB,EAAuB,IACvBiB,EAAA,KAAAhB,GACAgB,EAAA,KAAAf,GACAe,EAAA,KAAAd,GA5DA,WAAW,oBAAqB,CAC9B,MAAO,CAAC,GAAG,MAAM,mBAAoB,MAAON,EAAW,UAAU,CACnE,CAEA,WAAW,QAAS,CAvBxB,IAAAqB,EAyBM,MAAO,CAAC,GAAG,IAAI,IAAI,CAAC,IAAIA,EAAA,MAAM,SAAN,KAAAA,EAAgB,CAAC,EAAI,GAAG,OAAO,OAAOC,CAAQ,CAAC,CAAC,CAAC,CAC3E,CAuDA,mBAAoB,CAGlB,GAFA,MAAM,kBAAkB,EAEpB,CAACC,EAAwB,eAAe,EAAG,CAC7C,QAAQ,MAAM,oEAAoE,EAE7E,KAAK,gBACRC,EAAA,KAAKjB,EAAAO,IAAL,WAEF,MACF,CAEA,GAAI,CAACW,EAAA,KAAKpB,GAAa,CACrBqB,EAAA,KAAKrB,EAAc,IAAIkB,EAAwB,CAC7C,YAAaE,EAAA,KAAKlB,EAAAM,GAClB,aAAc,KAAK,SACnB,aAAc,KAAK,sBAAsB,CAC3C,CAAC,GAED,QAAWc,KAAS,OAAO,OAAOL,CAAQ,EACxCG,EAAA,KAAKpB,GAAY,iBAAiBsB,EAAO,IAAI,CAEjD,CACF,CAEA,yBAAyBC,EAAkBC,EAA0BC,EAAgC,CACnG,MAAM,yBAAyBF,EAAUC,EAAUC,CAAQ,EAEvDF,IAAa,OAASE,IAAaD,IAKrCH,EAAA,KAAKtB,EAAe,QACpBsB,EAAA,KAAKpB,EAAe,QACpBoB,EAAA,KAAKvB,EAAuB,KAG1ByB,IAAa5B,EAAW,YAC1BwB,EAAA,KAAKjB,EAAAG,GAAL,UAEJ,CAMA,YAAYiB,EAA8B,CAhI9C,IAAAN,EAiIM,GAAIM,aAAiBI,EAAS,CAC5BP,EAAA,KAAKjB,EAAAQ,IAAL,UAAoBY,GACpB,MACF,EAIIN,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,UAIlBM,EAAM,OAAS,iBACjBH,EAAA,KAAKjB,EAAAC,IAAL,WACSmB,EAAM,OAAS,QACxBH,EAAA,KAAKjB,EAAAE,IAAL,WAGF,MAAM,YAAYkB,CAAK,EACzB,CAqHA,MAAO,CAxQX,IAAAN,EAyQM,MAAI,CAACE,EAAwB,eAAe,GAAK,CAAC,KAAK,eAC9C,QAAQ,OAAO,IAAI,MAAM,8CAA8C,CAAC,GAE7EF,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,QACbI,EAAA,KAAKpB,GAAY,KAAK,EAExB,MAAM,KAAK,CACpB,CAEA,OAAQ,CAlRZ,IAAAgB,EAAAW,GAmRUX,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,WACpBW,EAAAP,EAAA,KAAKpB,KAAL,MAAA2B,EAAkB,SAEpB,MAAM,MAAM,CACd,CAEA,IAAI,IAAK,CAzRb,IAAAX,EA0RM,OAAOA,EAAAI,EAAA,KAAKpB,KAAL,YAAAgB,EAAkB,EAC3B,CAEA,IAAI,WAAY,CA7RpB,IAAAA,EA8RM,OAAKI,EAAA,KAAKpB,IACR,QAAQ,KAAK,6BAA6B,GAErCgB,EAAAI,EAAA,KAAKpB,KAAL,YAAAgB,EAAkB,SAC3B,CAEA,IAAI,UAAW,CApSnB,IAAAA,EAqSM,OAAOA,EAAA,KAAK,aAAarB,EAAW,UAAU,IAAvC,KAAAqB,EAA4C,MACrD,CAEA,IAAI,SAASY,EAAO,CACdA,GAAS,KAAK,WAEdA,GAAS,KACX,KAAK,gBAAgBjC,EAAW,UAAU,EAE1C,KAAK,aAAaA,EAAW,WAAYiC,CAAK,EAElD,CAEA,IAAI,gBAAiB,CACnB,OAAO,KAAK,aAAajC,EAAW,gBAAgB,CACtD,CAEA,IAAI,eAAekC,EAAK,CACtB,KAAK,gBAAgBlC,EAAW,iBAAkB,EAAQkC,CAAI,CAChE,CAEA,IAAI,QAAS,CA1TjB,IAAAb,EAAAW,EAAAG,EA2TM,OAAId,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,SACbc,GAAAH,EAAAP,EAAA,KAAKpB,KAAL,YAAA2B,EAAkB,SAAlB,KAAAG,EAA4B,GAE9B,MAAM,MACf,CAEA,IAAI,UAAW,CAjUnB,IAAAd,EAAAW,EAAAG,EAkUM,OAAId,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,SACbc,GAAAH,EAAAP,EAAA,KAAKpB,KAAL,YAAA2B,EAAkB,WAAlB,KAAAG,EAA8B,EAEhC,MAAM,QACf,CAEA,IAAI,aAAc,CAxUtB,IAAAd,EAAAW,EAAAG,EAyUM,OAAId,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,SACbc,GAAAH,EAAAP,EAAA,KAAKpB,KAAL,YAAA2B,EAAkB,cAAlB,KAAAG,EAAiC,EAEnC,MAAM,WACf,CAEA,IAAI,YAAYD,EAAK,CA/UzB,IAAAb,GAgVUA,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,UAGtB,MAAM,YAAca,EACtB,CAEA,IAAI,QAAS,CAtVjB,IAAAb,EAAAW,EAAAG,EAuVM,OAAId,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,SACbc,GAAAH,EAAAP,EAAA,KAAKpB,KAAL,YAAA2B,EAAkB,SAAlB,KAAAG,EAA4B,EAE9B,MAAM,MACf,CAEA,IAAI,OAAOD,EAAK,CA7VpB,IAAAb,GA8VUA,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,SAChBI,EAAA,KAAKpB,KACPoB,EAAA,KAAKpB,GAAY,OAAS6B,GAG9B,MAAM,OAASA,CACjB,CAEA,IAAI,OAAQ,CAtWhB,IAAAb,EAAAW,EAuWM,OAAIX,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,QACb,GAACW,EAAAP,EAAA,KAAKpB,KAAL,MAAA2B,EAAkB,QAErB,MAAM,KACf,CAEA,IAAI,MAAME,EAAK,CA7WnB,IAAAb,GA8WUA,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,SAChBI,EAAA,KAAKpB,KACPoB,EAAA,KAAKpB,GAAY,OAAS6B,EAAM,EAAI,KAAK,QAG7C,MAAM,MAAQA,CAChB,CAEA,IAAI,YAAa,CAtXrB,IAAAb,EAuXM,OAAIA,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,QACb,EAEF,MAAM,UACf,CAEA,MAAM,yBAA2D,CA7XrE,IAAAA,EA8XM,IAAIA,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,QACpB,MAAM,IAAI,MAAM,uCAAuC,EAEzD,OAAO,MAAM,wBAAwB,CACvC,CACF,EAvTElB,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YA7DFC,EAAA,YAmIEC,GAAiB,UAAG,CAClBkB,EAAA,KAAKvB,EAAuB,IAE5BqB,EAAA,KAAKjB,EAAAG,GAAL,UACF,EAEAD,GAAO,UAAG,CA3Jd,IAAAY,EA6JMG,EAAA,KAAKjB,EAAAG,GAAL,YACAW,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,8BACpB,EAEAX,EAAS,UAAG,CACN,KAAK,SACPc,EAAA,KAAKjB,EAAAI,IAAL,WAEAa,EAAA,KAAKjB,EAAAK,IAAL,UAEJ,EAEMD,GAAW,gBAAG,CAzKxB,IAAAU,EA2KU,CAAC,KAAK,UAAY,CAAC,KAAK,aAGvBI,EAAA,KAAKtB,IAEN,KAAK,WAAasB,EAAA,KAAKrB,KACzBsB,EAAA,KAAKtB,EAAe,KAAK,WACzBiB,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,WAAW,KAAK,UAEtC,EAEAT,GAAW,UAAG,CAtLlB,IAAAS,GAuLMA,EAAAI,EAAA,KAAKpB,KAAL,MAAAgB,EAAkB,SAClBK,EAAA,KAAKtB,EAAe,OACtB,EAEIS,EAAY,UAAG,CA3LvB,IAAAQ,EA4LM,OAAOA,EAAA,KAAK,aAAL,YAAAA,EAAiB,eAAe,eACzC,EAEAP,GAAqB,UAAG,CA/L5B,IAAAO,EAAAW,GAgMUX,EAAA,KAAK,aAAL,MAAAA,EAAiB,cAAc,8BAInCW,EAAAP,EAAA,KAAKlB,EAAAM,KAAL,MAAAmB,EAAmB,mBACjB,WACW;AAAA;AAAA;AAAA;AAAA;AAAA,UAOf,EAEAjB,GAAc,SAACY,EAAwB,CACrC,GAAIA,EAAM,OAASL,EAAS,eAAgB,CAC1CE,EAAA,KAAKjB,EAAAU,IAAL,WACAO,EAAA,KAAKjB,EAAAS,GAAL,UAAsBM,EAAS,iBAC/BE,EAAA,KAAKjB,EAAAS,GAAL,UAAsBW,EAAM,MAC5B,MACF,CAEA,GAAIA,EAAM,OAASL,EAAS,aAAc,CACxCE,EAAA,KAAKjB,EAAAW,IAAL,WACAM,EAAA,KAAKjB,EAAAS,GAAL,UAAsBM,EAAS,iBAC/BE,EAAA,KAAKjB,EAAAS,GAAL,UAAsBW,EAAM,MAC5B,MACF,CAEAH,EAAA,KAAKjB,EAAAS,GAAL,UAAsBW,EAAM,KAC9B,EAEAX,EAAgB,SAACoB,EAAmB,CAElC,KAAK,cAAc,IAAIL,EAAQK,EAAW,CAAE,SAAU,EAAK,CAAC,CAAC,CAC/D,EAEAnB,GAAe,UAAG,CAtOtB,IAAAI,EAAAW,GAuOMX,EAAAI,EAAA,KAAKlB,EAAAM,KAAL,MAAAQ,EAAmB,UAAU,OAAO,WAAY,KAE3CW,EAAA,KAAK,KAAL,MAAAA,EAAS,aAIdK,EAAAlB,EAAA,eAAM,cAAN,IAAY,EAEZO,EAAA,KAAKpB,EAAe,CAClB,YAAa+B,EAAAlB,EAAA,eAAM,cACrB,GACF,EAEAD,GAAa,UAAG,CApPpB,IAAAG,EAAAW,GAqPMX,EAAAI,EAAA,KAAKlB,EAAAM,KAAL,MAAAQ,EAAmB,UAAU,OAAO,WAAY,KAE5CW,EAAAP,EAAA,KAAKnB,KAAL,MAAA0B,EAAmB,cACrB,KAAK,YAAcP,EAAA,KAAKnB,GAAa,aAGvCoB,EAAA,KAAKpB,EAAe,QAEpB,WAAW,IAAM,CACf,GAAI,CAAC+B,EAAAlB,EAAA,eAAM,SACT,GAAI,CACF,KAAK,KAAK,CACZ,MAAQ,CAER,CAEJ,EAAG,GAAG,CACR,EApPIA,EAUG,gBAAmBmB,GAEtBpC,EAAW,gBAAgBoC,CAAK,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAbf,IAAMC,EAANpB,EAmXA,OAAOoB,CACT", "names": ["index_exports", "__export", "AdEvent", "AdsVideoMixin", "Attributes", "Events", "__toCommonJS", "AdEvent", "Events", "_ad", "_manager", "GoogleImaClientAd", "ad", "manager", "__privateAdd", "__privateSet", "__privateGet", "_adContainer", "_videoElement", "_originalSize", "_resizeObserver", "_adDisplayContainer", "_adsLoader", "_adsManager", "_imaAd", "_ad", "_adProgressData", "_initializedAdDisplayContainer", "_adPaused", "_videoPlayed", "_adBreak", "_lastCurrentTime", "_GoogleImaClientProvider_instances", "resize_fn", "_onVideoPlay", "_onVideoSeeking", "_onVideoEnded", "_onAdError", "onAdComplete_fn", "_onAdsManagerLoaded", "startAds_fn", "GoogleImaClientProvider", "config", "_a", "__privateAdd", "__privateSet", "__privateGet", "adErrorEvent", "AdEvent", "Events", "__privateMethod", "loadedEvent", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "_k", "_l", "_m", "_n", "_o", "_p", "_q", "_r", "_s", "_t", "_u", "adsRenderingSettings", "event", "GoogleImaClientAd", "adProgressEvent", "entries", "entry", "width", "height", "val", "adTagUrl", "adsRequest", "_adEvent", "Attributes", "AdsVideoMixin", "superclass", "_videoMetadataLoaded", "_oldAdTagUrl", "_adProvider", "_videoBackup", "_AdsVideo_instances", "onLoadedMetadata_fn", "onPlay_fn", "resetAds_fn", "requestAds_fn", "destroyAds_fn", "adContainer_get", "showAdBlockedMessage_fn", "handleAdEvent_fn", "dispatchAdEvent_fn", "onAdBreakStart_fn", "onAdBreakEnd_fn", "_AdsVideo", "__privateAdd", "_a", "Events", "GoogleImaClientProvider", "__privateMethod", "__privateGet", "__privateSet", "event", "attrName", "oldValue", "newValue", "AdEvent", "_b", "value", "val", "_c", "eventType", "__superGet", "attrs", "AdsVideo"]}