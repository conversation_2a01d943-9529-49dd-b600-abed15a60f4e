import { NextResponse } from 'next/server';
import { botDetector } from '@/lib/advancedSecurity';

/**
 * API endpoint do raportowania podejrzanych aktywności
 */

// Rate limiting dla security reports
const securityReportLimits = new Map();
const MAX_REPORTS_PER_IP = 10;
const REPORT_WINDOW = 5 * 60 * 1000; // 5 minut

function getClientIP(request) {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return 'unknown';
}

function checkSecurityReportLimit(ip) {
  const now = Date.now();
  const key = `security_report:${ip}`;
  
  if (!securityReportLimits.has(key)) {
    securityReportLimits.set(key, []);
  }
  
  const reports = securityReportLimits.get(key);
  const recentReports = reports.filter(time => now - time < REPORT_WINDOW);
  
  if (recentReports.length >= MAX_REPORTS_PER_IP) {
    return false;
  }
  
  recentReports.push(now);
  securityReportLimits.set(key, recentReports);
  return true;
}

export async function POST(request) {
  try {
    const clientIP = getClientIP(request);
    
    // Rate limiting dla security reports
    if (!checkSecurityReportLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Too many security reports from this IP' },
        { status: 429 }
      );
    }

    const {
      activities,
      fingerprint,
      userAgent,
      formValidation,
      behaviorData
    } = await request.json();

    // Walidacja danych
    if (!activities || !Array.isArray(activities)) {
      return NextResponse.json(
        { error: 'Invalid activities data' },
        { status: 400 }
      );
    }

    // Analiza ryzyka przez bot detector
    const riskAnalysis = botDetector.analyzeBehavior(clientIP, userAgent, {
      path: '/api/security/report',
      method: 'POST',
      headers: Object.fromEntries(request.headers.entries()),
      jsFingerprint: fingerprint,
      formFillTime: formValidation?.fillTime,
      mouseData: behaviorData?.mouseMovements
    });

    // Przygotuj dane do logowania
    const securityReport = {
      timestamp: new Date().toISOString(),
      clientIP,
      userAgent,
      fingerprint,
      activities,
      riskAnalysis,
      formValidation,
      behaviorData: {
        mouseMovements: behaviorData?.mouseMovements?.length || 0,
        keystrokes: behaviorData?.keystrokes?.length || 0,
        patterns: behaviorData?.keystrokePattern?.slice(0, 5) // Tylko pierwsze 5 dla prywatności
      }
    };

    // Loguj do konsoli (w produkcji wysłać do systemu monitoringu)
    console.log('🚨 Security Report:', JSON.stringify(securityReport, null, 2));

    // Jeśli wysokie ryzyko, dodatkowe działania
    if (riskAnalysis.riskScore > 80) {
      console.warn(`🔴 HIGH RISK DETECTED: IP ${clientIP}, Risk Score: ${riskAnalysis.riskScore}`);
      
      // W produkcji: wyślij alert do administratorów
      // await sendSecurityAlert(securityReport);
    }

    // Sprawdź czy IP powinno być zablokowane
    const shouldBlock = riskAnalysis.recommendation === 'BLOCK';
    
    if (shouldBlock) {
      // W produkcji: dodaj IP do blacklisty
      console.error(`🚫 BLOCKING IP: ${clientIP} due to high risk score`);
    }

    return NextResponse.json({
      success: true,
      message: 'Security report received',
      riskScore: riskAnalysis.riskScore,
      recommendation: riskAnalysis.recommendation,
      reportId: `SR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    });

  } catch (error) {
    console.error('Security report error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to process security report',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Security reporting endpoint',
    timestamp: new Date().toISOString(),
    limits: {
      maxReportsPerIP: MAX_REPORTS_PER_IP,
      windowMinutes: REPORT_WINDOW / 1000 / 60
    }
  });
}
